name: 'Create deployment'
description: 'Create deployment and set in_progress status on GitHub'
inputs:
  ref:
    description: 'Ref to use on GitHub Actions'
    required: true
  token:
    description: 'GitHub token'
    required: true
  sha:
    description: 'SHA1 hash of commit'
    required: true

outputs:
  deployid:
    description: 'Deployment ID'

runs:
  using: 'docker'
  image: 'Dockerfile'
  args:
    - ${{ inputs.ref }}
    - ${{ inputs.token }}
    - ${{ inputs.sha }}
