<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Seasons\Models\Season;

trait PaymentSettingsFeatures
{
    /**
     * @When I view the payments page
     */
    public function iViewThePaymentsPage()
    {
        $this->route('GET', 'payment.general');

        $this->assertResponseOk();
    }

    /**
     * @When I turn payments on
     */
    public function iTurnPaymentsOn()
    {
        $this->route('PUT', 'payment.toggle', ['paid-entries' => 1]);

        $this->assertRedirectedToRoute('payment.general');
    }

    /**
     * @Then I should be able to save my payments settings
     */
    public function iShouldBeAbleToSaveMyPaymentsSettings()
    {
        $data = [
            'setting' => [
                'entry-payment' => 'submit',
                'free-cart-billing-details' => 'optional',
                'legal-name' => '',
                'organisation-address' => md5(random_bytes(100)),
                'tax-number' => '',
                'organisation-logo' => '',
                'accept-credit-cards' => '0',
                'accept-invoice' => '0',
                'invoice-instructions' => '',
                'accept-paypal' => '0',
                'paypal-express-username' => '',
                'paypal-express-password' => '',
                'paypal-express-signature' => '',
                'payment-identifier' => 'AWARD FORCE',
                'levy-processing-fee' => '0',
                'alipay-processing-fee' => '',
                'american_express-processing-fee' => '',
                'diners_club-processing-fee' => '',
                'discover-processing-fee' => '',
                'ideal-processing-fee' => '',
                'invoice-processing-fee' => '',
                'jcb-processing-fee' => '',
                'maestro-processing-fee' => '',
                'mastercard-processing-fee' => '',
                'paypal_express-processing-fee' => '',
                'visa-processing-fee' => '',
                'payment-gateway' => '',
                'payment-test-mode' => '0',
                'authorize-net-api-login-id' => '',
                'authorize-net-transaction-key' => '',
                'bluepay-account-id' => '',
                'bluepay-secret-key' => '',
                'bpoint-api-username' => '',
                'bpoint-api-password' => '',
                'bpoint-merchant-number' => '',
                'cybersource-merchant-id' => '',
                'cybersource-transaction-key' => '',
                'eway-api-key' => '',
                'eway-password' => '',
                'eway-redirect-api-key' => '',
                'eway-redirect-password' => '',
                'mercanet-merchant-id' => '',
                'mercanet-secret-key' => '',
                'nab-transact-merchant-id' => '',
                'nab-transact-password' => '',
                'netbanx-account-number' => '',
                'netbanx-store-id' => '',
                'netbanx-store-password' => '',
                'sagepay-vendor' => '',
                'sagepay-referrer-id' => '',
                'securepay-merchant-id' => '',
                'securepay-transaction-password' => '',
                'stripe-api-key' => '',
                'payflow-partner' => '',
                'payflow-vendor' => '',
                'payflow-username' => '',
                'payflow-password' => '',
                'realex-merchant-id' => '',
                'realex-account' => '',
                'realex-shared-secret' => '',
                'westpac-payway-username' => '',
                'westpac-payway-password' => '',
                'westpac-payway-merchant-id' => '',
                'worldpay-installation-id' => '',
                'accept-american-express' => '0',
                'accept-diners-club' => '0',
                'accept-discover' => '0',
                'accept-jcb' => '0',
                'accept-maestro' => '0',
                'accept-mastercard' => '0',
                'accept-visa' => '0',
                'activate-volume-entry-pricing' => '0',
                'payment-volume-applies-to' => 'all',
                'activate-related-entries-pricing' => '0',
                'related-entries-field' => '',
                'related-entries-action' => 'charge_first',
                'next-invoice-number' => '1',
            ],
        ];

        $this->route('PUT', 'payment.general.update', [], $this->withInput($data));
        $this->assertRedirectedToRoute('payment.general');

        $this->route('GET', 'payment.general');
        $this->assertResponseOk();
        $this->assertResponseContains($data['setting']['organisation-address']);
    }

    /**
     * @When I want new tier
     */
    public function iWantNewTier()
    {
        $this->route('GET', 'tier.new');
        if ($this->response->status() !== 200) {
            dd($this->response->getContent());
        }
        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to create a tier
     */
    public function iShouldBeAbleToCreateATier()
    {
        $season = $this->muffin(Season::class);
        $price = $this->muffin(Price::class);

        $entries = time();

        $input = $this->withInput([
            'amounts' => ['EUR' => 1, 'USD' => 2],
            'entries' => $entries,
            'seasonId' => $season->id,
            'type' => 'amount',
            'priceId' => $price->id,
        ]);

        $this->route('POST', 'tier.create', [], $input);
        $this->assertRedirectedToRoute('tier');
    }

    /**
     * @Then I should not be able to set negative amount for tier
     */
    public function iShouldBeAbleToSetNegativeAmountForTier()
    {
        $season = $this->muffin(Season::class);
        $price = $this->muffin(Price::class);

        $entries = time();

        $input = $this->withInput([
            'amounts' => ['EUR' => -5, 'USD' => 2, 'AUD' => 'xxx'],
            'entries' => $entries,
            'seasonId' => $season->id,
            'type' => 'amount',
            'priceId' => $price->id,
        ]);

        $this->route('POST', 'tier.create', [], $input);

        $this->assertViewErrorsMatch([
            'amounts.EUR' => trans('payments.currency-table.validation.amount.min', ['currency' => 'EUR', 'min' => 0]),
            'amounts.AUD' => trans('payments.currency-table.validation.amount.numeric', ['currency' => 'AUD']),
        ], false);
    }
}
