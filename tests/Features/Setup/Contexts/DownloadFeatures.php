<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Downloads\Models\Download;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;
use AwardForce\Modules\Identity\Users\Models\User;

trait DownloadFeatures
{
    /**
     * @Given /^a number of downloads exist$/
     */
    public function aNumberOfDownloadsExist()
    {
        $user1 = $this->muffin(User::class);
        $this->downloads = collect([
            $this->muffin(Download::class, ['user_id' => $user1->id]),
            $this->muffin(Download::class, ['user_id' => $this->user->id]),
        ]);
    }

    /**
     * @Given /^I can view downloads$/
     */
    public function iCanViewDownloads()
    {
        $this->role->permissions()->save(Permission::add($this->role, 'Download', 'view', new Mode('allow')));
    }

    /**
     * @Then /^I should be able to view only my own downloads$/
     */
    public function iShouldBeAbleToViewOnlyMyOwnDownloads()
    {
        $this->route('GET', 'download.list');
        $this->assertResponseOk();

        $this->assertResponseContains((string) $this->downloads->filter(fn(Download $download) => $download->userId === $this->user->id)->first()->url);
        $this->assertResponseNotContains((string) $this->downloads->filter(fn(Download $download) => $download->userId !== $this->user->id)->first()->url);
    }

    /**
     * @Then /^I should not be able to view the downloads list$/
     */
    public function iShouldNotBeAbleToViewTheDownloadsList()
    {
        $this->route('GET', 'download.list');
        $this->assertResponseStatus(403);
    }

    /**
     * @Then /^I should be able to view the downloads list$/
     */
    public function iShouldeAbleToViewTheDownloadsList()
    {
        $this->route('GET', 'download.list');
        $this->assertResponseStatus(200);
    }
}
