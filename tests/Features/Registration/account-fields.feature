Feature: Registration account fields
  In order to provide the required user profile information
  I need to be prompted when I login to fill out required fields

  Scenario:
    Given I have a valid "Entrant" account
    And there is a required "Users" field
    When I login to my account
    Then I should be prompted to fill in account fields
    And the view should contain the fields required message

  <PERSON><PERSON><PERSON>:
    Given registration is open
    And there is an optional "Users" field
    When I register to an account
    And I am currently logged in as an "Entrant"
    Then I should be prompted to fill in account fields

  <PERSON><PERSON><PERSON>:
    Given registration is open
    When I register to an account
    And I am currently logged in as an "Entrant"
    Then I should be prompted to fill in account fields
    And the view should not contain the fields required message

  <PERSON><PERSON><PERSON>:
    Given registration is open
    When I register to an account
    And I am currently logged in as an "Entrant"
    And there is a required step one users field
    And there is a required step two users field
    Then I should be prompted to fill in account fields
    And the field from step one should not be present
    And the field from step two should be present
