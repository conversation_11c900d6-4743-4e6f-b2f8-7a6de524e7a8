<?php

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Faker\Factory as Faker;
use League\FactoryMuffin\FactoryMuffin;

/** @var FactoryMuffin $this */
$this->define(Category::class)
    ->setDefinitions([
        'account_id' => function () {
            return current_account_id();
        },
        'season_id' => function () {
            return current_account()->activeSeason()->id;
        },
        'form_id' => function () {
            return FormSelector::getId();
        },
        'active' => true,
    ])
    ->setCallback(function (Category $category) {
        // Translations
        $category->saveTranslation(
            default_language_code(),
            'name',
            Faker::create()->word(),
            $category->accountId
        );
        $category->saveTranslation(
            default_language_code(),
            'description',
            Faker::create()->catchPhrase(),
            $category->accountId
        );
        $category->saveTranslation(
            default_language_code(),
            'shortcode',
            strtoupper(str_random(8)),
            $category->accountId
        );

        // Default Chapter
        $category->chapters()->attach($this->create(Chapter::class, [
            'account_id' => $category->accountId,
            'season_id' => $category->seasonId,
        ]));
    });
