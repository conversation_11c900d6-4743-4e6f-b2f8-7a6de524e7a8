<?php

namespace Tests\Stubs;

use AwardForce\Modules\Accounts\Models\Account;
use Illuminate\Contracts\Queue\ShouldQueue;
use Platform\Bus\Pipeline\Accountable;

class QueuedCommandCommand implements Accountable, ShouldQueue
{
    public $account;

    public function __construct($account)
    {
        $this->account = $account;
    }

    /**
     * Returns the account that this command is for.
     *
     * @return Account
     */
    public function account()
    {
        return $this->account;
    }
}
