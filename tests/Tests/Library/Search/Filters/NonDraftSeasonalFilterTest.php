<?php

namespace Tests\Library\Search\Filters;

use AwardForce\Library\Search\Filters\NonDraftSeasonalFilter;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Support\Collection;
use Mockery as m;
use Tests\UnitTestCase;

final class NonDraftSeasonalFilterTest extends UnitTestCase
{
    /**
     * @var NonDraftSeasonalFilter
     */
    protected $filter;

    /**
     * @var SeasonRepository
     */
    protected $seasons;

    protected $query;

    public function init()
    {
        $this->query = m::spy('query');
        $this->query->shouldReceive('with')->once();
        $this->query->shouldReceive('getModel->getTable')->once();

        $this->seasons = m::mock(SeasonRepository::class);
        $this->filter = new NonDraftSeasonalFilter($this->seasons);
    }

    public function testFiltersBySelectedSeason(): void
    {
        $active = $this->getSeason(Season::STATUS_ACTIVE, 1);

        SeasonFilter::shouldReceive('viewingAll')->once()->andReturn(false);
        SeasonFilter::shouldReceive('get')->once()->andReturn($active);

        $this->filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with(m::any(), 1)->once();
    }

    public function testReplacesDraftWithActiveSeason(): void
    {
        $active = $this->getSeason(Season::STATUS_ACTIVE, 1);
        $draft = $this->getSeason(Season::STATUS_DRAFT, 2);

        SeasonFilter::shouldReceive('viewingAll')->once()->andReturn(false);
        SeasonFilter::shouldReceive('get')->once()->andReturn($draft);

        CurrentAccount::shouldReceive('activeSeason')->once()->andReturn($active);

        $this->filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with(m::any(), 1)->once();
    }

    public function testIgnoresDraftSeasonsWhenViewingAll(): void
    {
        $archived = $this->getSeason(Season::STATUS_ARCHIVED, 1);
        $active = $this->getSeason(Season::STATUS_ACTIVE, 2);

        SeasonFilter::shouldReceive('viewingAll')->once()->andReturn(true);

        $this->seasons->shouldReceive('getNonDraft')->once()->andReturn(new Collection([$archived, $active]));

        $this->filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('whereIn')->with(m::any(), [1, 2])->once();
    }

    private function getSeason($status, $seasonId)
    {
        $season = new Season;
        $season->id = $seasonId;
        $season->status = $status;

        return $season;
    }
}
