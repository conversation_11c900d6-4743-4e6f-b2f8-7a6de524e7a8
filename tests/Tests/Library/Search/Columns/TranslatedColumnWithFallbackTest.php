<?php

namespace Tests\Library\Search\Columns;

use AwardForce\Library\Search\Columns\TranslatedColumnWithFallback;
use Illuminate\Support\Facades\DB;
use Platform\Search\Defaults;
use Tests\IntegratedTestCase;

final class TranslatedColumnWithFallbackTest extends IntegratedTestCase
{
    public function testDefaultLanguage(): void
    {
        $column = $this->stub();
        \Consumer::shouldReceive('languageCode')->andReturn('el_GR');

        $this->assertEquals('el_GR', $column->defaultLanguage());
    }

    public function testFallbackLanguageWhenDifferentThanConsumer(): void
    {
        $column = $this->stub();
        \Consumer::shouldReceive('languageCode')->andReturn('el_GR');

        $this->assertEquals(default_language_code(), $column->fallbackLanguage());
    }

    public function testNoFallbackIfConsumerLanguageIsTheSame(): void
    {
        $column = $this->stub();
        $this->assertNull($column->fallbackLanguage());
    }

    public function testNoFallbackSelect(): void
    {
        $column = $this->stub();
        \Consumer::shouldReceive('languageCode')->andReturn('el_GR');

        $this->assertEquals('coalesce(NULLIF(tf_stub_table.value, \'\'), tf_stub_table_fallback.value) as tf_stub_table_stub', $column->field()->getValue(DB::connection()->getQueryGrammar()));
    }

    public function testFallbackSelect(): void
    {
        $column = $this->stub();

        $this->assertEquals('tf_stub_table.value as tf_stub_table_stub', $column->field()->getValue(DB::connection()->getQueryGrammar()));
    }

    private function stub()
    {
        return new class extends TranslatedColumnWithFallback
        {
            /**
             * {@inheritDoc}
             */
            public function title()
            {
                return 'stub';
            }

            /**
             * {@inheritDoc}
             */
            public function name(): string
            {
                return 'stub';
            }

            /**
             * {@inheritDoc}
             */
            public function html($record)
            {
                return 'stub';
            }

            /**
             * {@inheritDoc}
             */
            public function default(): Defaults
            {
                return new Defaults('all');
            }

            /**
             * {@inheritDoc}
             */
            public function priority(): int
            {
                return 1;
            }

            /**
             * {@inheritDoc}
             */
            public function fieldName(): string
            {
                return 'stub';
            }
        };
    }
}
