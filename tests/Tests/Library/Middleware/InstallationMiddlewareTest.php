<?php

namespace Tests\Library\Middleware;

use AwardForce\Library\Middleware\InstallationMiddleware;
use AwardForce\Modules\Accounts\Services\AccountsService;
use Illuminate\Support\Facades\App;
use Mockery as m;
use Tests\UnitTestCase;

final class InstallationMiddlewareTest extends UnitTestCase
{
    private $mockAccountManagementService;
    private $middleware;

    public function init()
    {
        $this->mockAccountManagementService = m::mock(AccountsService::class);

        $this->middleware = new InstallationMiddleware($this->mockAccountManagementService);
    }

    public function testInstallationAlreadyCompleted(): void
    {
        $this->mockAccountManagementService->shouldReceive('totalNumberOfAccounts')->andReturn(1);
        App::shouldReceive('abort')->with(404)->once();

        $this->middleware->handle('request', function () {
        });
    }

    public function testInstallationNeedsToBeCompleted(): void
    {
        $this->mockAccountManagementService->shouldReceive('totalNumberOfAccounts')->andReturn(0);

        $this->middleware->handle('request', function () {
        });
    }
}
