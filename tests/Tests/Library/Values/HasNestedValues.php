<?php

namespace Tests\Library\Values;

use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use Platform\Search\HasValues;
use Tests\TestsPrivateMethods;

trait HasNestedValues
{
    use TestsPrivateMethods;

    private function assertHasProvidersFor(ValuesProvider $model, array $expectedTables)
    {
        $providersTables = $this->invokePrivate(
            $model,
            'getValuesProviders'
        )->map(
            function (HasValues $providerModel) {
                return $providerModel->getTable();
            }
        )->toArray();

        sort($providersTables);
        sort($expectedTables);

        $this->assertEquals($providersTables, $expectedTables);
    }
}
