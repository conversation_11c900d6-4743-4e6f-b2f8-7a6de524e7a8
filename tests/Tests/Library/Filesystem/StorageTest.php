<?php

namespace Tests\Library\Filesystem;

use AwardForce\Library\Exceptions\CourierNeedsOtherRegion;
use AwardForce\Library\Filesystem\Exceptions\NoStorageRegionSelected;
use AwardForce\Library\Filesystem\Storage;
use AwardForce\Library\Region\Region;
use AwardForce\Library\Region\RegionSelector;
use AwardForce\Modules\Accounts\Services\CurrentAccountService;
use Illuminate\Contracts\Filesystem\Factory;
use Illuminate\Contracts\Filesystem\Filesystem;
use Illuminate\Filesystem\FilesystemAdapter;
use Mockery as m;
use Tests\UnitTestCase;

final class StorageTest extends UnitTestCase
{
    private CurrentAccountService $currentAccount;
    private Factory $filesystem;
    private RegionSelector $regions;

    /** @var m\MockInterface|Storage */
    private $storage;

    public function init()
    {
        $this->currentAccount = m::mock(CurrentAccountService::class);
        $this->filesystem = m::mock(Factory::class);
        $this->regions = m::mock(RegionSelector::class);

        $this->storage = new Storage(
            $this->currentAccount,
            $this->filesystem,
            $this->regions
        );
    }

    public function testItCallsTheCorrectRegion(): void
    {
        $this->currentAccount->shouldReceive('attribute')->once()->with('region')->andReturn('au');
        $this->filesystem->shouldReceive('disk')->once()->with('s3-au')->andReturn(m::mock(Filesystem::class));

        $this->assertInstanceOf(Filesystem::class, $this->storage->disk());
    }

    public function testItCallsTheCorrectRegionInTheAbsenceOfAnAccount(): void
    {
        $this->currentAccount->shouldReceive('attribute')->once()->with('region')->andReturnNull();

        $this->regions->shouldReceive('isSelected')->once()->andReturn(true);
        $this->regions->shouldReceive('current')->once()->andReturn(new Region('au'));

        $this->filesystem->shouldReceive('disk')->once()->with('s3-au')->andReturn(m::mock(Filesystem::class));

        $this->assertInstanceOf(Filesystem::class, $this->storage->disk());
    }

    public function testItThrowsAnExceptionInTheAbsenceOfAccountAndRegion(): void
    {
        $this->currentAccount->shouldReceive('attribute')->once()->with('region')->andReturnNull();
        $this->regions->shouldReceive('isSelected')->once()->andReturn(false);

        try {
            $this->storage->disk();
        } catch (NoStorageRegionSelected $exception) {
            return;
        }

        $this->fail('No exception was thrown');
    }

    public function testItCopiesAllFiles(): void
    {
        $storage = app(Storage::class);
        $storage->disk('test');

        if ($storage->exists('destination')) {
            $storage->deleteDirectory('destination');
        }

        $storage->copyAll('origin', 'destination');

        $this->assertSame('touched1', $storage->get('destination/touch1'));
        $this->assertSame('touched2', $storage->get('destination/touch2'));
    }

    public function testCourierSetupWithOtherRegion(): void
    {
        $this->currentAccount->shouldReceive('attribute')->times(3)->with('region')->andReturn('au');
        $this->filesystem->shouldReceive('disk')->twice()->with('s3-au')->andReturn(m::mock(FilesystemAdapter::class));
        $this->filesystem->shouldReceive('disk')->twice()->with('tmp')->andReturn(m::mock(FilesystemAdapter::class));
        $this->filesystem->shouldReceive('disk')->once()->with('s3-eu')->andReturn(m::mock(FilesystemAdapter::class));

        $courier = $this->storage->courier();

        try {
            $courier->copyAllToRegion([]);
            $this->fail('Courier should throw CourierNeedsOtherRegion without other region setup.');
        } catch (CourierNeedsOtherRegion $exception) {
            // Ignore
        }

        $courier = $this->storage->courier('eu');
        $courier->copyAllToRegion([]);
    }
}
