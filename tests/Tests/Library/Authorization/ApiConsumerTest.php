<?php

namespace Tests\Library\Authorization;

use AwardForce\Library\Authorization\ApiConsumer;
use Tests\UnitTestCase;

final class ApiConsumerTest extends UnitTestCase
{
    /**
     * @var ApiConsumer
     */
    private $consumer;

    public function init()
    {
        $this->consumer = new ApiConsumer;
    }

    public function testAccounts(): void
    {
        $this->assertEquals([current_account()], $this->consumer->accounts());
    }

    public function testLanguage(): void
    {
        $this->assertEquals(current_account()->defaultLanguage(), $this->consumer->language());
    }

    public function testId(): void
    {
        $this->assertNull($this->consumer->id());
    }

    public function testType(): void
    {
        $this->assertSame('api', $this->consumer->type());
    }

    public function testCan(): void
    {
        $this->assertTrue($this->consumer->can('update', 'EntriesAll'));
        $this->assertFalse($this->consumer->can('change', 'Anything else'));
    }

    public function testCannot(): void
    {
        $this->assertFalse($this->consumer->cannot('update', 'EntriesAll'));
        $this->assertTrue($this->consumer->cannot('change', 'Anything else'));
    }

    public function testGlobalId(): void
    {
        $this->assertEquals(current_account()->owner->globalId, $this->consumer->globalId());
    }
}
