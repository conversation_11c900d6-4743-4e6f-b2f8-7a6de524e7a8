<?php

namespace Tests\Http\Middleware\Api\V2;

use AwardForce\Http\Middleware\Api\V2\Authenticate;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Api\V2\Models\ApiKey;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Group;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\IntegratedTestCase;

#[Group('apiV2')]
final class AuthenticateTest extends IntegratedTestCase
{
    public function testAuthenticationPasses(): void
    {
        $account = $this->muffin(Account::class);

        $apiKeyResult = ApiKey::generate($account->id, 'Test API key name', 'read', (string) $account->slug);

        $middleware = app(Authenticate::class);
        $request = app(Request::class);

        $request->headers->set('Accept', 'application/vnd.Award Force.v2.0+json');
        $request->headers->set('x-api-key', $apiKeyResult->value);

        $result = $middleware->handle($request, function ($request) {
            return $request;
        });

        $this->assertEquals($request, $result);
    }

    public function testAuthenticationFailsWithInvalidKey(): void
    {
        $this->expectException(HttpException::class);

        $account = $this->muffin(Account::class);

        $apiKeyResult = ApiKey::generate($account->id, 'Test API key name', 'read', (string) $account->slug);

        $middleware = app(Authenticate::class);
        $request = app(Request::class);

        $request->headers->set('Accept', 'application/vnd.Award Force.v2.0+json');
        $request->headers->set('x-api-key', Str::random(60));

        $result = $middleware->handle($request, function ($request) {
            throw new HttpException(500, 'This should not be thrown!');
        });
    }

    public function testAuthenticationFailsWithInvalidId(): void
    {
        $this->expectException(HttpException::class);

        $account = $this->muffin(Account::class);

        $apiKeyResult = ApiKey::generate($account->id, 'Test API key name', 'read', (string) $account->slug);

        $middleware = app(Authenticate::class);
        $request = app(Request::class);

        $request->headers->set('Accept', 'application/vnd.Award Force.v2.0+json');
        $request->headers->set('x-api-key', '********-'.$apiKeyResult->getOriginal('value'));

        $result = $middleware->handle($request, function ($request) {
            throw new HttpException(500, 'This should not be thrown!');
        });
    }

    public function testAuthenticationFailsWithInvalidValue(): void
    {
        $this->expectException(HttpException::class);

        $account = $this->muffin(Account::class);

        $apiKeyResult = ApiKey::generate($account->id, 'Test API key name', 'read', (string) $account->slug);

        $middleware = app(Authenticate::class);
        $request = app(Request::class);

        $request->headers->set('Accept', 'application/vnd.Award Force.v2.0+json');
        $request->headers->set('x-api-key', $apiKeyResult->id.'-'.Str::random(60));

        $result = $middleware->handle($request, function ($request) {
            throw new HttpException(500, 'This should not be thrown!');
        });
    }

    public function testAuthenticationFailsWithNoAcceptInHeader(): void
    {
        $this->expectException(HttpException::class);

        $account = $this->muffin(Account::class);

        $apiKeyResult = ApiKey::generate($account->id, 'Test API key name', 'read', (string) $account->slug);

        $middleware = app(Authenticate::class);
        $request = app(Request::class);

        $request->headers->set('x-api-key', $apiKeyResult->value);

        $result = $middleware->handle($request, function ($request) {
            throw new HttpException(500, 'This should not be thrown!');
        });
    }

    public function testAuthenticationFailsWithNoKeyInHeader(): void
    {
        $this->expectException(HttpException::class);

        $middleware = app(Authenticate::class);
        $request = app(Request::class);

        $request->headers->set('Accept', 'application/vnd.Award Force.v2.0+json');
        $request->headers->set('x-api-key', null);

        $result = $middleware->handle($request, function ($request) {
            throw new HttpException(500, 'This should not be thrown!');
        });
    }
}
