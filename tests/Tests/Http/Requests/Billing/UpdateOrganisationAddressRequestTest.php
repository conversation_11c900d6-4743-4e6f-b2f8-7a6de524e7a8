<?php

namespace Tests\Http\Requests\Billing;

use AwardForce\Http\Requests\Billing\UpdateOrganisationAddressRequest;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\IntegratedTestCase;
use Validator;

final class UpdateOrganisationAddressRequestTest extends IntegratedTestCase
{
    use WithFaker;

    protected $request;

    public function init()
    {
        $this->request = new UpdateOrganisationAddressRequest();
    }

    public function testValidationPasses(): void
    {
        $this->request->replace([
            'countryCode' => 'GR',
            'postalCode' => '41334',
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertTrue($validator->passes());
        $this->assertTrue($validator->errors()->isEmpty());
    }

    public function testCountryCodeValidationFails(): void
    {
        $this->request->replace([
            'countryCode' => 'GR',
        ]);
        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
        $this->assertFalse($validator->errors()->isEmpty());
    }
}
