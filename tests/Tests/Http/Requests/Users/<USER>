<?php

namespace Tests\Http\Requests\Users;

use AwardForce\Http\Requests\User\ConfirmDestroyUsers;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Identity\Users\Models\User;
use Tests\IntegratedTestCase;

final class ConfirmDestroyUsersTest extends IntegratedTestCase
{
    /** @var ConfirmDestroyUsers */
    protected $request;

    /** @var User */
    protected $accountOwner;

    /** @var User */
    protected $userA;

    /** @var User */
    protected $userB;

    public function init()
    {
        $this->request = new ConfirmDestroyUsers;
        $this->accountOwner = $this->muffin(User::class);
        current_account()->setOwner($this->accountOwner);
        $this->userA = $this->muffin(User::class);
        $this->userB = $this->muffin(User::class);
        \Consumer::set(new UserConsumer($this->accountOwner));

        Membership::register(current_account(), $this->userA, 'en_GB');
        Membership::register(current_account(), $this->userB, 'en_GB');
    }

    public function testAuthorizesOnlyAccountOwner(): void
    {
        $this->assertTrue($this->request->authorize());
        \Consumer::set(new UserConsumer($this->userB));
        $this->assertFalse($this->request->authorize());
    }

    public function testValidatesPassword(): void
    {
        $this->request->replace(['selected' => [$this->userA->id, $this->userB->id], 'option' => 'option', 'confirmPassword' => 'InValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('confirmPassword'));

        $this->request->replace(['selected' => [$this->userA->id, $this->userB->id], 'option' => 'option', 'confirmPassword' => 'ValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertTrue($validator->passes());
    }

    public function testValidatesOption(): void
    {
        $this->request->replace(['selected' => [$this->userA->id, $this->userB->id],  'confirmPassword' => 'ValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('option'));

        $this->request->replace(['selected' => [$this->userA->id, $this->userB->id], 'option' => 'option', 'confirmPassword' => 'ValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertTrue($validator->passes());
    }

    public function testValidateSelected(): void
    {
        $this->request->replace(['selected' => [],  'option' => 'option', 'confirmPassword' => 'ValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('selected'));

        $this->request->replace(['selected' => [$this->userA->id, $this->userB->id], 'option' => 'option', 'confirmPassword' => 'ValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertTrue($validator->passes());
    }

    public function testCanDestroyUsers(): void
    {
        $this->request->replace(['selected' => [$this->userA->id, $this->userB->id], 'option' => 'option', 'confirmPassword' => 'ValidPa$$w0rd']);
        $validator = \Validator::make($this->request->all(), $this->request->rules());

        $this->assertTrue($validator->passes());
    }
}
