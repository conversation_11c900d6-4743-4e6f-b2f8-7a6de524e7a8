<?php

namespace Tests\Migrations;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Tests\IntegratedTestCase;

final class ChangeFileFieldRegistrationStep extends IntegratedTestCase
{
    public function init()
    {
        $this->muffins(3, Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'registration' => 'step1',
            'type' => function () {
                return 'file';
            },
        ]);

        $this->muffins(3, Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'registration' => 'step1',
            'type' => function () {
                return 'text';
            },
        ]);
    }

    public function testMigration(): void
    {
        $this->assertCount(6, Field::whereResource(Field::RESOURCE_USERS)->whereRegistration('step1')->get());
        $this->assertCount(0, Field::whereResource(Field::RESOURCE_USERS)->whereRegistration('step2')->get());
        $this->assertCount(3, Field::whereResource(Field::RESOURCE_USERS)->whereRegistration('step1')->whereType('file')->get());

        $migration = app(\ChangeRegistrationStepOnFileFields::class);
        $migration->up();

        $this->assertCount(3, Field::whereResource(Field::RESOURCE_USERS)->whereRegistration('step1')->get());
        $this->assertCount(3, Field::whereResource(Field::RESOURCE_USERS)->whereRegistration('step2')->get());
        $this->assertCount(0, Field::whereResource(Field::RESOURCE_USERS)->whereRegistration('step1')->whereType('file')->get());
    }
}
