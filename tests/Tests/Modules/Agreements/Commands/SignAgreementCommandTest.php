<?php

namespace Tests\Modules\Agreements\Commands;

use AwardForce\Modules\Agreements\Commands\SignAgreementCommand;
use AwardForce\Modules\Agreements\Commands\SignAgreementCommandHandler;
use AwardForce\Modules\Agreements\Models\Agreement;
use AwardForce\Modules\Agreements\Models\AgreementRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Mockery as m;
use Tests\UnitTestCase;

final class SignAgreementCommandTest extends UnitTestCase
{
    /**
     * @var AgreementRepository
     */
    protected $agreements;

    public function init()
    {
        $this->agreements = m::spy(AgreementRepository::class);
    }

    public function testSignsAgreement(): void
    {
        $handler = new SignAgreementCommandHandler($this->agreements);

        $agreement = $handler->handle(new SignAgreementCommand(
            new User,
            Agreement::TYPE_JUDGING,
            'I agree',
            'Signature',
            new ScoreSet
        ));

        $this->agreements->shouldHaveReceived('save');
        $this->assertNotNull($agreement->signedAt);
    }
}
