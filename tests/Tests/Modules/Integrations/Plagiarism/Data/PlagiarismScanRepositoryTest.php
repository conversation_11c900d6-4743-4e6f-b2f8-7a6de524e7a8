<?php

namespace Tests\Modules\Integrations\Plagiarism\Data;

use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Integrations\Plagiarism\Copyleaks;
use AwardForce\Modules\Integrations\Plagiarism\Data\PlagiarismScan;
use AwardForce\Modules\Integrations\Plagiarism\Data\PlagiarismScanRepository;
use AwardForce\Modules\Integrations\Plagiarism\PlagiarismDetection;
use Tests\IntegratedTestCase;

final class PlagiarismScanRepositoryTest extends IntegratedTestCase
{
    /** @var PlagiarismScanRepository */
    protected $scans;

    /** @var Entry */
    protected $entry;

    /** @var PlagiarismDetection */
    protected $integration;

    /** @var ValuesService */
    private $valuesService;

    public function init()
    {
        $this->valuesService = app(ValuesService::class);
        $this->scans = app(PlagiarismScanRepository::class);
        $this->entry = $this->muffin(Entry::class, ['submitted_at' => now()]);
        $this->integration = $this->newPlagiarismIntegration();
    }

    private function newPlagiarismIntegration()
    {
        $integration = new Copyleaks;
        $integration->accountId = $this->account->id;
        $integration->seasonId = $this->entry->seasonId;
        $integration->driver = 'copyleaks';

        $integration->save();

        return $integration;
    }

    public function testCountsFieldsWithScanEnabled(): void
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'textarea';
        }]);
        $this->valuesService->setValuesForObject(
            [
                (string) $field->slug => 'value',
            ],
            $this->entry
        );

        PlagiarismScan::requestFor($this->entry, $this->integration, $this->valuesService->mapValuesToFields($this->entry));

        $counts = $this->scans->getPendingCounts($this->integration->id);

        $this->assertEquals(1, $counts->total_entries);
        $this->assertEquals(1, $counts->total_fields);
        $this->assertEquals(0, $counts->total_files);
    }

    public function testIgnoresFieldsWithScanDisabled(): void
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'textarea';
        }]);
        $this->valuesService->setValuesForObject(
            [
                (string) $field->slug => 'value',
            ],
            $this->entry
        );

        PlagiarismScan::requestFor($this->entry, $this->integration, $this->valuesService->mapValuesToFields($this->entry));

        $field->plagiarismDetection = false;
        $field->save();

        $counts = $this->scans->getPendingCounts($this->integration->id);

        $this->assertEquals(0, $counts->total_entries);
        $this->assertEquals(0, $counts->total_fields);
        $this->assertEquals(0, $counts->total_files);
    }

    public function testIgnoresDeletedFields(): void
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'textarea';
        }]);
        $this->valuesService->setValuesForObject(
            [
                (string) $field->slug => 'value',
            ],
            $this->entry
        );

        PlagiarismScan::requestFor($this->entry, $this->integration, $this->valuesService->mapValuesToFields($this->entry));

        $field->delete();

        $counts = $this->scans->getPendingCounts($this->integration->id);

        $this->assertEquals(0, $counts->total_entries);
        $this->assertEquals(0, $counts->total_fields);
        $this->assertEquals(0, $counts->total_files);
    }

    public function testCountsAttachments(): void
    {
        $attachment = $this->muffin(Attachment::class, ['submittable_id' => $this->entry->id]);

        PlagiarismScan::requestFor($this->entry, $this->integration, new Fields([]));

        $counts = $this->scans->getPendingCounts($this->integration->id);

        $this->assertEquals(1, $counts->total_entries);
        $this->assertEquals(0, $counts->total_fields);
        $this->assertEquals(1, $counts->total_files);
    }

    public function testIgnoresDeletedFiles(): void
    {
        $attachment = $this->muffin(Attachment::class, ['submittable_id' => $this->entry->id]);

        PlagiarismScan::requestFor($this->entry, $this->integration, new Fields([]));

        $attachment->file->delete();

        $counts = $this->scans->getPendingCounts($this->integration->id);

        $this->assertEquals(0, $counts->total_entries);
        $this->assertEquals(0, $counts->total_fields);
        $this->assertEquals(0, $counts->total_files);
    }

    public function testIgnoresFileUploadFieldsWithScanDisabled(): void
    {
        $this->markTestIncomplete();

        FieldValue::unguard();

        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $file = $this->muffin(File::class, ['user_id' => $this->entry->userId, 'resource_id' => $field->id]);
        $this->muffin(FieldValue::class, ['field_id' => $field->id, 'foreign_id' => $this->entry->id, 'value' => $file->token]);

        PlagiarismScan::requestFor($this->entry, $this->integration, new Fields([]));

        $field->plagiarismDetection = false;
        $field->save();

        $counts = $this->scans->getPendingCounts($this->integration->id);

        $this->assertEquals(0, $counts->total_entries);
        $this->assertEquals(0, $counts->total_fields);
        $this->assertEquals(0, $counts->total_files);
    }

    public function testPrioritisesPendingScansThatHaveBeenWaitingTheLongest(): void
    {
        $this->muffins(3, Attachment::class, ['submittable_id' => $this->entry->id]);

        PlagiarismScan::requestFor($this->entry, $this->integration, new Fields([]));

        $pending = $this->scans->getByIds($this->scans->getPendingIds($this->integration->id)->all());

        ($newest = $pending[0])->update(['requested_at' => now()->subDays(1)]);
        $unscanned = $pending[1];
        ($oldest = $pending[2])->update(['requested_at' => now()->subDays(2)]);

        $pendingIds = $this->scans->getPendingIds($this->integration->id)->all();

        $this->assertEquals([$unscanned->id, $oldest->id, $newest->id], $pendingIds);
    }
}
