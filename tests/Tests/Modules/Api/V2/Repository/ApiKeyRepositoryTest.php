<?php

namespace Api\V2\Repository;

use AwardForce\Modules\Api\V2\Contracts\ApiKeyRepository;
use AwardForce\Modules\Api\V2\Models\ApiKey;
use Tests\IntegratedTestCase;

class ApiKeyRepositoryTest extends IntegratedTestCase
{
    public function testGetFirstForApplicability()
    {
        $apiKey1 = $this->muffin(ApiKey::class, ['applicability' => ApiKey::APPLICABILITY_MARKETPLACE]);
        $apiKey2 = $this->muffin(ApiKey::class, ['applicability' => ApiKey::APPLICABILITY_GENERAL]);

        $result1 = app(ApiKeyRepository::class)->firstForApplicability(ApiKey::APPLICABILITY_GENERAL);
        $result2 = app(ApiKeyRepository::class)->firstForApplicability(ApiKey::APPLICABILITY_MARKETPLACE);

        $this->assertEquals($apiKey2->id, $result1->id);
        $this->assertEquals($apiKey1->id, $result2->id);

        $apiKey1->delete();

        $result1 = app(ApiKeyRepository::class)->firstForApplicability(ApiKey::APPLICABILITY_GENERAL);
        $result2 = app(ApiKeyRepository::class)->firstForApplicability(ApiKey::APPLICABILITY_MARKETPLACE);

        $this->assertEquals($apiKey2->id, $result1->id);
        $this->assertNull($result2);
    }
}
