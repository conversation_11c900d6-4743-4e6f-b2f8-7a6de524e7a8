<?php

namespace Tests\Modules\Rounds\Models;

use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Models\RoundCollection;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Carbon\Carbon;
use Tests\IntegratedTestCase;

final class RoundCollectionTest extends IntegratedTestCase
{
    public function testResubmissionDeadlineIsInTheAccountTimezone(): void
    {
        $round1 = $this->muffin(Round::class, ['roundType' => Round::ROUND_TYPE_ENTRY]);
        $round1->reviewEndAt(Carbon::now()->addDay(random_int(2, 10))->format('Y-m-d H:i'), 'UTC');
        $round2 = $this->muffin(Round::class, ['roundType' => Round::ROUND_TYPE_ENTRY]);
        $round2->reviewEndAt(Carbon::now()->addDay(random_int(2, 10))->format('Y-m-d H:i'), 'UTC');

        $collection = new RoundCollection([$round1, $round2]);
        $deadline = $collection->resubmissionDeadline();

        $this->assertInstanceOf(Carbon::class, $deadline);
        $this->assertEquals('UTC', $deadline->timezoneName);

        $settings = $this->mock(SettingRepository::class);
        $settings->shouldReceive('getValueByKey')
            ->once()
            ->with('timezone', 'UTC')
            ->andReturn('America/Chicago');

        $this->assertEquals('America/Chicago', $collection->resubmissionDeadline()->timezoneName);
    }

    public function testSubmissionDeadlineIsInTheAccountTimezone(): void
    {
        $round1 = $this->muffin(Round::class, ['roundType' => Round::ROUND_TYPE_ENTRY]);
        $round2 = $this->muffin(Round::class, ['roundType' => Round::ROUND_TYPE_ENTRY]);

        $collection = new RoundCollection([$round1, $round2]);
        $deadline = $collection->submissionDeadline();

        $this->assertInstanceOf(Carbon::class, $deadline);
        $this->assertEquals('UTC', $deadline->timezoneName);

        $settings = $this->mock(SettingRepository::class);
        $settings->shouldReceive('getValueByKey')
            ->once()
            ->with('timezone', 'UTC')
            ->andReturn('America/Chicago');

        $this->assertEquals('America/Chicago', $collection->submissionDeadline()->timezoneName);
    }
}
