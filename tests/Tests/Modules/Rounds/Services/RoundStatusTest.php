<?php

namespace Tests\Modules\Rounds\Services;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use Illuminate\Session\Store;
use Illuminate\Support\Carbon;
use Mockery as m;
use Tests\IntegratedTestCase;

final class RoundStatusTest extends IntegratedTestCase
{
    /** @var m\MockInterface */
    protected $rounds;

    /** @var m\MockInterface */
    protected $manager;

    /** @var m\MockInterface */
    protected $session;

    /** @var RoundStatus */
    protected $status;

    /** @var Form */
    protected $form;

    public function init()
    {
        $this->rounds = m::mock(RoundRepository::class);
        $this->rounds->shouldReceive('countActive')->andReturn(0)->byDefault();

        $this->manager = m::mock(Manager::class)->shouldIgnoreMissing(false);
        $this->session = m::mock(Store::class);

        $this->status = new RoundStatus($this->rounds, $this->manager, $this->session);

        $this->form = $this->muffin(Form::class);

        Round::unguard();
    }

    public function testEntriesNotAllowed(): void
    {
        $this->rounds->shouldReceive('countActive')->with(Round::ROUND_TYPE_ENTRY)->andReturn(0);

        $this->assertFalse($this->status->entriesAllowed($this->form));
    }

    public function testResubmissionNotAllowed(): void
    {
        $this->rounds->shouldReceive('countActiveResubmission')->with(Round::ROUND_TYPE_ENTRY, $this->form->id)->andReturn(0);

        $this->assertFalse($this->status->resubmissionAllowed($this->form));
    }

    public function testResubmissionAllowed(): void
    {
        $this->rounds->shouldReceive('countActiveResubmission')->with(Round::ROUND_TYPE_ENTRY, $this->form->id)->andReturn(1);

        $this->assertTrue($this->status->resubmissionAllowed($this->form));
    }

    public function testResubmissionAllowedAsAManager(): void
    {
        $this->manager->shouldReceive('isManager')->andReturn(true);
        $this->rounds->shouldReceive('countActiveResubmission')->with(Round::ROUND_TYPE_ENTRY)->andReturn(0);

        $this->assertTrue($this->status->resubmissionAllowed($this->form));
    }

    public function testEntryRoundOpen(): void
    {
        $this->rounds->shouldReceive('countActive')->with(Round::ROUND_TYPE_ENTRY, $this->form->id)->andReturn(1);

        $this->assertTrue($this->status->entriesAllowed($this->form));
    }

    public function testEntryManagerOpen(): void
    {
        $this->manager->shouldReceive('isManager')->andReturn(true);

        $this->assertTrue($this->status->entriesAllowed($this->form));
    }

    public function testChapterEntriesNotAllowed(): void
    {
        $this->rounds->shouldReceive('countActive')->with(Round::ROUND_TYPE_ENTRY, [32])->andReturn(0);

        $this->assertFalse($this->status->chapterEntriesAllowed(32, $this->form));
    }

    public function testChapterEntryRoundOpen(): void
    {
        $this->rounds->shouldReceive('countActive')->with(Round::ROUND_TYPE_ENTRY, $this->form->id, [32])->andReturn(1);

        $this->assertTrue($this->status->chapterEntriesAllowed(32, $this->form));
    }

    public function testChapterEntriesWithOverride(): void
    {
        $this->manager->shouldReceive('isManager')->andReturn(true);

        $this->assertTrue($this->status->chapterEntriesAllowed(32, $this->form));
    }

    public function testOpenReturnsOpenRoundsByDefault(): void
    {
        $this->dontExpectOverride();
        $this->rounds->shouldReceive('allOpenIds')->andReturn([1, 3]);

        $this->assertEquals([1, 3], $this->status->allOpen());
    }

    public function testOpenReturnsOverrideRoundsWhenSet(): void
    {
        $this->expectOverride([2, 4]);

        $this->assertEquals([2, 4], $this->status->allOpen());
    }

    public function testOverrideRounds(): void
    {
        $this->session->shouldReceive('put')->with(RoundStatus::SESSION_OVERRIDE, [3, 5])->once();

        $this->status->override([3, 5]);
    }

    public function testResetOverrides(): void
    {
        $this->session->shouldReceive('forget')->with(RoundStatus::SESSION_OVERRIDE)->once();

        $this->status->resetOverrides();
    }

    public function testJudgingRoundIds(): void
    {
        $this->dontExpectOverride();
        $this->rounds->shouldReceive('getAllActiveIds')->with(Round::ROUND_TYPE_JUDGE)->andReturn(collect([7, 9]));

        $this->assertEquals([7, 9], $this->status->judgingRoundIds());
    }

    public function testJudgingRoundIdsWithOverrides(): void
    {
        $this->expectOverride([9, 11, 13]);
        $this->rounds->shouldReceive('onlyType')->with([9, 11, 13], Round::ROUND_TYPE_JUDGE)->andReturn([9, 11]);

        $this->assertEquals([9, 11], $this->status->judgingRoundIds());
    }

    public function testRoundIsScoringJudgingNoOverride(): void
    {
        $this->dontExpectOverride();

        $roundViewing = $this->muffin(Round::class, ['judging_view' => Round::ROUND_VIEW_VIEWING, 'round_type' => Round::ROUND_TYPE_JUDGE]);
        $roundScoring = $this->muffin(Round::class, ['judging_view' => Round::ROUND_VIEW_SCORING, 'round_type' => Round::ROUND_TYPE_JUDGE]);

        /** @var Assignment $assignmentViewing */
        $assignmentViewing = $this->muffin(Assignment::class);
        $assignmentViewing->rounds()->attach([$roundViewing->id]);

        /** @var Assignment $assignmentScoring */
        $assignmentScoring = $this->muffin(Assignment::class);
        $assignmentScoring->rounds()->attach([$roundScoring->id]);

        $status = app(RoundStatus::class);

        $this->assertTrue($status->isScoringJudging(collect([$assignmentScoring])));
        $this->assertFalse($status->isScoringJudging(collect([$assignmentViewing])));
    }

    public function testRoundIsScoringJudgingOverride(): void
    {
        Consumer::shouldReceive('isManager')->andReturn(true);

        /** @var Round $round */
        $round = $this->muffin(Round::class, ['judging_view' => Round::ROUND_VIEW_SCORING, 'round_type' => Round::ROUND_TYPE_JUDGE]);
        $round->endAt(Carbon::now()->subDay(3)->format('Y-m-d H:i'), 'UTC');
        $round->save();

        /** @var Assignment $assignment */
        $assignment = $this->muffin(Assignment::class);
        $assignment->rounds()->attach([$round->id]);

        $status = app(RoundStatus::class);

        $this->assertFalse($status->isScoringJudging(collect([$assignment])));
        $status->override([$round->id]);
        $this->assertTrue($status->isScoringJudging(collect([$assignment])));
    }

    public function testRoundIsViewOnlyJudging(): void
    {
        /** @var Round $round */
        $round = $this->muffin(Round::class, ['judging_view' => Round::ROUND_VIEW_VIEWING, 'round_type' => Round::ROUND_TYPE_JUDGE]);
        $round->endAt(Carbon::now()->subDay(3)->format('Y-m-d H:i'), 'UTC');
        $round->save();

        /** @var Assignment $assignment */
        $assignment = $this->muffin(Assignment::class);
        $assignment->rounds()->attach([$round->id]);

        $status = app(RoundStatus::class);

        $this->assertFalse($status->isScoringJudging(collect([$assignment])));
        $this->assertTrue($status->isViewOnlyJudging() && $status->isViewOnlyJudging(true));
    }

    protected function expectOverride(array $ids)
    {
        Consumer::shouldReceive('isManager')->andReturn(true);

        $this->session->shouldReceive('has')->with(RoundStatus::SESSION_OVERRIDE)->andReturn(true);
        $this->session->shouldReceive('get')->with(RoundStatus::SESSION_OVERRIDE)->andReturn($ids);
    }

    protected function dontExpectOverride()
    {
        $this->session->shouldReceive('has')->with(RoundStatus::SESSION_OVERRIDE)->andReturn(false);
    }

    public function testHasOverridesShouldReturnTrueIfManager()
    {
        $user = $this->setupUserWithRole('Program manager');
        Consumer::set(new UserConsumer($user));

        $rounds = app(RoundStatus::class);
        $rounds->override([1, 2, 3]);

        $this->assertTrue($rounds->hasOverrides());
    }

    public function testHasOverridesShouldReturnFalseIfNotManager()
    {
        $user = $this->setupUserWithRole('Judge');
        Consumer::set(new UserConsumer($user));

        $rounds = app(RoundStatus::class);
        $rounds->override([1, 2, 3]);

        $this->assertFalse($rounds->hasOverrides());
    }
}
