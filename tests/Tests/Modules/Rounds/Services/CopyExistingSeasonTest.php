<?php

namespace Tests\Modules\Rounds\Services;

use AwardForce\Library\Copier\Map;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Rounds\Services\CopyExistingSeason;
use AwardForce\Modules\Seasons\Models\Season;
use Tests\IntegratedTestCase;

final class CopyExistingSeasonTest extends IntegratedTestCase
{
    /** @var CopyExistingSeason */
    private $copyExistingService;

    /** @var Season */
    private $newSeason;

    /** @var RoundRepository */
    private $rounds;

    public function init()
    {
        $this->copyExistingService = app(CopyExistingSeason::class);
        $this->newSeason = $this->muffin(Season::class, ['status' => Season::STATUS_DRAFT]);
        $this->rounds = app(RoundRepository::class);
    }

    public function testCopiesRounds(): void
    {
        [$original, $map] = $this->addOriginalRound();
        $this->copyExistingService->copy(current_account()->activeSeason(), $this->newSeason, $map);
        $roundsInNewSeason = Round::where('season_id', $this->newSeason->id)->get();
        $copy = $roundsInNewSeason->first();

        $this->assertCount(1, $roundsInNewSeason);
        $this->assertNotEquals($original->id, $copy->id);
    }

    public function testCopiesRoundTranslations(): void
    {
        [$original, $map] = $this->addOriginalRound();
        $original->saveTranslation(default_language_code(), 'content', 'foobar', $original->accountId);
        $original->saveTranslation('pl_PL', 'content', 'buzz', $original->accountId);

        $this->copyExistingService->copy(current_account()->activeSeason(), $this->newSeason, $map);
        $copy = translate(Round::where('season_id', '=', $this->newSeason->id)->first());

        $this->assertNotNull($copy);
        foreach ($original->translations as $index => $translation) {
            $this->assertEquals($translation->value, $copy->translations->get($index)->value);
        }
    }

    /**
     * This test is the exact case that caused the issues on season copy on production
     * and this would fail without the addition of default chapter fallback in the rounds CopyExistingService
     */
    public function testUsesFallbackChapter(): void
    {
        $wrongSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $wrongChapter = $this->muffin(Chapter::class, ['season_id' => $wrongSeason->id]);

        [$original, $map] = $this->addOriginalRound($wrongChapter);
        $this->copyExistingService->copy(current_account()->activeSeason(), $this->newSeason, $map);
        $roundsInNewSeason = Round::where('season_id', $this->newSeason->id)->get();
        $copy = $roundsInNewSeason->first();

        $this->assertCount(1, $roundsInNewSeason);
        $this->assertNotEquals($original->id, $copy->id);
    }

    private function addOriginalRound(?Chapter $chapter = null)
    {
        $map = new Map();
        $originalChapter = $chapter ?? $this->muffin(Chapter::class);
        $original = $this->muffin(Round::class);
        $this->rounds->syncChapters($original, [$originalChapter->id]);
        app(\AwardForce\Modules\Chapters\Services\CopyExistingService::class)
            ->copy(current_account()->activeSeason(), $this->newSeason, $map);

        return [$original, $map];
    }
}
