<?php

namespace Tests\Modules\Rounds\Search\Columns;

use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Search\Columns\RoundEndDate;
use AwardForce\Modules\Rounds\Search\Columns\RoundStartDate;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\IntegratedTestCase;

final class RoundTest extends IntegratedTestCase
{
    #[TestWith([RoundEndDate::class, 'endsTz', null])]
    #[TestWith([RoundEndDate::class, 'endsTz', ''])]
    #[TestWith([RoundStartDate::class, 'startsTz', null])]
    #[TestWith([RoundStartDate::class, 'startsTz', ''])]
    public function testHtmlContainsUTCWhenTimezoneIsEmpty(string $columnClass, string $field, ?string $value)
    {
        $round = $this->muffin(Round::class, [
            $field => $value,
        ]);

        $column = new $columnClass();
        $this->assertStringContainsString('UTC', $column->html($round));
    }
}
