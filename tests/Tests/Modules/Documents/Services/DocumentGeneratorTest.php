<?php

namespace Tests\Modules\Documents\Services;

use AwardForce\Modules\Documents\Models\Document;
use AwardForce\Modules\Documents\Services\LocalDocumentGenerator;
use AwardForce\Modules\Documents\Services\WordToPDFConverter;
use Faker\Factory as Faker;
use Tests\UnitTestCase;

final class DocumentGeneratorTest extends UnitTestCase
{
    protected $faker;
    protected $wordContent;
    protected $pdfContent;

    protected function init(): void
    {
        $this->faker = Faker::create();
        $this->wordContent = base64_decode('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');
        $this->pdfContent = '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';
    }

    public function testGenerateReturnsBase64EncodedContentForWordFileType(): void
    {
        $converter = $this->mock(WordToPDFConverter::class);

        $generator = new LocalDocumentGenerator($converter);

        $mergeFields = ['field1' => 'value1', 'field2' => 'value2'];

        $result1 = $generator->handle($this->wordContent, Document::FILE_TYPE_WORD, $mergeFields);

        $this->assertIsString($result1);
        $this->assertTrue(base64_encode(base64_decode($result1, true)) === $result1);

        $converter->shouldReceive('execute')->once()->andReturn($this->pdfContent);

        $result2 = $generator->handle($this->wordContent, Document::FILE_TYPE_PDF, $mergeFields);

        $this->assertIsString($result2);
        $this->assertSame($this->pdfContent, $result2);
        $this->assertTrue(base64_encode(base64_decode($result2, true)) === $result2);
    }

    public function testItDoesNotThrowExceptionIfMergeFieldsContainEmptyMergeValues(): void
    {
        $converter = $this->mock(WordToPDFConverter::class);

        $generator = new LocalDocumentGenerator($converter);

        $mergeFields = ['field1' => 'value1', 'field2' => 'value2', 'field3' => '', 'field3' => null];

        $result1 = $generator->handle($this->wordContent, Document::FILE_TYPE_WORD, $mergeFields);

        $this->assertIsString($result1);
        $this->assertTrue(base64_encode(base64_decode($result1, true)) === $result1);

        $converter->shouldReceive('execute')->once()->andReturn($this->pdfContent);

        $result2 = $generator->handle($this->wordContent, Document::FILE_TYPE_PDF, $mergeFields);

        $this->assertIsString($result2);
        $this->assertSame($this->pdfContent, $result2);
        $this->assertTrue(base64_encode(base64_decode($result2, true)) === $result2);
    }
}
