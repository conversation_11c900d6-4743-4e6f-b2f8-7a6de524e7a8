<?php

namespace Tests\Modules\Categories\Commands;

use AwardForce\Modules\Categories\Commands\DeleteCategories;
use AwardForce\Modules\Categories\Commands\DeleteCategoriesHandler;
use AwardForce\Modules\Categories\Models\Category;
use Tests\IntegratedTestCase;

final class DeleteCategoriesTest extends IntegratedTestCase
{
    public function init()
    {
        $this->handler = app(DeleteCategoriesHandler::class);
    }

    public function testDeletesCategories(): void
    {
        $categories = collect($this->muffins(2, Category::class));

        $this->handler->handle(new DeleteCategories($categories->pluck('id')->toArray()));

        foreach ($categories as $category) {
            $this->assertNull(Category::find($category->id));
        }
    }

    public function testDeletesCategoriesWithChildren(): void
    {
        $parent = $this->muffin(Category::class);
        $children = collect($this->muffins(2, Category::class, ['parent_id' => $parent->id]));

        $this->handler->handle(new DeleteCategories([$parent->id]));
        foreach ($children as $child) {
            $this->assertNotNull($category = Category::find($child->id));
            $this->assertNull($category->parentId);
        }
    }

    public function testFixesBrokenTreeBeforeDeletion(): void
    {
        $parent = $this->muffin(Category::class);
        $children = collect($this->muffins(2, Category::class, ['parent_id' => $parent->id]));

        $parent->lft = 100;
        $parent->rgt = 101;

        $parent->save();

        $this->handler->handle(new DeleteCategories([$parent->id]));
        foreach ($children as $child) {
            $this->assertNotNull($category = Category::find($child->id));
            $this->assertNull($category->parentId);
        }
    }
}
