<?php

namespace Tests\Modules\Categories\Commands;

use AwardForce\Modules\Categories\Commands\CreateCategory;
use AwardForce\Modules\Categories\Commands\CreateCategoryHandler;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Seasons\Models\Season;
use Tests\IntegratedTestCase;

final class CreateCategoryTest extends IntegratedTestCase
{
    public function testCreateFromApp(): void
    {
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromApp(
            $season->id,
            $form->id,
            true,
            $translated
        );

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);

        $this->assertNotNull($category);
    }

    public function testCreateFromApi(): void
    {
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromApi(
            (string) $season->slug,
            $form->id,
            'active',
            $translated
        );

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);

        $this->assertNotNull($category);
    }

    public function testCreateWithParent(): void
    {
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);
        $parent = $this->muffin(Category::class, ['season_id' => $season->id]);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromApp(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            [],
            (string) $parent->slug
        );

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);

        $this->assertEquals($parent->id, $category->parent->id);
    }

    public function testCreateWithChapter(): void
    {
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);
        $chapter = $this->muffin(Chapter::class);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromApp(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            [(string) $chapter->slug]
        );

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);

        $this->assertEquals($chapter->id, $category->chapters()->first()->id);
    }

    public function testCreateFromFormConfiguration(): void
    {
        $files = app(FileRepository::class);

        $file = $this->muffin(File::class, ['resource' => 'Categories', 'resource_id' => null]);
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromFormConfiguration(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            null,
            2,
            3,
            true,
            300,
            null,
            false,
            $file->token
        );

        $categoryOne = $this->app->make(CreateCategoryHandler::class)->handle($command);
        $categoryFiles = $files->getByResourceId(File::RESOURCE_CATEGORIES, [$categoryOne->id])->groupBy('resource_id');
        $this->assertCount(1, $categoryFiles);

        $newFileOne = $this->muffin(File::class, ['resource' => 'Categories', 'resource_id' => null]);
        $newFileTwo = $this->muffin(File::class, ['resource' => 'Categories', 'resource_id' => null]);
        $fileTokens = [$newFileOne->token, $newFileTwo->token];
        $command = CreateCategory::fromFormConfiguration(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            null,
            2,
            3,
            true,
            300,
            null,
            false,
            json_encode($fileTokens)
        );
        $categoryTwo = $this->app->make(CreateCategoryHandler::class)->handle($command);
        $categoryFiles = $files->getByResourceId(File::RESOURCE_CATEGORIES, [$categoryTwo->id, $categoryOne->id])->groupBy('resource_id');

        $this->assertCount(1, $categoryFiles[$categoryOne->id]);
        $this->assertCount(2, $categoryFiles[$categoryTwo->id]);
    }

    public function testCreateFromAppWithAllData(): void
    {
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromFormConfiguration(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            null,
            2,
            3,
            true,
            300,
            null,
            false
        );

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);

        $this->assertNotNull($category);
        $this->assertEquals(2, $category->divisions);
        $this->assertEquals(300, $category->maxImageWidth);
        $this->assertEquals(3, $category->entrantMaxEntries);
        $this->assertTrue((bool) $category->fillEntryName);
        $this->assertFalse((bool) $category->packingSlip);
    }

    public function testCreateCategoryShouldHaveDefaultMaxWidthWhenIsntSet(): void
    {
        $season = $this->muffin(Season::class);
        $form = $this->muffin(Form::class);

        $translated = [
            'name' => [
                'en_GB' => 'Test Name',
            ],
        ];

        $command = CreateCategory::fromFormConfiguration(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            null,
            2,
            3,
            true,
            0
        );

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);
        $this->assertEquals(Category::DEFAULT_MAX_IMAGE_WIDTH, $category->maxImageWidth);

        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);
        $this->assertEquals(Category::DEFAULT_MAX_IMAGE_WIDTH, $category->maxImageWidth);

        $command = CreateCategory::fromFormConfiguration(
            $season->id,
            $form->id,
            true,
            $translated,
            false,
            null,
            2,
            3,
            true,
            ''
        );
        $category = $this->app->make(CreateCategoryHandler::class)->handle($command);
        $this->assertEquals(Category::DEFAULT_MAX_IMAGE_WIDTH, $category->maxImageWidth);
    }
}
