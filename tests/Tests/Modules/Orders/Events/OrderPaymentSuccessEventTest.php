<?php

namespace Tests\Modules\Orders\Events;

use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasCreated;
use AwardForce\Modules\Identity\Users\Models\User;
use Tests\IntegratedTestCase;

class OrderPaymentSuccessEventTest extends IntegratedTestCase
{
    public function testPayload()
    {
        $order = $this->muffin(Order::class, ['userId' => $this->muffin(user::class)->id, 'processing_fee' => 1.1]);
        $payload = (new OrderWasCreated($order))->webhookPayload();

        $this->assertArrayHasKey('address', $payload);
        $this->assertArrayHasKey('city', $payload);
        $this->assertArrayHasKey('comments', $payload);
        $this->assertArrayHasKey('country', $payload);
        $this->assertArrayHasKey('created_at', $payload);
        $this->assertArrayHasKey('currency', $payload);
        $this->assertArrayHasKey('discount', $payload);
        $this->assertArrayHasKey('discount_code', $payload);
        $this->assertArrayHasKey('invoice_number', $payload);
        $this->assertArrayHasKey('ip_address', $payload);
        $this->assertArrayHasKey('items', $payload);
        $this->assertArrayHasKey('member_number', $payload);
        $this->assertArrayHasKey('payment_status', $payload);
        $this->assertArrayHasKey('processing_fee', $payload);
        $this->assertArrayHasKey('region', $payload);
        $this->assertArrayHasKey('season', $payload);
        $this->assertArrayHasKey('slug', $payload);
        $this->assertArrayHasKey('state', $payload);
        $this->assertArrayHasKey('subtotal', $payload);
        $this->assertArrayHasKey('tax', $payload);
        $this->assertArrayHasKey('tax_rate', $payload);
        $this->assertArrayHasKey('tax_term', $payload);
        $this->assertArrayHasKey('total', $payload);
        $this->assertArrayHasKey('transaction_date', $payload);
        $this->assertArrayHasKey('transaction_provider', $payload);
        $this->assertArrayHasKey('transaction_reference', $payload);
        $this->assertArrayHasKey('transaction_status', $payload);
        $this->assertArrayHasKey('user', $payload);
        $this->assertArrayHasKey('event', $payload);
        $this->assertArrayHasKey('timestamp', $payload);
        $this->assertArrayHasKey('total', $payload);
    }

    public function testSubTotalShouldHaveValue()
    {
        $order = $this->muffin(Order::class, ['userId' => $this->muffin(user::class)->id, 'processing_fee' => 1.1, 'sub_total' => 15.0]);
        $payload = (new OrderWasCreated($order))->webhookPayload();

        $this->assertEquals(15.0, $payload['subtotal']);
    }

    public function testProcessingFeeShouldHaveValue()
    {
        $order = $this->muffin(Order::class, ['userId' => $this->muffin(user::class)->id, 'processing_fee' => 1.1, 'sub_total' => 15.0]);
        $payload = (new OrderWasCreated($order))->webhookPayload();

        $this->assertEquals(1.1, $payload['processing_fee']);
    }

    public function testDiscountCodeShouldHaveValueEvenWhenTheOrderTotalIsZero()
    {
        $order = $this->muffin(Order::class, ['userId' => $this->muffin(user::class)->id, 'discount' => 12, 'discount_code' => 'DISCOUNT']);
        $payload = (new OrderWasCreated($order))->webhookPayload();

        $this->assertEquals(12, $payload['discount']);
        $this->assertEquals('DISCOUNT', $payload['discount_code']);
    }
}
