<?php

namespace Tests\Modules\Files\Services;

use AwardForce\Library\Cloud\Aws\Adapters\AwsCredentialsAdapter;
use AwardForce\Modules\Files\Services\Uploader;
use AwardForce\Modules\Files\Services\UploaderOptions;
use Illuminate\Contracts\Config\Repository as Config;
use Illuminate\Translation\Translator;
use Mockery as m;
use Tests\UnitTestCase;

final class UploaderTest extends UnitTestCase
{
    protected $config;
    protected $uploader;
    protected $translator;
    protected $manager;

    public function init()
    {
        $this->config = m::spy(Config::class);
        $this->translator = m::mock(Translator::class)->shouldIgnoreMissing();

        $this->uploader = new Uploader(new UploaderOptions(1, 'abcefg', []), $this->config, $this->translator, app(AwsCredentialsAdapter::class));
    }

    public function test_max_filesize(): void
    {
        $this->translator->shouldReceive('get')->with('files.uploader.errors')->once()
            ->andReturn([
                'generic' => 'generic error',
                'file' => 'filesize: :filesize',
            ]);

        $fileSize = 123;
        $this->assertInstanceOf(Uploader::class, $this->uploader->setMaxFileSize($fileSize));
        $this->assertEquals($fileSize, $this->uploader->getMaxFileSize());

        $options = $this->uploader->options();
        $this->assertEquals(($fileSize * 1024).'KB', array_get($options, 's3.filters.max_file_size'));
        $this->assertArrayHasKey('errors', $options);
        $this->assertArrayHasKey('file', $options['errors']);
        $this->assertEquals('filesize: 123', $options['errors']['file']);
    }

    public function test_max_attachments(): void
    {
        $maxAttachments = 12;
        $this->assertInstanceOf(Uploader::class, $this->uploader->setMaxAttachments($maxAttachments));

        $options = $this->uploader->options();
        $this->assertEquals($maxAttachments, array_get($options, 'maxAttachments'));
    }

    public function test_multi_select(): void
    {
        $this->uploader->setMultiSelect(true);
        $this->assertTrue($this->uploader->isMultiSelect());

        $options = $this->uploader->options();
        $this->assertTrue(array_get($options, 's3.multi_selection'));

        $this->uploader->setMultiSelect(false);
        $this->assertFalse($this->uploader->isMultiSelect());

        $options = $this->uploader->options();
        $this->assertFalse(array_get($options, 's3.multi_selection'));
    }

    public function test_setting_of_field_types(): void
    {
        $this->config->shouldReceive('get')->with('filetypes.image')->twice()->andReturn(['jpg', 'png', 'gif']);

        $this->uploader->setFileTypes('image');
        $this->uploader->setFileTypes(['image']);
        $this->uploader->setFileTypes(['audio' => ['m4a', 'm4p', 'mp3']]);
    }

    public function test_set_resource_id(): void
    {
        $resource = 'magical-resource';
        $this->uploader->setResource($resource);

        $options = $this->uploader->options();

        $this->assertEquals($resource, array_get($options, 'resource'));
        $this->assertNull(array_get($options, 'resourceId'));
    }

    public function test_set_resource_with_id(): void
    {
        $resource = 'magical-resource';
        $id = 123;
        $this->uploader->setResource($resource, $id);

        $options = $this->uploader->options();

        $this->assertEquals($resource, array_get($options, 'resource'));
        $this->assertEquals($id, array_get($options, 'resourceId'));
    }

    public function test_temp_prefix(): void
    {
        $prefix = 'prefix';
        $this->uploader->setTempPrefix($prefix);

        $options = $this->uploader->options();

        $this->assertStringContainsString('/1-prefix', array_get($options, 'tempPrefix')); // user id prefix
        $this->assertStringContainsString($prefix, array_get($options, 'tempPrefix'));
        $this->assertStringContainsString($prefix, array_get($options, 's3.multipart_params.key'));
        $this->assertStringContainsString($prefix, array_get($options, 's3.multipart_params.Filename'));
    }

    public function test_it_can_set_and_retrieve_settings(): void
    {
        $this->uploader->setMinFileSize(500);
        $this->uploader->setUrl('some url');
        $this->uploader->setUseFileToken(true);

        $this->assertSame(500, $this->uploader->getMinFileSize());
        $this->assertTrue($this->uploader->isUsingFileTokens());
        $this->assertSame('', $this->uploader->humanReadableExtensions());
    }
}
