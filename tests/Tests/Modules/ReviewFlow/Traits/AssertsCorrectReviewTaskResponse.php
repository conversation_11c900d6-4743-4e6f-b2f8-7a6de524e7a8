<?php

namespace Tests\Modules\ReviewFlow\Traits;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Search\Columns\Decision;
use Illuminate\Testing\Fluent\AssertableJson;
use Tectonic\LaravelLocalisation\Database\TranslationService;

trait AssertsCorrectReviewTaskResponse
{
    protected function assertCorrectReviewTaskResponse(AssertableJson $json, ReviewTask $reviewTask): void
    {
        $entry = $reviewTask->entry;
        $category = translate($entry->category);
        $reviewStage = translate($reviewTask->reviewStage);
        $season = translate($entry->season);
        $entryPath = Vertical::replace('entry');
        $json->has(
            'category',
            fn($json) => $json->where('slug', (string) $category->slug)
                ->where('name.en_GB', (string) $category->name)
                ->where('link', fn(string $link) => str_ends_with($link, '/category/'.$category->slug))
                ->etc()
        )
            ->has(
                $entryPath,
                fn($json) => $json->where('slug', (string) $entry->slug)
                    ->where('title', (string) $entry->title)
                    ->where('link', fn(string $link) => str_ends_with($link, "/{$entryPath}/{$entry->slug}"))
                    ->etc()
            )
            ->has(
                Vertical::replace('review_stage'),
                fn($json) => $json->where('slug', (string) $reviewStage->slug)
                    ->where('name.en_GB', (string) $reviewStage->name)
                    ->where('link', fn(string $link) => str_ends_with($link, '/'.Vertical::replace('review-stage').'/'.$reviewStage->slug))
                    ->etc()
            )
            ->has(
                'season',
                fn($json) => $json->where('slug', (string) $season->slug)
                    ->where('name.en_GB', (string) $season->name)
                    ->where('link', fn(string $link) => str_ends_with($link, '/season/'.$season->slug))
                    ->etc()
            )
            ->where('token', (string) $reviewTask->token)
            ->where('created', $reviewTask->createdAt->toIso8601ZuluString())
            ->where('updated', $reviewTask->updatedAt->toIso8601ZuluString())
            ->where('decision', app(Decision::class)->apiValue($reviewTask))
            ->etc();
    }

    protected function setReviewStageStatuses(ReviewTask $reviewTask): void
    {
        $reviewStage = $reviewTask->reviewStage;

        $translationService = app(TranslationService::class);
        $translationService->sync($reviewStage, [
            'proceedStatus' => [
                'en_GB' => 'approved',
            ],
            'stopStatus' => [
                'en_GB' => 'rejected',
            ],
        ]);
    }
}
