<?php

namespace Tests\Modules\ReviewFlow\Search\Columns;

use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Search\Columns\Decision;
use Tests\IntegratedTestCase;

final class DecisionTest extends IntegratedTestCase
{
    public function testApiValueReturnsNullIfNoActionYet(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class);
        $column = new Decision();
        $this->assertNull($column->apiValue($reviewTask));
    }

    public function testApiValueReturnsStopValueIfActionIsStop(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class, [
            'actionTaken' => ReviewTask::ACTION_STOP,
        ]);
        $column = new Decision();
        $this->assertEquals($reviewTask->reviewStage->stopStatus, $column->apiValue($reviewTask));
    }

    public function testApiValueReturnsProceedValueIfActionIsProceed(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class, [
            'actionTaken' => ReviewTask::ACTION_PROCEED,
        ]);
        $column = new Decision();
        $this->assertEquals($reviewTask->reviewStage->proceedStatus, $column->apiValue($reviewTask));
    }
}
