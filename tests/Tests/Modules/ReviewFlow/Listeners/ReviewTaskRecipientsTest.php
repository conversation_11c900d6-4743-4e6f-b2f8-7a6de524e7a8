<?php

namespace Tests\Modules\ReviewFlow\Listeners;

use AwardForce\Modules\Broadcasts\Models\Broadcast;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use Tests\IntegratedTestCase;

final class ReviewTaskRecipientsTest extends IntegratedTestCase
{
    /** @var Broadcast */
    private $broadcast;

    public function init()
    {
        $this->broadcast = Broadcast::create([
            'type' => 'reviewers',
            'seasonId' => $this->account->activeSeason()->id,
            'filters' => [],
        ]);
    }

    public function testCreatesRecipientsFromReviewTaskAssignees(): void
    {
        $user = $this->muffin(User::class);

        $task = $this->muffin(ReviewTask::class);
        $task->assignees()->attach([$user->id]);

        $recipients = $this->broadcast->prepareRecipients();

        $this->assertCount(1, $recipients);
        $this->assertEquals($user->email, $recipients->first()->email());
    }

    public function testCreatesMultipleRecipientsForSameUserAssignedToMultipleTasks(): void
    {
        $user = $this->muffin(User::class);

        $task1 = $this->muffin(ReviewTask::class);
        $task1->assignees()->attach($user->id);

        $task2 = $this->muffin(ReviewTask::class);
        $task2->assignees()->attach($user->id);

        $this->broadcast->consolidateRecipients = false;

        $recipients = $this->broadcast->prepareRecipients();

        $this->assertCount(2, $recipients);
        $this->assertEquals($user->email, $recipients->first()->email());
        $this->assertEquals($user->email, $recipients->last()->email());
    }

    public function testCreatesRecipientsFromNameAndEmail(): void
    {
        $task = $this->muffin(ReviewTask::class, [
            'assignee_email' => '<EMAIL>',
            'assignee_name' => 'Review Flow',
        ]);

        $recipients = $this->broadcast->prepareRecipients();

        $this->assertCount(1, $recipients);
        $this->assertEquals($task->assigneeEmail, $recipients->first()->email());
    }
}
