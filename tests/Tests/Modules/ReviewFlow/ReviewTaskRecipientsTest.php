<?php

namespace Tests\Modules\ReviewFlow;

use AwardForce\Modules\Broadcasts\Models\Broadcast;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Services\Recipients\Recipients;
use Illuminate\Support\Arr;
use Tests\IntegratedTestCase;

final class ReviewTaskRecipientsTest extends IntegratedTestCase
{
    private $broadcast;

    public function testGetRecipientsForReviewStage(): void
    {
        $notification = new Notification(['trigger' => 'review.stage.started']);
        $notification->save();

        $reviewStage = $this->muffin(
            ReviewStage::class,
            [
                'mode' => 'notification',
                'review_by' => ReviewStage::REVIEW_FIELD,
                'start_notification_id' => $notification->id,
            ]
        );
        $reviewTask = $this->muffin(ReviewTask::class, [
            'assignee_name' => 'Joe Bloggs',
            'assignee_email' => '<EMAIL>',
            'account_id' => current_account_id(),
            'review_stage_id' => $reviewStage->id,
        ]);

        $entry = $reviewTask->entry;
        $chapter = translate($entry->chapter);
        $category = translate($entry->category);
        $recipients = $this->broadcast->prepareRecipients();

        $this->assertCount(1, $recipients);
        $this->assertSame(($recipient = $recipients->first())->firstName(), $reviewTask->assigneeName);

        $this->assertSame(Arr::get($data = $recipient->data(), 'entry_name'), $entry->title);
        $this->assertSame(Arr::get($data, 'entry_slug'), (string) $entry->slug);
        $this->assertSame(Arr::get($data, 'entry_local_id'), $entry->localId);
        $this->assertSame(Arr::get($data, 'chapter'), $chapter->name);
        $this->assertSame(Arr::get($data, 'category'), $category->name);
    }

    public function testGetRecipientsForReviewTaskFieldReviewer(): void
    {
        $notification = new Notification(['trigger' => 'review.stage.started']);
        $notification->save();

        $reviewStage = $this->muffin(
            ReviewStage::class,
            [
                'mode' => 'notification',
                'review_by' => ReviewStage::REVIEW_MANAGER,
                'start_notification_id' => null,
            ]
        );
        $reviewTask = $this->muffin(ReviewTask::class, [
            'assignee_name' => 'Joe Bloggs',
            'assignee_email' => '<EMAIL>',
            'account_id' => current_account_id(),
            'review_stage_id' => $reviewStage->id,
        ]);

        $recipient = app(Recipients::class)->get($reviewStage, $reviewTask, $notification, true)->first();

        $this->assertSame('<EMAIL>', (string) $recipient->destination());
    }

    public function testGetRecipientsForReviewTaskManagerReviewer(): void
    {
        $notification = new Notification(['trigger' => 'review.stage.started']);
        $notification->save();

        $reviewStage = $this->muffin(
            ReviewStage::class,
            [
                'mode' => 'notification',
                'review_by' => ReviewStage::REVIEW_FIELD,
                'start_notification_id' => null,
            ]
        );
        $reviewTask = $this->muffin(ReviewTask::class, [
            'assignee_name' => null,
            'assignee_email' => null,
            'account_id' => current_account_id(),
            'review_stage_id' => $reviewStage->id,
        ]);

        $reviewTask->assignees()->attach($this->muffin(User::class, [
            'first_name' => 'Joe',
            'last_name' => 'Bloggs',
            'email' => '<EMAIL>',
        ],
        ));

        $recipient = app(Recipients::class)->get($reviewStage, $reviewTask, $notification, true)->first();
        $this->assertSame('<EMAIL>', (string) $recipient->destination());

        $recipient = app(Recipients::class)->get($reviewStage, $reviewTask, $notification, false);
        $this->assertEmpty($recipient);
    }

    public function init()
    {
        $this->broadcast = Broadcast::create([
            'type' => 'reviewers',
            'seasonId' => $this->account->activeSeason()->id,
            'filters' => [],
        ]);
    }
}
