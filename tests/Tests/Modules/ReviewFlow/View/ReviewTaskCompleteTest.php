<?php

namespace Tests\Modules\ReviewFlow\View;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\View\ReviewTaskComplete;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\FileBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\InputBag;
use Symfony\Component\HttpFoundation\ParameterBag;
use Symfony\Component\HttpFoundation\ServerBag;
use Tests\IntegratedTestCase;

final class ReviewTaskCompleteTest extends IntegratedTestCase
{
    private Entry $entry;

    public function testRedirectsToManagersViewWithProperTab(): void
    {
        $this->newRequest('entry.manager.view');
        $view = app(ReviewTaskComplete::class);

        $this->assertStringContainsString('entry/manager/'.$this->entry->slug, $view->redirectTo());
        $this->assertStringContainsString('?vtab=entry.manager.tabs.review-flow', $view->redirectTo());
    }

    public function testRedirectsToReviewTaskManage(): void
    {
        $this->newRequest('review-flow.task.manage');
        $view = app(ReviewTaskComplete::class);

        $this->assertStringEndsWith('/entry/manage-reviews', $view->redirectTo());
    }

    public function testRedirectsToReviewTask(): void
    {
        $this->newRequest('review-flow.task.review');
        $view = app(ReviewTaskComplete::class);

        $this->assertStringContainsString('entry/review/', $view->redirectTo());
    }

    public function testNoRedirectsToOtherRoutes(): void
    {
        $this->newRequest('voting.index'); // Non-whitelisted route
        $view = app(ReviewTaskComplete::class);

        $this->assertNull($view->redirectTo());
    }

    private function newRequest(string $routeName): void
    {
        $reviewTask = $this->muffin(ReviewTask::class);
        $this->entry = $reviewTask->entry;

        $this->app->singleton('request', function () use ($reviewTask, $routeName) {
            $request = new Request();

            $request->query = $parameterBag = app(InputBag::class);
            $request->request = $parameterBag;
            $request->attributes = new ParameterBag(['redirectTo' => $routeName]);
            $request->cookies = $parameterBag;
            $request->files = app(FileBag::class);
            $request->server = app(ServerBag::class);
            $request->headers = app(HeaderBag::class);

            $request->request->set('reviewTask', $reviewTask);

            return $request;
        });
    }
}
