<?php

namespace Tests\Modules\Identity\Users\Search\Columns;

use AwardForce\Library\Search\Actions\SimpleLinkAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Search\Columns\Mobile;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Search\Defaults;
use Tests\IntegratedTestCase;

final class ColumnsTest extends IntegratedTestCase
{
    public function testActionHasCorrectLabel(): void
    {
        $column = new ActionOverflow(Str::random());
        $column->addAction(new SimpleLinkAction('test label', route('category.new')));
        $user = $this->muffin(User::class);

        $htmlWithoutLabel = $column->html(null)->toHtml();
        $htmlWithLabel = $column->html($user)->toHtml();

        $this->assertStringNotContainsString(trans('buttons.action_overflow', ['resource' => $user->resourceLabel()]), $htmlWithoutLabel);
        $this->assertStringContainsString(trans('buttons.action_overflow', ['resource' => $user->resourceLabel()]), $htmlWithLabel);
        $this->assertSame($user->name, $user->resourceLabel());
    }

    public function testMobileColumnSearchDefaultWhenRegistrationsAreNotOpen(): void
    {
        $column = new Mobile;
        $this->assertTrue($column->default()->matches(new Defaults('export')));
    }

    public function testMobileColumnAllDefaultWhenMobileRegistrationsAreOpen(): void
    {
        $column = new Mobile;
        $this->instance(SettingRepository::class, $settings = m::mock(SettingRepository::class));
        $settings->shouldReceive('getByKey')->with('enable-mobile-registrations')
            ->andReturn((object) [
                'key' => 'enable-mobile-registrations',
                'value' => true,
            ]);
        $settings->shouldReceive('getByKey')->andReturnNull();

        $this->assertTrue($column->default()->matches(new Defaults('all')));
    }
}
