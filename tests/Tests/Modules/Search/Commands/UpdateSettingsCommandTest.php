<?php

namespace Tests\Modules\Search\Commands;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Search\Commands\UpdateSettingsCommand;
use AwardForce\Modules\Search\Commands\UpdateSettingsCommandHandler;
use AwardForce\Modules\Search\SavedViewShortcuts;
use AwardForce\Modules\Search\Search;
use Tests\IntegratedTestCase;

final class UpdateSettingsCommandTest extends IntegratedTestCase
{
    /** @var UpdateSettingsCommandHandler */
    protected $handler;

    public function init()
    {
        $this->handler = new UpdateSettingsCommandHandler();
    }

    public function testUpdateSettings(): void
    {
        $search = $this->muffin(Search::class, ['user_id' => Consumer::id()]);

        $command = new UpdateSettingsCommand(
            $search,
            'New Search Name',
            1,
            new SavedViewShortcuts(false, false)
        );

        $this->handler->handle($command);

        $search = Search::first();

        $this->assertEquals('New Search Name', $search->name);
        $this->assertEquals(true, $search->shared);
        $this->assertEquals(false, $search->shortcuts->dashboard);
        $this->assertEquals(false, $search->shortcuts->listView);
    }
}
