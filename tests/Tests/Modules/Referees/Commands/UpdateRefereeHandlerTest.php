<?php

namespace Tests\Modules\Referees\Commands;

use AwardForce\Modules\Referees\Commands\UpdateReferee;
use AwardForce\Modules\Referees\Commands\UpdateRefereeHandler;
use AwardForce\Modules\Referees\Models\Referee;
use Faker\Generator as Faker;
use Tests\IntegratedTestCase;

class UpdateRefereeHandlerTest extends IntegratedTestCase
{
    private Faker $faker;

    public function init(): void
    {
        $this->faker = app(Faker::class);
    }

    public function testItCanUpdateRefereeNameAndEmail(): void
    {
        $referee = $this->muffin(Referee::class);
        $handler = app(UpdateRefereeHandler::class);

        $handler->handle(new UpdateReferee(
            $referee->id,
            $name = $this->faker->name,
            $email = $this->faker->email,
        ));

        $this->assertDatabaseHas(Referee::class, [
            'id' => $referee->id,
            'name' => $name,
            'email' => $email,
        ]);
    }
}
