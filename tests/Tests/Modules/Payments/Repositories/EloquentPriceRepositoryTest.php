<?php

namespace Tests\Modules\Payments\Repositories;

use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Repositories\EloquentPriceRepository;
use AwardForce\Modules\Seasons\Models\Season;
use Tests\IntegratedTestCase;

final class EloquentPriceRepositoryTest extends IntegratedTestCase
{
    /**
     * @var EloquentPriceRepository
     */
    protected $repository;

    public function init()
    {
        $this->repository = app(EloquentPriceRepository::class);
    }

    public function testTagPrices(): void
    {
        // Matching record
        $match = $this->muffin(Price::class, ['type' => Price::TYPE_TAG]);

        // Non-matching records
        $this->muffin(Price::class, ['type' => Price::TYPE_TAG, 'season_id' => $match->seasonId + 1]); // different season
        $this->muffin(Price::class, ['type' => Price::TYPE_ENTRANT]); // different type

        $prices = $this->repository->tagPrices($match->seasonId);

        $this->assertCount(1, $prices);
        $this->assertEquals($match->id, $prices->first()->id);
    }

    public function testGetsDefaultPriceForATypeAndSeason(): void
    {
        $pastSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $oldPrice = $this->muffin(Price::class, ['default' => true, 'type' => Price::TYPE_ENTRANT, 'season_id' => $pastSeason->id]);

        $price = $this->muffin(Price::class, ['default' => true, 'type' => Price::TYPE_ENTRANT]);

        $defaultPrice = $this->repository->getDefault(Price::TYPE_ENTRANT, $price->seasonId);

        $this->assertEquals($price->id, $defaultPrice->id);
    }
}
