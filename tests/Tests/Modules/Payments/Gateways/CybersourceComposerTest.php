<?php

namespace Tests\Modules\Payments\Gateways;

use AwardForce\Auth\Models\Setting;
use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Payments\Composers\CybersourceComposer;
use AwardForce\Modules\Payments\Gateways\Cybersource;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Mockery as m;
use Tests\IntegratedTestCase;

final class CybersourceComposerTest extends IntegratedTestCase
{
    public function testComposer(): void
    {
        app()->instance(Cart::class, $cart = m::mock(Cart::class));
        $cart->shouldReceive('total')->andReturn(new Amount(rand(), new Currency('USD')));
        $cart->shouldReceive('currencyCode')->andReturn('USD');
        $this->muffin(Setting::class, ['key' => 'cybersource-api-key', 'value' => $apiKey = Str::random()]);
        $this->muffin(Setting::class, ['key' => 'cybersource-api-identifier', 'value' => $apiId = Str::random()]);
        $this->muffin(Setting::class, ['key' => 'cybersource-org-unit-id', 'value' => $orgUnitId = Str::random()]);
        $this->muffin(Setting::class, ['key' => 'payment-test-mode', 'value' => true]);

        app(CybersourceComposer::class)->compose($view = m::spy(View::class));

        $view->shouldHaveReceived('with')->withArgs(function ($args) use ($apiKey) {
            $this->assertIsObject(JWT::decode($args['jwt'], new Key($apiKey, 'HS256')));
            $this->assertSame(Cybersource::SONGBIRD_STAGING, $args['jsUrl']);
            $this->assertArrayHasKey('order', $args);

            return true;
        });
    }
}
