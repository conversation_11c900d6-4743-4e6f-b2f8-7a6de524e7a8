<?php

namespace Tests\Modules\Payments\Services;

use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Costing\NullPrice;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Models\Tax;
use AwardForce\Modules\Payments\Services\Prices;
use AwardForce\Modules\Seasons\Models\Season;
use Consumer;
use Tests\IntegratedTestCase;

final class PricesTest extends IntegratedTestCase
{
    public function init()
    {
        $this->muffin(Tax::class);
    }

    private function openCart(string $currency)
    {
        $cart = Cart::open(Consumer::user());
        $cart->setCurrency(new Currency($currency));

        return $cart;
    }

    public function testReturnsEntryPricesGivenSeason(): void
    {
        $oldSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);

        $price = $this->muffin(Price::class, ['type' => Price::TYPE_ENTRY, 'default' => true]);

        $cart = $this->openCart('AUD');
        $prices = app(Prices::class);

        $this->assertEquals($price->id, $prices->getPriceForEntryFee($cart, $this->season->id)->id);
        $this->assertInstanceOf(NullPrice::class, $prices->getPriceForEntryFee($cart, $oldSeason->id));
    }

    public function testReturnsFirstPriceIfNoDefaultAvailable(): void
    {
        $priceA = $this->muffin(Price::class, ['type' => Price::TYPE_ENTRY, 'default' => false]);
        $this->muffin(Price::class, ['type' => Price::TYPE_ENTRY, 'default' => false]);

        $cart = $this->openCart('AUD');
        $prices = app(Prices::class);

        $this->assertEquals($priceA->id, $prices->getPriceForEntryFee($cart, $this->season->id)->id);
    }
}
