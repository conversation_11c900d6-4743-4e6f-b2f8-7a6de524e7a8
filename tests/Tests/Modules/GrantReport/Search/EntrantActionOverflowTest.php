<?php

namespace Tests\Modules\GrantReport\Search;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Users\Models\User;
use Faker\Factory as Faker;
use Tests\IntegratedTestCase;

class EntrantActionOverflowTest extends IntegratedTestCase
{
    private $grantReport;

    public function init()
    {
        $user = $this->muffin(User::class, ['last_name' => Faker::create()->lastName()]);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $this->grantReport = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);
    }

    public function testDisplayPreviewActions()
    {
        $view = view('grant-report.entrant.search.action-overflow', ['grantReport' => $this->grantReport])->render();

        $this->assertStringContainsString('<a href="https://notapplicable.app/grant-report/entrant/'.$this->grantReport->slug.'/preview">', $view);
        $this->assertStringContainsString('<a href="https://notapplicable.app/entry/entrant/'.$this->grantReport->entry->slug.'/preview">', $view);
    }

    public function testDontDisplayPreviewActionsInSoftDeletedGrantReport()
    {
        $this->grantReport->deleted_at = now();
        $view = view('grant-report.entrant.search.action-overflow', ['grantReport' => $this->grantReport])->render();

        $this->assertStringNotContainsString('<a href="https://notapplicable.app/grant-report/entrant/'.$this->grantReport->slug.'/preview">', $view);
        $this->assertStringNotContainsString('<a href="https://notapplicable.app/entry/entrant/'.$this->grantReport->entry->slug.'/preview">', $view);
    }

    public function testDisplayEditAction()
    {
        $view = view('grant-report.entrant.search.action-overflow', ['grantReport' => $this->grantReport])->render();

        $this->assertStringContainsString(' <a href="https://notapplicable.app/grant-report/entrant/'.$this->grantReport->slug.'/preview">', $view);
    }

    public function testDontDisplayEditActionInArchivedEntry()
    {
        $this->grantReport->entry->archive();
        $view = view('grant-report.entrant.search.action-overflow', ['grantReport' => $this->grantReport])->render();

        $this->assertStringNotContainsString(' <a href="https://notapplicable.app/entry/entrant/'.$this->grantReport->entry->slug.'">', $view);
    }
}
