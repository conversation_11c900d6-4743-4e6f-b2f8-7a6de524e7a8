<?php

namespace Tests\Modules\Grants\Listeners;

use AwardForce\Modules\Accounts\Events\AccountWasCreated;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Grants\Commands\SeedDefaultGrantStatuses;
use AwardForce\Modules\Grants\Listeners\Account as AccountListener;
use Illuminate\Support\Facades\Bus;
use Tests\UnitTestCase;

final class AccountTest extends UnitTestCase
{
    /** @var AccountListener */
    private $listener;

    public function init()
    {
        Bus::fake();
        $this->listener = new AccountListener;
    }

    public function testDispatchesSeedDefaultGrantStatuses(): void
    {
        $event = new AccountWasCreated(new Account(['brand' => 'goodgrants']));
        $this->listener->whenAccountWasCreated($event);

        Bus::assertDispatched(SeedDefaultGrantStatuses::class);
    }

    public function testDoesNotDispatchesSeedDefaultGrantStatusesWhenNotGoodGrants(): void
    {
        $event = new AccountWasCreated(new Account());
        $this->listener->whenAccountWasCreated($event);

        Bus::assertNotDispatched(SeedDefaultGrantStatuses::class);
    }
}
