<?php

namespace Tests\Modules\Holocron\Services;

use AwardForce\Library\Localisation\CurrentLocaleService;
use AwardForce\Modules\Holocron\Models\HolocronModel;
use AwardForce\Modules\Holocron\Repositories\TranslationsRepository;
use AwardForce\Modules\Holocron\Services\HolocronTranslator;
use AwardForce\Modules\Holocron\Services\TranslationKeyExtractor;
use Mockery as m;
use Tests\IntegratedTestCase;

final class HolocronTranslatorTest extends IntegratedTestCase
{
    private CurrentLocaleService $currentLocale;
    private TranslationsRepository $translations;
    private TranslationKeyExtractor $translationKeyExtractor;
    private HolocronTranslator $service;

    public function init()
    {
        $this->currentLocale = app(CurrentLocaleService::class);
        $this->translations = m::mock(TranslationsRepository::class);
        $this->translationKeyExtractor = app(TranslationKeyExtractor::class);
        $this->service = new HolocronTranslator($this->currentLocale, $this->translations, $this->translationKeyExtractor);
    }

    public function testItTranslatesModel(): void
    {
        $model = $this->newModel([
            'name' => 'full_name',
            'content' => 'this_is_content',
        ]);

        $this->translations->shouldReceive('getTranslationsOfKeys')->andReturn(collect([
            'full_name' => 'Full NAME',
            'this_is_content' => 'This _is_ **CONTENT**!',
        ]));

        $model = $this->service->translate($model);

        $this->assertEquals([
            'name' => 'Full NAME',
            'content' => 'This _is_ **CONTENT**!',
            'release_id' => '66',
        ], $model->getAttributes());
    }

    private function newModel(array $attributes = [])
    {
        $model = new class extends HolocronModel
        {
            public function translatableAttributes(): array
            {
                return ['name', 'content'];
            }
        };

        $model->setRawAttributes($attributes, true);
        $model->releaseId = 66;

        return $model;
    }

    public function testItTranslatesModelWithOrStrings(): void
    {
        $model = $this->newModel([
            'name' => 'full_name',
            'content' => 'this_is_content',
        ]);

        $this->translations->shouldReceive('getTranslationsOfKeys')->andReturn(collect([
            'full_name' => 'Testing for, FOR, worn, WORN, and, sand',
            'this_is_content' => 'for, FOR, worn, WORN, and, sand',
        ]));

        $model = $this->service->translate($model);

        $this->assertEquals([
            'name' => 'Testing for, FOR, worn, WORN, and, sand',
            'content' => 'for, FOR, worn, WORN, and, sand',
            'release_id' => '66',
        ], $model->getAttributes());
    }

    public function testItTranslatesModelIntroGreek(): void
    {
        $model = $this->newModel([
            'name' => 'full_name',
            'content' => 'this_is_content',
        ]);

        $this->translations->shouldReceive('getTranslationsOfKeys')->andReturn(collect([
            'full_name' => 'Καλημέρα, είμαι ο Θάνος',
            'this_is_content' => 'Αυτό είναι ένα τεστ',
        ]));

        $model = $this->service->translate($model);

        $this->assertEquals([
            'name' => 'Καλημέρα, είμαι ο Θάνος',
            'content' => 'Αυτό είναι ένα τεστ',
            'release_id' => '66',
        ], $model->getAttributes());
    }
}
