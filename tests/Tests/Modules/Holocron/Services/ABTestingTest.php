<?php

namespace Tests\Modules\Holocron\Services;

use AwardForce\Modules\Holocron\Models\HolocronModel;
use AwardForce\Modules\Holocron\Services\ABTesting;
use Tests\LightUnitTestCase;

final class ABTestingTest extends LightUnitTestCase
{
    private ABTesting $abTesting;

    protected function init(): void
    {
        $this->abTesting = new ABTesting;
    }

    public function testItThrowsAnExceptionIfNoModelExists(): void
    {
        $this->expectException(\Exception::class);

        $this->abTesting->pickModel(collect());
    }

    public function testItReturnsTheOnlyModel(): void
    {
        $models = collect([$model = $this->newModel()]);

        $abModel = $this->abTesting->pickModel($models);

        $this->assertEquals($model, $abModel);
    }

    public function testItReturnsOneOfTheModels(): void
    {
        $models = collect([$this->newModel(), $this->newModel()]);

        $abModel = $this->abTesting->pickModel($models);

        $this->assertTrue($models->contains($abModel));
    }

    private function newModel()
    {
        return new class extends HolocronModel
        {
        };
    }
}
