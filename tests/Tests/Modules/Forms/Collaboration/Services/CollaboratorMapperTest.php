<?php

namespace Tests\Modules\Forms\Collaboration\Services;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Collaboration\Services\CollaboratorMapper;
use AwardForce\Modules\Identity\Users\Models\User;
use Tests\IntegratedTestCase;

class CollaboratorMapperTest extends IntegratedTestCase
{
    public function testItMapsCollaboratorProperly()
    {
        config(['awardforce.default-user-colors' => ['#ff8f8f']]);

        $submittable = $this->muffin(Entry::class);
        $this->muffin(Membership::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
        $collaborator = $this->muffin(Collaborator::class, ['submittable_id' => $submittable->id, 'submittable_type' => $submittable->getMorphClass(), 'user_id' => $user->id]);
        $expected = [
            'slug' => (string) $collaborator->slug,
            'profilePhoto' => [
                'image' => '',
                'fullName' => $collaborator->user->fullName(),
                'color' => '#ff8f8f',
                'initials' => $collaborator->user->initials,
            ],
            'firstName' => $collaborator->user->firstName,
            'lastName' => $collaborator->user->lastName,
            'fullName' => $collaborator->user->getName(),
            'email' => $collaborator->user->email,
            'wasInvited' => $collaborator->user->wasInvited,
            'privilege' => $collaborator->privilege->get(),
            'owner' => $collaborator->userId === $collaborator->submittable->getUserId(),
            'user' => (string) $collaborator->user->slug,
            'manager' => false,
            'initials' => $collaborator->user->initials,
        ];

        $this->assertEquals($expected, (new CollaboratorMapper($collaborator, $submittable))->toArray());
    }

    public function testProgramManagerRoleIsACollaboratorManager()
    {
        $user = $this->setupUserWithRole('Program manager');
        $submittable = $this->muffin(Entry::class);
        $collaborator = $this->muffin(Collaborator::class, ['submittable_id' => $submittable->id, 'submittable_type' => $submittable->getMorphClass(), 'user_id' => $user->id]);
        Consumer::set(new UserConsumer($user));

        $mappedCollaborator = (new CollaboratorMapper($collaborator, $submittable))->toArray();

        $this->assertTrue($mappedCollaborator['manager']);
    }

    public function testEntrantRoleIsNotACollaboratorManager()
    {
        $user = $this->setupUserWithRole('Entrant');
        $submittable = $this->muffin(Entry::class);
        $collaborator = $this->muffin(Collaborator::class, ['submittable_id' => $submittable->id, 'submittable_type' => $submittable->getMorphClass(), 'user_id' => $user->id]);
        Consumer::set(new UserConsumer($user));

        $mappedCollaborator = (new CollaboratorMapper($collaborator, $submittable))->toArray();

        $this->assertFalse($mappedCollaborator['manager']);
    }

    public function testSubmittableChapterManagerIsACollaboratorManager()
    {
        $chapterManager = $this->setupUserWithRole('Chapter manager');

        $assignedChapter = $this->muffin(Chapter::class);
        $assignedChapter->managers()->sync([$chapterManager->id]);

        $submittable = $this->muffin(Entry::class, ['chapter_id' => $assignedChapter->id]);
        $collaborator = $this->muffin(Collaborator::class, ['submittable_id' => $submittable->id, 'submittable_type' => $submittable->getMorphClass(), 'user_id' => $chapterManager->id]);
        Consumer::set(new UserConsumer($chapterManager));

        $mappedCollaborator = (new CollaboratorMapper($collaborator, $submittable))->toArray();

        $this->assertTrue($mappedCollaborator['manager']);
    }

    public function testSubmittableChapterManagerIsNotACollaboratorManager()
    {
        $chapterManager = $this->setupUserWithRole('Chapter manager');

        $assignedChapter = $this->muffin(Chapter::class);
        $assignedChapter->managers()->sync([$chapterManager->id]);

        $otherChapter = $this->muffin(Chapter::class);

        $submittable = $this->muffin(Entry::class, ['chapter_id' => $otherChapter->id]);
        $collaborator = $this->muffin(Collaborator::class, ['submittable_id' => $submittable->id, 'submittable_type' => $submittable->getMorphClass(), 'user_id' => $chapterManager->id]);
        Consumer::set(new UserConsumer($chapterManager));

        $mappedCollaborator = (new CollaboratorMapper($collaborator, $submittable))->toArray();

        $this->assertFalse($mappedCollaborator['manager']);
    }
}
