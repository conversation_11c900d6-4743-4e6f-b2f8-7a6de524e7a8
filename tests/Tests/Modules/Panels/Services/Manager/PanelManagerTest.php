<?php

namespace Tests\Modules\Panels\Services\Manager;

use AwardForce\Modules\Assignments\Validation\MaxAssignments;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Panels\Commands\CreatePanel;
use AwardForce\Modules\Panels\Commands\UpdatePanel;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Panels\Models\PanelRepository;
use AwardForce\Modules\Panels\Services\Manager\PanelManager;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Tags\Models\Tag;
use Mockery as m;
use Tests\IntegratedTestCase;

class PanelManagerTest extends IntegratedTestCase
{
    protected ScoreSet $scoreSet;
    protected MaxAssignments $validation;

    public function init()
    {
        $this->scoreSet = $this->muffin(ScoreSet::class);
    }

    public function testCanCreateRoleBasedPanel()
    {
        $panel = app(PanelManager::class)->process(
            new CreatePanel(
                app(PanelRepository::class),
                $this->season->id,
                $this->scoreSet->id,
                ['name' => ['en_GB' => $name = 'Test panel']],
                Panel::JUDGES_ROLES,
                true,
                true,
                Panel::TAGS_ANY,
                [$category = $this->muffin(Category::class)->id],
                [$chapter = $this->muffin(Chapter::class)->id],
                [$tag = $this->muffin(Tag::class)->id],
                [],
                [$role = $this->muffin(Role::class)->id],
                [],
                [],
                [$round = $this->muffin(Round::class)->id]
            )
        );

        $this->assertEquals(Panel::first()->id, $panel->id);

        $this->assertEquals(current_account_id(), $panel->accountId);
        $this->assertEquals($this->season->id, $panel->seasonId);
        $this->assertEquals($this->scoreSet->id, $panel->scoreSetId);
        $this->assertEquals($name, translate($panel)->name);
        $this->assertEquals(Panel::JUDGES_ROLES, $panel->appliedJudges);
        $this->assertTrue($panel->moderationEntries);
        $this->assertTrue($panel->archivedEntries);
        $this->assertEquals(Panel::TAGS_ANY, $panel->tagMatch);

        $this->assertEquals([$category], $panel->categories->just('id'));
        $this->assertEquals(1, $panel->categoryCount);

        $this->assertEquals([$chapter], $panel->chapters->just('id'));
        $this->assertEquals(1, $panel->chapterCount);

        $this->assertEquals([$tag], $panel->tags->just('id'));
        $this->assertEquals(1, $panel->tagCount);

        $this->assertEquals([$role], $panel->roles->just('id'));
        $this->assertEquals([$round], $panel->rounds->just('id'));
    }

    public function testCanCreateJudgeBasedPanel()
    {
        $panel = app(PanelManager::class)->process(
            new CreatePanel(
                app(PanelRepository::class),
                $this->season->id,
                $this->scoreSet->id,
                ['name' => ['en_GB' => $name = 'Test panel']],
                Panel::JUDGES_INVITED,
                true,
                true,
                Panel::TAGS_ANY,
                [],
                [],
                [],
                [$judge = $this->setupUserWithRole('Judge')->id],
                [],
                [],
                [],
                []
            )
        );

        $this->assertEquals(Panel::first()->id, $panel->id);

        $this->assertEquals(Panel::JUDGES_INVITED, $panel->appliedJudges);
        $this->assertEquals([$judge], $panel->judges->just('id'));
        $this->assertEquals(1, $panel->judgeCount);
    }

    public function testDoesNotCreatePanelIfValidationFails()
    {
        $this->app->instance(MaxAssignments::class, $maxAssignments = m::mock(MaxAssignments::class));
        $maxAssignments->shouldReceive('validatePanel')->andThrow(new \Exception('Validation failed'));

        try {
            app(PanelManager::class)->process(
                new CreatePanel(
                    app(PanelRepository::class),
                    $this->season->id,
                    $this->scoreSet->id,
                    [],
                    Panel::JUDGES_ROLES,
                    false,
                    false,
                    Panel::TAGS_ANY,
                    [],
                    [],
                    [],
                    [],
                    [],
                    [],
                    [],
                    []
                )
            );
        } catch (\Exception $e) {
        } finally {
            $this->assertEmpty(Panel::all());
        }
    }

    public function testCanUpdatePanel()
    {
        $panel = $this->muffin(Panel::class, [
            'scoreSetId' => $this->scoreSet->id,
            'appliedJudges' => Panel::JUDGES_INVITED,
            'moderationEntries' => false,
        ])->fresh();

        $panel = app(PanelManager::class)->process(
            new UpdatePanel(
                $panel,
                $newScoreSet = $this->muffin(ScoreSet::class)->id,
                ['name' => ['en_GB' => $name = 'Test panel']],
                Panel::JUDGES_ROLES,
                true,
                true,
                Panel::TAGS_ANY,
                [$category = $this->muffin(Category::class)->id],
                [$chapter = $this->muffin(Chapter::class)->id],
                [$tag = $this->muffin(Tag::class)->id],
                [],
                [$role = $this->muffin(Role::class)->id],
                [],
                [],
                [$round = $this->muffin(Round::class)->id]
            )
        );

        $this->assertEquals($newScoreSet, $panel->scoreSetId);
        $this->assertEquals($name, translate($panel)->name);
        $this->assertEquals(Panel::JUDGES_ROLES, $panel->appliedJudges);
        $this->assertTrue((bool) $panel->moderationEntries);
        $this->assertTrue($panel->archivedEntries);
        $this->assertEquals(Panel::TAGS_ANY, $panel->tagMatch);

        $this->assertEquals([$category], $panel->categories->just('id'));
        $this->assertEquals(1, $panel->categoryCount);

        $this->assertEquals([$chapter], $panel->chapters->just('id'));
        $this->assertEquals(1, $panel->chapterCount);

        $this->assertEquals([$tag], $panel->tags->just('id'));
        $this->assertEquals(1, $panel->tagCount);

        $this->assertEquals([$role], $panel->roles->just('id'));
        $this->assertEquals([$round], $panel->rounds->just('id'));
    }

    public function testDoesNotUpdatePanelIfValidationFails()
    {
        $this->app->instance(MaxAssignments::class, $maxAssignments = m::mock(MaxAssignments::class));
        $maxAssignments->shouldReceive('validatePanel')->andThrow(new \Exception('Validation failed'));

        $panel = $this->muffin(Panel::class, [
            'scoreSetId' => $this->scoreSet->id,
            'appliedJudges' => Panel::JUDGES_INVITED,
            'moderationEntries' => true,
        ])->fresh();

        try {
            app(PanelManager::class)->process(
                new UpdatePanel(
                    $panel,
                    $this->muffin(ScoreSet::class)->id,
                    [],
                    Panel::JUDGES_ROLES,
                    false,
                    false,
                    Panel::TAGS_ANY,
                    [],
                    [],
                    [],
                    [],
                    [],
                    [],
                    [],
                    []
                )
            );
        } catch (\Exception $e) {
        } finally {
            $panel->refresh();

            $this->assertEquals($this->scoreSet->id, $panel->scoreSetId);
            $this->assertEquals(Panel::JUDGES_INVITED, $panel->appliedJudges);
            $this->assertTrue((bool) $panel->moderationEntries);
        }
    }
}
