<?php

namespace AwardForce\Library\Search\Filters;

use Illuminate\Support\Facades\DB;
use Platform\Search\Filters\SearchFilter;

class MultiTranslationsKeywordFilter implements SearchFilter
{
    /**
     * @var string
     */
    private $resource;

    /**
     * @var array
     */
    private $fields;

    /**
     * @var null|string
     */
    private $keyword;

    /**
     * MultiTranslationsKeywordFilter constructor.
     *
     * @param  string  $resource
     * @param  array  $fields
     * @param  null|string  $keyword
     */
    public function __construct($resource, $fields = [], $keyword = null)
    {
        $this->resource = $resource;
        $this->fields = $fields;
        $this->keyword = $keyword;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! empty($this->keyword) && ! empty($this->fields)) {
            $this->addJoin($query);
            $query->where("{$this->alias()}.value", 'LIKE', "%{$this->keyword}%");
        }

        return $query;
    }

    /**
     * Alters the query object, adding the translations join and filtering by the relevant search query.
     *
     * @param  Query  $query
     * @return Query
     */
    private function addJoin($query)
    {
        $alias = $this->alias();
        $from = $query->getQuery()->from;

        $query->join(DB::raw("translations $alias"), function ($join) use ($from, $alias) {
            $join->on("$alias.foreign_id", '=', "$from.id")
                ->where("$alias.resource", '=', $this->resource)
                ->whereIn("$alias.field", $this->fields);
        });

        return $query;
    }

    /**
     * Creates the required table alias.
     *
     * @return string
     */
    private function alias()
    {
        return 'k';
    }
}
