<?php

namespace AwardForce\Library\Database\Firebase;

use AwardForce\Library\Database\Firebase\Database as FirebaseDatabase;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;
use Psr\Log\LoggerInterface;

class FirebaseServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->scoped(FirebaseDatabase::class, fn() => new LoggerProxy(
            $this->app->make(Firestore::class),
            $this->app->make(LoggerInterface::class)
        ));
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [
            FirebaseDatabase::class,
        ];
    }
}
