<?php

namespace AwardForce\Library\Validation;

use Platform\Database\Repository;

class CircularReference
{
    /** @var Repository */
    protected $repository;

    public function __construct(Repository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Detects if setting a relationship between the given child and parent ids would create a circular reference
     * between the two objects.
     */
    public function detect(string $referencingField, int $childId, int $parentId): bool
    {
        while ($parentId) {
            if ($childId == $parentId) {
                return true;
            }

            $parentId = $this->repository->getById($parentId)->{$referencingField} ?? null;
        }

        return false;
    }
}
