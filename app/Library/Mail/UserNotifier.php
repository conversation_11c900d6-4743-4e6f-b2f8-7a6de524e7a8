<?php

namespace AwardForce\Library\Mail;

use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Services\Recipients\SmsRecipient;
use AwardForce\Modules\Notifications\Services\SmsSender;
use Facades\Platform\Strings\Output;
use Illuminate\Contracts\Mail\Mailer as IlluminateMailer;
use Illuminate\Mail\Message;
use Platform\Support\Values\PhoneNumber;

class UserNotifier
{
    const MAIL_TEMPLATE = 'emails.templates.main';

    /**
     * @var IlluminateMailer
     */
    private $mailer;

    /**
     * @var SmsSender
     */
    private $sms;

    public function __construct(IlluminateMailer $mailer, SmsSender $sms)
    {
        $this->mailer = $mailer;
        $this->sms = $sms;
    }

    public function notify(User $user, string $emailSubject, string $emailContent, string $mobileContent)
    {
        if ($user->email) {
            $this->notifyEmail($user, $emailSubject, $emailContent);
        } elseif ($user->mobile) {
            $this->notifyMobile($user, $mobileContent);
        }
    }

    private function notifyEmail(User $user, string $subject, string $content)
    {
        $this->mailer->send(self::MAIL_TEMPLATE, [
            'content' => Output::html($content),
        ], function (Message $message) use ($user, $subject) {
            $message->to($user->email, $user->getName())->subject($subject);
        });
    }

    private function notifyMobile(User $user, string $content)
    {
        $this->sms->send(new SmsRecipient(new PhoneNumber($user->mobile)), $content, []);
    }
}
