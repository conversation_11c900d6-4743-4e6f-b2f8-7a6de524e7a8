<?php

namespace AwardForce\Library\Middleware;

use Closure;

class ThrottleRequests extends \Illuminate\Routing\Middleware\ThrottleRequests
{
    public function handle(
        $request,
        Closure $next,
        $maxAttempts = 60,
        $decayMinutes = 1,
        $prefix = ''
    ) {
        if (throttling_disabled()) {
            return $next($request);
        }

        if (! is_numeric($maxAttempts)) {
            return parent::handle($request, $next, $maxAttempts);
        }

        return parent::handle($request, $next, $maxAttempts, $decayMinutes, $prefix);
    }
}
