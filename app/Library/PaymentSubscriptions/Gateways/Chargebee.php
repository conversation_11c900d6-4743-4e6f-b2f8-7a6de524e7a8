<?php

namespace AwardForce\Library\PaymentSubscriptions\Gateways;

use AwardForce\Library\PaymentSubscriptions\Contracts\PaymentSubscriptionGateway;
use AwardForce\Library\PaymentSubscriptions\Data\TaxItem;
use AwardForce\Library\PaymentSubscriptions\Exceptions\CustomerException;
use AwardForce\Library\PaymentSubscriptions\Exceptions\PaymentSubscriptionException;
use AwardForce\Library\PaymentSubscriptions\Exceptions\SubscriptionException;
use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use Chargebee\ChargebeeClient;
use Chargebee\Resources\Card\Card;
use Chargebee\Resources\Customer\Customer;
use Chargebee\Resources\Invoice\Invoice;
use Chargebee\Resources\Invoice\LineItem;
use Chargebee\Resources\ItemPrice\ItemPrice;
use Chargebee\Resources\Subscription\Subscription;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Throwable;

class Chargebee implements PaymentSubscriptionGateway
{
    /** @var Customer */
    public $customer;

    /** @var Subscription */
    public $subscription;

    /** @var array */
    protected $billing;

    /** @var string */
    protected $cardId;

    /** @var Invoice */
    protected $invoice;

    /** @var Card */
    protected $card;

    protected string $verticalProvider;
    protected ChargebeeClient $client;

    /**
     * @throws Exception
     */
    public function __construct(
        protected array $config,
        ?ChargebeeClient $chargebeeClient = null
    ) {
        $this->client = $chargebeeClient ?? new ChargebeeClient(options: [
            'site' => array_get($this->config, 'site'),
            'apiKey' => array_get($this->config, 'full-access-key'),
        ]);
    }

    public function addSubscriptionByIntent(array $parameters = []): array
    {
        ['product' => $product, 'paymentIntent' => $paymentIntent] = $parameters;

        return $this->createSubscription($this->makeSubscriptionData($product, $paymentIntent))?->toArray();
    }

    /**
     * @throws SubscriptionException
     */
    protected function createSubscription(array $parameters = []): Subscription
    {
        try {
            $customerId = array_get($parameters, 'customerId');
            unset($parameters['customerId']);

            $existingSubscription = $this->getSubscriptionById($subscriptionId = current_account_subscription_id());

            if ($existingSubscription) {
                $this->cancelSubscription($subscriptionId);
            }
            $result = $this->client->subscription()->createWithItems($customerId, $parameters);

            $this->subscription = $result->subscription;
            $this->invoice = $result->invoice;

            return $this->subscription;
        } catch (Exception $e) {
            throw (new SubscriptionException($e->getMessage()));
        }
    }

    protected function getSubscription(?string $subscriptionReference = null): ?Subscription
    {
        if ($subscriptionReference === null) {
            return null;
        }

        try {
            return $this->client->subscription()->retrieve(current_account_subscription_id())?->subscription;
        } catch (Exception $e) {
            return null;
        }
    }

    public function getCustomerBySubscriptionId(string|int $subscriptionId): ?Customer
    {
        try {
            /** @var Subscription $subscription */
            $subscription = $this->getSubscription($subscriptionId);

            if ($subscription?->customer_id) {
                return $this->getRemoteCustomer($subscription->customer_id);
            }
        } catch (Exception $e) {
        }

        return null;
    }

    /**
     * @throws Throwable
     */
    protected function makeSubscriptionData($product, array $paymentIntent = []): array
    {
        $price = $this->getPrice($product);
        throw_if(! $price, new PaymentSubscriptionException('The selected price is undefined. '.$product));
        $priceId = array_get($price, 'id');

        $subscriptionData = [
            'subscriptionItems' => [
                [
                    'itemPriceId' => $priceId,
                ],
            ],
            'metaData' => [
                'account_name' => current_account()->name,
                'account_slug' => (string) current_account()->slug,
            ],
            'cf_falcon_id' => current_account_falcon_id(),
            'cf_global_id' => (string) current_account_global_id(),
            'cf_hs_deal_id' => current_account()->dealId,
            'cf_account_url' => current_account_url(),
            'customerId' => $this->customer?->id,
            'invoiceImmediately' => true,
        ];

        if (! empty($paymentIntent)) {
            $result = $this->client->paymentSource()->createUsingPaymentIntent([
                'customer_id' => $this->customer->id,
                'payment_intent' => [
                    'id' => array_get($paymentIntent, 'id'),
                ],
            ]);
            $paymentSource = $result->payment_source;
            $subscriptionData['paymentSourceId'] = $paymentSource->id;
        } else {
            $subscriptionData['auto_collection'] = 'off';
        }

        return $subscriptionData;
    }

    public function getPrice($product): ?array
    {
        $products = $this->fetchProducts();

        if ($price = array_get($products, $product)) {
            return $price;
        }

        return null;
    }

    public function fetchProducts(): array
    {
        $products = array_keys(Config::get('products.available'));

        return $this->remember('chargebee-prices', function () use ($products) {
            $offset = 0;
            $itemPrices = [];
            do {
                $listItemPriceResponse = $this->client->itemPrice()->all([
                    'limit' => 100,
                    'status[is]' => 'active',
                    'itemType[is]' => 'plan',
                    'offset' => $offset,
                ]);

                foreach (array_get($listItemPriceResponse->toArray(), 'list') as $result) {
                    $itemPrices[] = $result->item_price;
                }
            } while ($offset = array_get($listItemPriceResponse->toArray(), 'next_offset'));

            return collect($itemPrices)
                ->filter(fn(ItemPrice $price) => in_array($price->name, $products))
                ->mapWithKeys(function (ItemPrice $price) {
                    return [
                        $price->name => [
                            'id' => $price->id,
                            'amountInCents' => $price->price,
                            'amount' => (new Amount($price->price, new Currency(strtoupper($price->currency_code))))->valueFromCents(),
                            'currencyCode' => $price->currency_code,
                        ],
                    ];
                })
                ->toArray();
        });
    }

    private function remember(string $key, callable $callback)
    {
        return Cache::remember($key, now()->addMinutes(30), function () use ($callback) {
            return $callback();
        });
    }

    public function deleteCustomer(string $customerId): bool
    {
        try {
            $subscriptionCustomer = consumer()->user()->subscriptionCustomer;

            // If exists customer with same customer id , prevent from deleting
            if ($subscriptionCustomer && $subscriptionCustomer->customer_id === $customerId) {
                return false;
            }

            $this->client->customer()->delete($customerId);

            return true;
        } catch (Exception $exception) {
            return false;
        }
    }

    /**
     * @throws CustomerException
     */
    public function createOrUpdateCustomer(array $data = []): array
    {
        ['customerData' => $customerData, 'billing' => $billing] = $data;

        $parameters = $this->makeCustomerData($customerData, $billing);

        $this->customer = $this->getRemoteCustomer($customerData['id'] ?? null);

        if (! empty($this->customer)) {
            $this->updateCustomer($this->customer->id, $parameters);
        } else {
            $this->createCustomer($parameters);
        }

        consumer()->user()->addSubscriptionCustomer(
            class_basename($this),
            $this->getCustomerId(),
            $this->getCustomerCurrency()
        );

        return $this->customer->toArray();
    }

    protected function makeCustomerData($customerData, $billing): array
    {
        return [
            'billingAddress' => $billing,
            'company' => array_get($customerData, 'company'),
            'locale' => array_get($customerData, 'locale', get_consumer_locale_for_chargebee()),
            'cf_global_user_id' => array_get($customerData, 'global_user_id'),
            'preferred_currency_code' => array_get($customerData, 'currency'),
            'vatNumber' => array_get($customerData, 'vatNumber'),
            'taxability' => 'taxable',
        ];
    }

    public function getRemoteCustomer(?string $customerId = null): ?Customer
    {
        try {
            $customer = null;

            if (! is_null($customerId) || ! empty(current_account_subscription_customer_id())) {
                $customer = $this->client->customer()->retrieve($customerId ?? current_account_subscription_customer_id())->customer;
            }

            if (! $customer) {
                $customer = $this->getCustomerBySubscriptionId(current_account_subscription_id());
            }

            if (isset($customer->id) && ! $customer->deleted) {
                return $customer;
            }
        } catch (Exception $e) {
        }

        return null;
    }

    /**
     * @throws CustomerException
     */
    protected function updateCustomer($customerId, array $parameters = []): Customer
    {
        try {
            $billingAddressParameters = Arr::only($parameters, 'billingAddress');
            $billingAddressParameters['vatNumber'] = $parameters['vatNumber'];
            $this->client->customer()->updateBillingInfo($customerId, $billingAddressParameters);

            return $this->customer = $this->client->customer()->update($customerId, $parameters)->customer;
        } catch (Exception $e) {
            throw (new CustomerException($e->getMessage()));
        }
    }

    /**
     * @throws CustomerException
     */
    protected function createCustomer(array $parameters = []): Customer
    {
        try {
            $this->customer = $this->client->customer()->create($parameters)->customer;

            return $this->customer;
        } catch (Exception $e) {
            throw (new CustomerException($e->getMessage()));
        }
    }

    /**
     * @throws CustomerException
     */
    public function findAndUpdateCustomer(array $parameters = []): array
    {
        try {
            ['paymentIntent' => $paymentIntent, 'customerParameters' => $customerParameters] = $parameters;
            $this->customer = $this->getRemoteCustomer(array_get($paymentIntent, 'customer_id'));

            $this->updateCustomer($this->customer->id, $customerParameters);

            return $this->customer->toArray();
        } catch (Exception $e) {
            throw (new CustomerException($e->getMessage().' - '.json_encode($parameters)));
        }
    }

    public function getSubscriptionId(): null|string|int
    {
        return $this->subscription->id ?? null;
    }

    public function getSubscriptionStatus(): ?string
    {
        return $this->subscription->status->value ?? null;
    }

    public function getCustomerId(): null|string|int
    {
        return $this->customer->id ?? null;
    }

    public function getCustomerCurrency(): ?string
    {
        return $this->customer->preferred_currency_code ?? null;
    }

    public function getCardId(): null|string|int
    {
        return $this->cardId ?? null;
    }

    public function getBilling(): ?array
    {
        return $this->billing ?? null;
    }

    public function getSubtotal(): float|int
    {
        return $this->invoice->sub_total ?? 0 / 100;
    }

    public function getTotal(): float|int
    {
        return $this->invoice->total ?? 0 / 100;
    }

    public function getVatPercentage(): int
    {
        if ($this->invoice === null) {
            return false;
        }

        /** @var LineItem $lineItem */
        $lineItem = collect($this->invoice->line_items)->first();

        return $lineItem && $lineItem->is_taxed
            ? $lineItem->tax_rate
            : 0;
    }

    public function fetchTaxItem($jurisdiction): TaxItem
    {
        return new TaxItem();
    }

    public function getSubscriptionById($subscriptionId): ?array
    {
        try {
            return $this->client->subscription()->retrieve($subscriptionId)->subscription->toArray();
        } catch (\Exception $exception) {
            return null;
        }
    }

    public function getPaymentMethodById($paymentMethodId): ?array
    {
        try {
            return $this->client->paymentSource()->retrieve($paymentMethodId)->payment_source->toArray();
        } catch (\Exception $exception) {
            return null;
        }
    }

    public function getProductPrice($product): null|int|float
    {
        $products = $this->fetchProducts();

        if ($price = array_get($products, $product)) {
            return $price['amount'];
        }

        return null;
    }

    public function createPaymentIntent(array $parameters = []): array
    {
        $customerId = array_get($parameters, 'customerId');
        $currencyCode = array_get($parameters, 'currencyCode');
        $amount = array_get($parameters, 'amount');

        return $this->client->paymentIntent()->create([
            'customerId' => $customerId,
            'currencyCode' => $currencyCode,
            'amount' => $amount,
        ])
            ->payment_intent
            ->toArray();
    }

    /**
     * @throws Exception
     */
    public function getItemEstimation(array $parameters = []): array
    {
        $customerId = array_get($parameters, 'customerId');

        return $this->client->estimate()->createSubItemForCustomerEstimate(
            $customerId,
            array_merge(Arr::only($parameters, ['billingAddress', 'subscriptionItems']), [
                'invoiceImmediately' => true,
            ])
        )->estimate->toArray();
    }

    public function getVerticalProvider(): string
    {
        return $this->verticalProvider;
    }

    public function setVerticalProvider(string $verticalProvider): Chargebee
    {
        $this->verticalProvider = $verticalProvider;

        return $this;
    }

    public function cancelSubscription(?string $subscriptionId): void
    {
        try {
            $this->client->subscription()->cancelForItems($subscriptionId, [
                'cancel_reason_code' => 'Trial: Closed won',
            ]);
        } catch (Exception $e) {
            Log::warning('Unable to cancel existing subscription '.$e->getMessage(), ['subscriptionId' => $subscriptionId]);
        }
    }
}
