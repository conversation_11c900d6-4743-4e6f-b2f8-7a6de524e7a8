<?php

namespace AwardForce\Library\AIAgents\ValueObjects;

use Illuminate\Contracts\Support\Arrayable;

readonly class TokenUsage implements Arrayable
{
    public function __construct(
        public int $promptTokens,
        public int $completionTokens,
    ) {
    }

    public function toArray(): array
    {
        return [
            'prompt_tokens' => $this->promptTokens,
            'completion_tokens' => $this->completionTokens,
        ];
    }
}
