<?php

namespace AwardForce\Library\AIAgents\Enums;

use RuntimeException;

enum Model: string
{
    case Claude35Sonnet = 'claude_3.5_sonnet';
    case Claude35SonnetV2 = 'claude_3.5_sonnet_v2';
    case Claude37Sonnet = 'claude_3.7_sonnet';
    case ClaudeSonnet4 = 'claude_sonnet_4';
    case DeepSeekR1 = 'deepseek_r1';
    case MistralLarge = 'mistral_large';
    case MistralLarge2 = 'mistral_large_2';

    public function provider(): Provider
    {
        return Provider::Bedrock;
    }

    public function modelId(): string
    {
        return array_get($this->metadata(), 'modelId');
    }

    public function regionalModelId(string $region): string
    {
        if (! in_array($region, array_get($this->metadata(), 'supportedRegions'), true)) {
            throw new RuntimeException("Model {$this->name} is not available in {$region} region");
        }

        $modelId = $this->modelId();

        if ($this->hasCrossRegionSupport($region)) {
            $modelId = array_get($this->regionMapping(), $region, $region).".$modelId";
        }

        return $modelId;
    }

    public function metadata(): array
    {
        return match ($this) {
            self::Claude35Sonnet => [
                'modelId' => 'anthropic.claude-3-5-sonnet-20240620-v1:0',
                'supportedRegions' => ['eu', 'us'],
                'crossRegions' => ['eu', 'us'],
            ],
            self::Claude35SonnetV2 => [
                'modelId' => 'anthropic.claude-3-5-sonnet-20241022-v2:0',
                'supportedRegions' => ['au', 'us'],
                'crossRegions' => ['us'],
            ],
            self::Claude37Sonnet => [
                'modelId' => 'anthropic.claude-3-7-sonnet-20250219-v1:0',
                'supportedRegions' => ['eu', 'us'],
                'crossRegions' => ['eu', 'us'],
            ],
            self::ClaudeSonnet4 => [
                'modelId' => 'anthropic.claude-sonnet-4-20250514-v1:0',
                'supportedRegions' => ['eu', 'us'],
                'crossRegions' => ['eu', 'us'],
            ],
            self::DeepSeekR1 => [
                'modelId' => 'deepseek.r1-v1:0',
                'supportedRegions' => ['us'],
                'crossRegions' => ['us'],
            ],
            self::MistralLarge => [
                'modelId' => 'mistral.mistral-large-2402-v1:0',
                'supportedRegions' => ['eu', 'au', 'us', 'ca'],
                'crossRegions' => [],
            ],
            self::MistralLarge2 => [
                'modelId' => 'mistral.mistral-large-2407-v1:0',
                'supportedRegions' => ['us'],
                'crossRegions' => [],
            ],
        };
    }

    /**
     * Some regions have a different naming convention from our own, or even differ from AWS' own region codes. In
     * these cases, we need to store this map here, so that we can easily lookup the AWS region for the app's
     * region.
     */
    private function regionMapping(): array
    {
        return [
            'au' => 'apac',
            'hk' => 'apac',
        ];
    }

    /**
     * Returns true if the region has cross-region support. When this is true, we want to defer to cross-region use
     * of the models, rather than use them directly. This is meant to result in better and faster experience with
     * the Bedrock models.
     */
    private function hasCrossRegionSupport(string $region): bool
    {
        return in_array($region, array_get($this->metadata(), 'crossRegions'), true);
    }

    /**
     * @return Model[]
     */
    public static function casesFor(string $region): array
    {
        return collect(self::cases())
            ->filter(static fn(Model $model) => $model->availableIn($region))
            ->values()
            ->all();
    }

    public function availableIn(string $region): bool
    {
        return in_array($region, array_get($this->metadata(), 'supportedRegions'), true);
    }
}
