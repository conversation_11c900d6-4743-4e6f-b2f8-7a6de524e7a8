<?php

namespace AwardForce\Library\AIAgents\Implementations;

use AwardForce\Library\AIAgents\Contracts\TextGenerator;
use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Library\AIAgents\ValueObjects\Response;
use AwardForce\Library\AIAgents\ValueObjects\TokenUsage;
use Illuminate\Container\Attributes\Config;
use Prism\Prism\Prism;
use Prism\Prism\Text\Response as PrismResponse;

readonly class PrismTextGenerator implements TextGenerator
{
    public function __construct(
        #[Config('prism.providers.bedrock.region_code')] public string $region,
    ) {
    }

    public function prompt(Prompt $prompt): Response
    {
        try {
            $builder = Prism::text()
                ->using($prompt->model->provider()->value, $prompt->model->regionalModelId($this->region));

            if ($prompt->systemPrompt !== null) {
                $builder->withSystemPrompt($prompt->systemPrompt);
            }

            return $this->createResponse($builder->withPrompt($prompt->userPrompt)->asText());
        } catch (\Exception $e) {
            return Response::error($e->getMessage());
        }
    }

    private function createResponse(PrismResponse $response): Response
    {
        return new Response(
            text: $response->text,
            tokenUsage: new TokenUsage($response->usage->promptTokens, $response->usage->completionTokens)
        );
    }
}
