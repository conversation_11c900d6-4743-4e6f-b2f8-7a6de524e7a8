<?php

namespace AwardForce\Library\AIAgents\Implementations;

use AwardForce\Library\AIAgents\Enums\Model;
use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Library\AIAgents\ValueObjects\Response;
use AwardForce\Library\AIAgents\ValueObjects\TokenUsage;
use Prism\Prism\Enums\FinishReason;
use Prism\Prism\Prism;
use Prism\Prism\Text\Response as PrismResponse;
use Prism\Prism\ValueObjects\Meta;
use Prism\Prism\ValueObjects\Usage as PrismUsage;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class PrismTextGeneratorTest extends BaseTestCase
{
    use Laravel;

    public function testGenerateUsesCorrectModel(): void
    {
        $fakeResponse = new PrismResponse(
            steps: collect([]),
            responseMessages: collect([]),
            text: 'Provider and model test response',
            finishReason: FinishReason::Stop,
            toolCalls: [],
            toolResults: [],
            usage: new PrismUsage(8, 12),
            meta: new Meta('fake-3', 'fake-model'),
            messages: collect([]),
            additionalContent: []
        );

        $fake = Prism::fake([$fakeResponse]);

        $generator = app(PrismTextGenerator::class);

        $generator->prompt(new Prompt(
            model: Model::Claude37Sonnet,
            userPrompt: 'Test provider and model'
        ));

        $fake->assertRequest(function ($requests) {
            $this->assertEquals(Model::Claude37Sonnet->regionalModelId('us'), $requests[0]->model());
        });
    }

    public function testGenerateReturnsExpectedResponse(): void
    {
        $fakeResponse = new PrismResponse(
            steps: collect([]),
            responseMessages: collect([]),
            text: 'This is a test response',
            finishReason: FinishReason::Stop,
            toolCalls: [],
            toolResults: [],
            usage: new PrismUsage(10, 20),
            meta: new Meta('fake-1', 'fake-model'),
            messages: collect([]),
            additionalContent: []
        );

        Prism::fake([$fakeResponse]);

        $generator = app(PrismTextGenerator::class);

        $response = $generator->prompt(new Prompt(
            model: Model::Claude37Sonnet,
            userPrompt: 'Test prompt',
            systemPrompt: 'Test system prompt'
        ));

        $this->assertInstanceOf(Response::class, $response);
        $this->assertTrue($response->successful());
        $this->assertFalse($response->failed());
        $this->assertNull($response->error);
        $this->assertEquals('This is a test response', $response->text);
        $this->assertInstanceOf(TokenUsage::class, $response->tokenUsage);
        $this->assertEquals(10, $response->tokenUsage->promptTokens);
        $this->assertEquals(20, $response->tokenUsage->completionTokens);
    }

    public function testGenerateWithoutSystemPrompt(): void
    {
        $fakeResponse = new PrismResponse(
            steps: collect([]),
            responseMessages: collect([]),
            text: 'Response without system prompt',
            finishReason: FinishReason::Stop,
            toolCalls: [],
            toolResults: [],
            usage: new PrismUsage(5, 15),
            meta: new Meta('fake-2', 'fake-model'),
            messages: collect([]),
            additionalContent: []
        );

        Prism::fake([$fakeResponse]);

        $generator = app(PrismTextGenerator::class);

        $response = $generator->prompt(new Prompt(
            model: Model::Claude37Sonnet,
            userPrompt: 'Another test prompt'
        ));

        $this->assertInstanceOf(Response::class, $response);
        $this->assertTrue($response->successful());
        $this->assertEquals('Response without system prompt', $response->text);
        $this->assertInstanceOf(TokenUsage::class, $response->tokenUsage);
        $this->assertEquals(5, $response->tokenUsage->promptTokens);
        $this->assertEquals(15, $response->tokenUsage->completionTokens);
    }
}
