<?php

namespace AwardForce\Modules\Agreements\Models;

use AwardForce\Library\Database\Eloquent\Repository;
use Illuminate\Support\Collection;

class EloquentAgreementRepository extends Repository implements AgreementRepository
{
    public function __construct(Agreement $model)
    {
        $this->model = $model;
    }

    /**
     * Returns the agreement matching the user and type.
     *
     * @param  int  $userId
     * @param  string  $type
     * @param  int  $seasonId
     * @return Agreement
     */
    public function getSignedForUser($userId, $type, $seasonId = null)
    {
        return $this->getQuery()
            ->when($seasonId, function ($query) use ($seasonId) {
                $query->whereSeasonId($seasonId);
            })
            ->whereUserId($userId)
            ->whereType($type)
            ->whereNotNull('signed_at')
            ->whereNull('reset_at')
            ->first();
    }

    /**
     * Returns a signed agreement matching the user and score set.
     *
     * @return mixed
     */
    public function getSignedForUserAndScoreSet($userId, $scoreSetId)
    {
        return $this->getQuery()
            ->where('type', Agreement::TYPE_JUDGING)
            ->whereUserId($userId)
            ->whereScoreSetId($scoreSetId)
            ->whereNotNull('signed_at')
            ->whereNull('reset_at')
            ->first();
    }

    /**
     * Checks if there exists a signed agreement for the specified user and type.
     *
     * @param  int  $userId
     * @param  string  $type
     * @param  int  $seasonId
     * @return bool
     */
    public function hasAgreement($userId, $type, $seasonId = null)
    {
        $count = $this->getQuery()
            ->when($seasonId, function ($query) use ($seasonId) {
                $query->whereSeasonId($seasonId);
            })
            ->whereUserId($userId)
            ->whereType($type)
            ->whereNotNull('signed_at')
            ->whereNull('reset_at')
            ->count();

        return $count > 0;
    }

    /**
     * Returns all of the agreements for the user.
     *
     * @param  int  $userId
     * @return Collection
     */
    public function getAllForUser($userId)
    {
        return $this->getQuery()
            ->whereUserId($userId)
            ->where(function ($clause) {
                $clause->where('type', '!=', Agreement::TYPE_JUDGING);
                $clause->orWhere(function ($clause) {
                    $clause->whereType(Agreement::TYPE_JUDGING);
                    $clause->where(function ($clause) {
                        $clause->whereNotNull('score_set_id');
                        $clause->orWhereNotNull('reset_at');
                    });
                });
            })
            ->with('scoreSet', 'season')
            ->orderBy('signed_at', 'desc')
            ->get();
    }
}
