<?php

namespace AwardForce\Modules\Broadcasts\Requests;

use AwardForce\Library\Http\FormRequest;

class CreateBroadcastRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'senderName' => 'string',
            'senderAddress' => 'email:filter',
            'translated' => 'required|translation_required:subject,body',
            'dueDate' => 'required_if:scheduleOption,schedule|date',
            'dueTimezone' => 'nullable|string',
        ];
    }
}
