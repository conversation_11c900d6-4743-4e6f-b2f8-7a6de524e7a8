<?php

namespace AwardForce\Modules\Broadcasts\Search\Columns;

use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;

class SentTime implements Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return trans('broadcasts.table.sent');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'broadcast.sent_at';
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect([]);
    }

    /**
     * The field name is the field the value relates to in the translations table, such as "title" or "description".
     */
    public function field(): string
    {
        return 'broadcasts.sent_at';
    }

    /**
     * Return the value required in $broadcast.
     *
     * @return mixed
     */
    public function value($broadcast)
    {
        return $broadcast->sentAt;
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return Htmlable|string
     */
    public function html($broadcast)
    {
        return $broadcast->sentAt ?
            \HTML::dateTimezone($broadcast->sentAt, \Consumer::timezone(), \Consumer::dateLocale()) : '—';
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('search');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return SeasonFilter::viewingAll() ? 5 : 4;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }
}
