<?php

namespace AwardForce\Modules\Broadcasts\Models;

use AwardForce\Modules\Entries\Models\Entry;
use Illuminate\Support\Facades\Config;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Concerns\Passport;

final class BroadcastTest extends BaseTestCase
{
    use Database;
    use Laravel;
    use Passport;

    public function testFiltersWithSeason(): void
    {
        $broadcast = $this->muffin(Broadcast::class, [
            'filters' => function () {
                return ['category' => 1, 'status' => Entry::MODERATION_STATUS_APPROVED];
            }]);

        $this->assertCount(2, $broadcast->filters);

        $this->assertCount(4, $broadcast->filtersWithSeason());
        $this->assertArrayHasKey('seasonId', $broadcast->filtersWithSeason());
        $this->assertArrayHasKey('season', $broadcast->filtersWithSeason());
    }

    public function testFiltersAllSeason(): void
    {
        $broadcast = $this->muffin(Broadcast::class, [
            'filters' => function () {
                return ['category' => 1, 'status' => Entry::MODERATION_STATUS_APPROVED, 'season' => 'all'];
            }]);

        $this->assertCount(3, $broadcast->filters);

        $this->assertCount(3, $broadcast->filtersWithSeason());
        $this->assertArrayNotHasKey('seasonId', $broadcast->filtersWithSeason());
        $this->assertArrayHasKey('season', $broadcast->filtersWithSeason());
    }

    public function testLocksFilter(): void
    {
        Config::set('broadcasts.locked_filters.qualifying-leaderboard', ['filter']);

        $broadcast1 = $this->muffin(Broadcast::class, ['type' => 'qualifying-leaderboard']);
        $broadcast2 = $this->muffin(Broadcast::class, ['type' => 'assignments']);

        $this->assertTrue($broadcast1->locksFilter('filter'));
        $this->assertFalse($broadcast2->locksFilter('filter'));
    }

    public function testConsolidatesIfRequired(): void
    {
        Config::set('broadcasts.optional_consolidation', ['some-type']);

        $broadcast = $this->muffin(Broadcast::class, ['type' => 'some-type', 'consolidateRecipients' => true]);

        $this->assertTrue($broadcast->shouldConsolidate());
    }

    public function testDoesNotConsolidateIfNotRequired(): void
    {
        Config::set('broadcasts.optional_consolidation', ['some-type']);

        $broadcast = $this->muffin(Broadcast::class, ['type' => 'some-type', 'consolidateRecipients' => false]);

        $this->assertFalse($broadcast->shouldConsolidate());
    }

    public function testConsolidatesIfNotOptional(): void
    {
        Config::set('broadcasts.optional_consolidation', []);

        $broadcast = $this->muffin(Broadcast::class, ['type' => 'some-type', 'consolidateRecipients' => false]);

        $this->assertTrue($broadcast->shouldConsolidate());
    }

    public function testFiltersAreNotTruncated(): void
    {
        $text = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Morbi semper sapien id
                ullamcorper tempor. Praesent nec dictum felis, sed interdum libero. Fusce quam neque, viverra a pulvinar
                at, congue eget tellus. Maecenas gravida, justo eget sollicitudin lacinia, libero tortor ultricies mi,
                ut condimentum nunc metus eget ex. Aenean vehicula mollis blandit.';

        $broadcast = $this->muffin(Broadcast::class, [
            'filters' => function () use ($text) {
                return ['status' => $text];
            },
        ]);

        $this->assertEquals($text, $broadcast->fresh()->filters['status']);
    }

    public function testFiltersPaginationFilters(): void
    {
        $broadcast = $this->muffin(Broadcast::class, [
            'filters' => function () {
                return [
                    'order' => 'some.column',
                    'dir' => 'asc',
                    'per_page' => '100',
                    '_pjax' => '#',
                    'category' => 'some.category',
                ];
            }]);

        $this->assertCount(1, $filters = $broadcast->filters);
        $this->assertEquals('some.category', $filters['category']);
    }

    public function testItPopulatesMergeFieldsProperlyOnGrants(): void
    {
        current_account()->vertical = 'grants';
        current_account()->save();

        $broadcast = $this->muffin(Broadcast::class, [
            'type' => 'entrants-with-entries',
        ]);

        $mergeFields = $broadcast->mergeFields();

        $this->assertEquals('grants', current_account()->vertical);
        $this->assertContains('application_name', $mergeFields);
        $this->assertContains('application_slug', $mergeFields);
        $this->assertContains('application_local_id', $mergeFields);
        $this->assertNotContains('entry_name', $mergeFields);
        $this->assertNotContains('entry_slug', $mergeFields);
        $this->assertNotContains('entry_local_id', $mergeFields);
    }

    public function testItPopulatesMergeFieldsProperlyOnAwards(): void
    {
        $broadcast = $this->muffin(Broadcast::class, [
            'type' => 'entrants-with-entries',
        ]);

        $mergeFields = $broadcast->mergeFields();

        $this->assertEquals('awards', current_account()->vertical);
        $this->assertContains('entry_name', $mergeFields);
        $this->assertContains('entry_slug', $mergeFields);
        $this->assertContains('entry_local_id', $mergeFields);
        $this->assertNotContains('application_name', $mergeFields);
        $this->assertNotContains('application_slug', $mergeFields);
        $this->assertNotContains('application_local_id', $mergeFields);
    }

    public function testItPopulatesConsolidatedMergeFields(): void
    {
        $broadcast = $this->muffin(Broadcast::class);

        $mergeFields = $broadcast->consolidatedMergeFields();

        $this->assertContains('password_set_url', $mergeFields);
        $this->assertContains('account_name', $mergeFields);
        $this->assertContains('first_name', $mergeFields);
        $this->assertContains('last_name', $mergeFields);
        $this->assertContains('account_url', $mergeFields);
        $this->assertContains('user_field:abcd1234', $mergeFields);
    }
}
