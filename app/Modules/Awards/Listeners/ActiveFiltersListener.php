<?php

namespace AwardForce\Modules\Awards\Listeners;

use AwardForce\Library\Search\RouteAwareActiveFiltersListener;
use AwardForce\Modules\Awards\Data\AwardRepository;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Facades\Translator;

class ActiveFiltersListener extends RouteAwareActiveFiltersListener
{
    public function __construct(private AwardRepository $awards)
    {
    }

    protected function routes(): array
    {
        return ['voting.index', 'top-pick.index', 'judging.index', 'qualifying.index', 'gallery.index'];
    }

    protected function handleFilters(Collection $filters, array $context)
    {
        if ($filters->has('badge') && $badge = $this->awards->getBySlugOrId($filters->get('badge')->value)) {
            $filters->get('badge')->value = Translator::shallow($badge)->title;
            $filters->get('badge')->text = trans('judging.table.columns.badge');
        }
    }
}
