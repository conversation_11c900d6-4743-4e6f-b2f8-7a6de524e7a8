<?php

namespace AwardForce\Modules\Awards\Views;

use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Search\Services\SavedViews;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class AwardsListView extends View
{
    use SavedViews;

    /** @var Request */
    protected $request;

    /** @var Engine */
    protected $translator;

    /** @var ColumnatorFactory */
    private $columnatorFactory;

    public function __construct(
        Request $request,
        Engine $translator,
        ColumnatorFactory $columnatorFactory
    ) {
        $this->request = $request;
        $this->translator = $translator;
        $this->columnatorFactory = $columnatorFactory;
    }

    /**
     * @return LengthAwarePaginator
     */
    public function awards()
    {
        $search = new ColumnatorSearch($this->columnator);

        return $this->translator->translate($search->search());
    }

    /**
     * @return string
     */
    public function area()
    {
        return 'awards.search';
    }

    /**
     * @return \Platform\Search\Columnator
     */
    public function columnator()
    {
        return $this->columnatorFactory->forArea($this->area(), $this->request->all());
    }

    public function awardsIds()
    {
        return $this->awards->pluck('id')->toArray();
    }
}
