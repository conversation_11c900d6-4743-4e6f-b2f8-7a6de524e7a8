<?php

namespace AwardForce\Modules\Awards;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\Awards\Data\AwardRepository;
use AwardForce\Modules\Awards\Data\EloquentAwardRepository;
use AwardForce\Modules\Awards\Events\AwardWasCopied;
use AwardForce\Modules\Awards\Events\CopyAwardListener;
use Platform\Localisation\Listeners\CopyTranslationsListener;

class AwardsServiceProvider extends ServiceProvider
{
    /**
     * Sets up the required repositories and their bindings.
     *
     * @var array
     */
    protected $repositories = [
        AwardRepository::class => EloquentAwardRepository::class,
    ];

    /**
     * @var array
     */
    protected $files = [
        __DIR__.'/macros.php',
    ];

    /**
     * Define the listeners for this module.
     *
     * @var array
     */
    protected $listeners = [
        AwardWasCopied::class => [
            CopyTranslationsListener::class.'@whenModelWasCopied',
            CopyAwardListener::class,
        ],
    ];
}
