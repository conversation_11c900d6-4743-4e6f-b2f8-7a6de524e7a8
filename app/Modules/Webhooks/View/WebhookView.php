<?php

namespace AwardForce\Modules\Webhooks\View;

use AwardForce\Library\Collections\LocaleAwareNaturalSort;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Webhooks\Collections\SubscriptionEvents;
use AwardForce\Modules\Webhooks\Models\Webhook;
use AwardForce\Modules\Webhooks\Services\SigningKeyProvider;
use Illuminate\Support\Collection;
use Platform\View\View;

/**
 * Class WebhookView
 */
class WebhookView extends View
{
    public ?Webhook $webhook = null;

    /**
     * WebhookView constructor.
     */
    public function __construct(private readonly FormRepository $forms)
    {
    }

    public function webhook(): ?Webhook
    {
        return $this->webhook;
    }

    public function selectedFieldIds(): array
    {
        return $this->webhook?->fields()->pluck('id')->all() ?? [];
    }

    /**
     * Return an array of events to be used by the multiselect component
     */
    public function events(): array
    {
        return collect((new SubscriptionEvents)->all())
            ->map(fn($value, $key) => ['id' => $key, 'name' => $value])
            ->sortBy('name')
            ->values()
            ->all();
    }

    /**
     * Get an existing signing_key for this account, or generate a new one
     *
     * @throws \Exception
     */
    public function defaultSigningKey(): string
    {
        return SigningKeyProvider::getKey();
    }

    /**
     * Get a list of forms with their fields.
     */
    public function formsWithFields(): Collection
    {
        return $this->forms->allowApiUpdates()
            ->selectedSeason(CurrentAccount::activeSeason()?->id)
            ->with(['fields:id,slug,form_id'])
            ->fields(['id', 'slug'])
            ->get()
            ->translate()
            ->sort(new LocaleAwareNaturalSort('name'))
            ->map(fn(Form $form) => [
                'id' => $form->id,
                'slug' => (string) $form->slug,
                'name' => $form->name,
                'fields' => $form->fields
                    ->sort(new LocaleAwareNaturalSort('title'))
                    ->map(fn(Field $field) => [
                        'id' => $field->id,
                        'slug' => (string) $field->slug,
                        'name' => $field->title,
                    ])
                    ->values(),
            ])
            ->values();
    }
}
