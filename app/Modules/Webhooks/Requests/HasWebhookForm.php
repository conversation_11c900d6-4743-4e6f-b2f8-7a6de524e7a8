<?php

namespace AwardForce\Modules\Webhooks\Requests;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;

trait HasWebhookForm
{
    public function attributes(): array
    {
        return [
            'form' => trans('validation.attributes.resourceId'),
        ];
    }

    private function validFormSlug(): array
    {
        return app(FormRepository::class)
            ->allowApiUpdates()
            ->just('slug');
    }

    public function formId(): ?int
    {
        return once(fn() => app(FormRepository::class)->getBySlug($this->input('form'))?->id);
    }

    /**
     * @return int[]
     */
    public function fieldIds(): array
    {
        return ids_from_slugs($this->array('fields'), app(FieldRepository::class));
    }

    private function validFieldSlugs(): array
    {
        if (! $this->formId()) {
            return [];
        }

        return app(FieldRepository::class)
            ->resource([
                Field::RESOURCE_FORMS,
                Field::RESOURCE_ATTACHMENTS,
                Field::RESOURCE_CONTRIBUTORS,
                Field::RESOURCE_REFEREES,
            ])
            ->form($this->formId())
            ->just('slug');
    }
}
