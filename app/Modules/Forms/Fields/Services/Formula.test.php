<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Support\Facades\Process;
use Tests\IntegratedTestCase;

class FormulaTest extends IntegratedTestCase
{
    public function testCalculate(): void
    {
        $field1 = $this->muffin(Field::class, ['type' => 'formula']);
        $field2 = $this->muffin(Field::class, ['type' => 'formula']);
        $field3 = $this->muffin(Field::class, ['type' => 'formula']);
        $field = $this->muffin(Field::class, ['type' => 'numeric']);

        $field1->getConfiguration()->setFormula('SUM({'.$field->slug.'}, 1)');
        $field1->save();
        $field2->getConfiguration()->setFormula('SUM({'.$field->slug.'}, 2)');
        $field2->save();
        $field3->getConfiguration()->setFormula('SUM({'.$field->slug.'}, 3)');
        $field3->save();

        $fields = new Fields([$field1, $field2, $field3, $field]);

        $values = [
            (string) $field1->slug => '1',
            (string) $field2->slug => '2',
            (string) $field3->slug => '3',
            (string) $field->slug => '10',
        ];

        Process::fake([
            '*' => Process::sequence([json_encode([
                (string) $field1->slug => '11',
                (string) $field2->slug => '12',
                (string) $field3->slug => '13',
            ])]),
        ]);

        $results = (app(Formula::class))->calculate($fields, $values, $values);

        $this->assertCount(3, $results);

        $this->assertArrayHasKey((string) $field1->slug, $results);
        $this->assertArrayHasKey((string) $field2->slug, $results);
        $this->assertArrayHasKey((string) $field3->slug, $results);

        $this->assertEquals('11', $results[(string) $field1->slug]);
        $this->assertEquals('12', $results[(string) $field2->slug]);
        $this->assertEquals('13', $results[(string) $field3->slug]);
    }

    public function testItDoesNotCallRunnerWhenNoFormulaFields(): void
    {
        $field = $this->muffin(Field::class, ['type' => 'numeric']);
        $fields = new Fields([$field]);

        $values = [
            (string) $field->slug => '10',
        ];

        Process::fake();

        $results = (app(Formula::class))->calculate($fields, $values, $values);

        Process::assertNothingRan();
        $this->assertEmpty($results);
    }
}
