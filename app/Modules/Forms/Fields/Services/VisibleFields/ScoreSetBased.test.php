<?php

namespace AwardForce\Modules\Forms\Fields\Services\VisibleFields;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\ScoreSetBased as Voting;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;

final class ScoreSetBasedTest extends TestVisibleFields
{
    protected function getMode(): string
    {
        return ScoreSet::MODE_VOTING;
    }

    protected function getClass(): string
    {
        return Voting::class;
    }

    protected function visibleFieldsClass(ScoreSetCollection $scoreSets): VisibleFields
    {
        return $this->factory->scoreSetBased($scoreSets->first());
    }

    public function testDoesNotIncludeEmptyFieldsByDefault(): void
    {
        $entry = $this->muffin(Entry::class);

        $fieldWithValue = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $this->values->setValuesForObject([(string) $fieldWithValue->slug => 'some value'], $entry);

        $emptyField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);

        $scoreSet = $this->muffin(ScoreSet::class);
        $scoreSet->fields()->attach([$fieldWithValue->id, $emptyField->id]);

        $this->assertCount(1, $visibleFields = $this->factory->scoreSetBased($scoreSet)->forSubmittable($entry));
        $this->assertEquals($fieldWithValue->id, $visibleFields->first()->id);
    }
}
