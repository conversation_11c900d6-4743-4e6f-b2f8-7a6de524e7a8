<?php

namespace AwardForce\Modules\Forms\Fields\Bus;

use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Modules\AIAgents\Boundary\AIAgent;
use AwardForce\Modules\Forms\Fields\Configurations\Ai;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use Platform\Events\EventDispatcher;

readonly class GenerateAIFieldValueHandler
{
    use EventDispatcher;

    public function __construct(
        private FieldRepository $fields,
        private AIAgent $aiAgent,
        private ValuesService $valuesService,
    ) {
    }

    public function handle(GenerateAIFieldValue $command): void
    {
        $field = $this->field($command->fieldId);
        /** @var Ai $fieldConfig */
        $fieldConfig = $field->getConfiguration();

        $promptContext = $this->aiAgent->promptContext($command->resource, $fieldConfig->contexts());
        $response = $this->aiAgent->generateText(
            resource: $command->resource,
            prompt: new Prompt(
                model: $fieldConfig->model(),
                userPrompt: $fieldConfig->prompt(),
                systemPrompt: "You are a helpful assistant that answers questions. You're given a context and a question, and you should answer the question in plain text based on the context. If you don't know the answer, say you don't know. <context>$promptContext</context>",
            ),
            metaData: [
                'field_id' => $command->fieldId,
                'ai_agent_id' => $fieldConfig->aiAgentId(),
                'model_id' => $fieldConfig->model()->modelId(),
            ],
        );

        $this->valuesService->syncIndividualFieldValueForObject(
            $this->aiAgent->model($command->resource),
            $field,
            $response,
        );
    }

    private function field(int $fieldId): Field
    {
        return $this->fields
            ->type(Field::TYPE_AI)
            ->primary($fieldId)
            ->fields(['fields.id', 'fields.slug', 'fields.type', 'fields.configuration']) // TODO: add `ai_agent_id` after relationship is setup.
            ->require();
    }
}
