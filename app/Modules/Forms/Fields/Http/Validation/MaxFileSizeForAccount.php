<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation;

use Illuminate\Contracts\Validation\Rule;

class MaxFileSizeForAccount implements Rule
{
    private $attribute;

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $this->attribute = $attribute;
        if (current_account()->maxFileSize === null) {
            return true;
        }

        return $value <= current_account()->maxFileSize;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        if (! isTrialAccount() || current_account()->maxFileSize != config('awardforce.trial.file-size-limit')) {
            return trans('miscellaneous.account_max_file_size', ['size' => current_account()->maxFileSize]);
        }

        return trans('files.messages.trial_limit_size').' '.
            trans('validation.max.numeric', [
                // This wasn't resolving from validation.attributes
                'attribute' => trans('validation.attributes.'.$this->attribute),
                'max' => config('awardforce.trial.file-size-limit'),
            ]);
    }
}
