<?php

namespace AwardForce\Modules\Forms\Fields\Configurations;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\MarkdownField;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Forms\Fields\Configurations\Table\Calculation;
use AwardForce\Modules\Forms\Fields\Configurations\Table\CalculationCollection;
use AwardForce\Modules\Forms\Fields\Configurations\Table\Filter;
use AwardForce\Modules\Forms\Fields\Configurations\Table\FilterCollection;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Exceptions\UnsupportedTableCalculation;
use Illuminate\Support\Collection;
use Tectonic\Localisation\Contracts\Translatable;

class Table implements Configuration, Translatable
{
    /** @var array */
    public $translated = [];

    /** @var Field */
    private $field;

    /** @var array */
    private $configuration;

    /** @var string */
    private $language;

    /** @var string */
    private $defaultLanguage;

    public function __construct(Field $field)
    {
        $this->field = $field;
        $this->configuration = json_decode($field->configuration ?? '', true);
        if (CurrentAccount::get()) {
            $this->language = Consumer::languageCode();
            $this->defaultLanguage = CurrentAccount::defaultLanguage()->code;
        }
    }

    public function columns(): Collection
    {
        return collect($this->option('columns'))
            ->mapWithKeys(fn($column) => [$column => [
                'columnLabel' => $this->getTranslation($column),
                'calculationLabel' => $this->getTranslation('calculation:'.$column),
            ]]);
    }

    public function rows(): Collection
    {
        return collect($this->option('rows'))
            ->mapWithKeys(function ($option) {
                return [$option => $this->getTranslation($option)];
            });
    }

    public function dynamicRowsEnabled(): bool
    {
        return $this->option('dynamicRowsEnabled', false);
    }

    public function filters(): FilterCollection
    {
        return (new FilterCollection($this->option('filters')))
            ->map(function ($filter) {
                return Filter::restore($filter);
            });
    }

    public function calculations(): CalculationCollection
    {
        return (new CalculationCollection($this->option('calculations')))
            ->map(function ($calculation) {
                return Calculation::restore($calculation);
            });
    }

    public function columnOrder(array $columns): Table
    {
        return $this->setOption('columns', $columns);
    }

    public function rowOrder(array $rows): Table
    {
        return $this->setOption('rows', $rows);
    }

    public function toggleDynamicRows(bool $state): Table
    {
        return $this->setOption('dynamicRowsEnabled', $state);
    }

    public function setFilters(Collection $filters): Table
    {
        return $this->setOption('filters', $filters->toArray());
    }

    public function setCalculations(Collection $calculations): Table
    {
        return $this->setOption('calculations', $calculations->toArray());
    }

    /**
     * @throws UnsupportedTableCalculation
     */
    public function getFormattedCalculation(string $column): ?string
    {
        $calculatedValue = $this->calculations()->calculatedValue($this->field, $column);

        if (! $calculatedValue) {
            return null;
        }

        $columnType = $this->filters()->columnType($column);

        return match ($columnType) {
            Filter::TYPE_CURRENCY => format_currency($calculatedValue, $this->filters()->columnCurrency($column)),
            Filter::TYPE_DECIMAL => localised_number_format(floatval($calculatedValue), 1),
            Filter::TYPE_DECIMAL_PRECISE => localised_number_format(floatval($calculatedValue), 2),
            Filter::TYPE_INTEGER => localised_number_format(floatval($calculatedValue), 0),
            default => (string) $calculatedValue,
        };
    }

    public function getTranslatableFields(): array
    {
        return [];
    }

    public function getId(): ?int
    {
        return $this->field->getId();
    }

    public function getResourceName(): string
    {
        return 'Field.Table';
    }

    public function addTranslation($language, $key, $value)
    {
        if (! isset($this->translated[$language])) {
            $this->translated[$language] = [];
        }

        $this->translated[$language][$key] = $value;
    }

    public function getTranslation($key, $language = null): string
    {
        if ($language) {
            return array_get($this->translated, "$language.$key", '');
        }

        $language = $this->language;

        if (! array_has($this->translated, "$language.$key")) {
            $language = $this->defaultLanguage;
        }

        return array_get($this->translated, "$language.$key", '');
    }

    public function getTranslatedLabel($column, $row, $language = null): string
    {
        return $this->getTranslation($column.':'.$row, $language);
    }

    public function toArray(): array
    {
        return [
            'columns' => $this->option('columns'),
            'rows' => $this->option('rows'),
            'dynamicRowsEnabled' => $this->dynamicRowsEnabled(),
            'filters' => $this->filters(),
            'calculations' => $this->calculations(),
        ];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    public function option(string $key, $default = [])
    {
        return array_get($this->configuration, $key, $default);
    }

    public function translated(): array
    {
        return $this->translated;
    }

    private function setOption(string $key, $value): Table
    {
        array_set($this->configuration, $key, $value);
        $this->field->configuration = json_encode($this->configuration);

        return $this;
    }

    public function touch()
    {
        return $this->field->touch();
    }

    public static function compatible(Table $table1, Table $table2)
    {
        return self::configurationHash($table1) == self::configurationHash($table2);
    }

    public static function configurationHash(Table $table)
    {
        $columns = count($table->columns());
        $rows = count($table->rows());
        $filters = count($table->filters());
        $dynamicRows = ($table->dynamicRowsEnabled());
        $data = "columns:$columns|rows:$rows|filters:$filters|dynamic:".(int) $dynamicRows;

        return hash('sha256', $data);
    }

    public function adaptValues(?string $configToBeAdapted)
    {
        if (! $configToBeAdapted) {
            return null;
        }

        $newConfiguration = $configToBeAdapted;
        $i = 0;
        foreach (array_keys($this->columns()->toArray()) as $column) {
            $newConfiguration = str_replace('"column-'.($i++).'"', '"'.$column.'"', $newConfiguration);
        }

        $i = 0;
        foreach (array_keys($this->rows()->toArray()) as $row) {
            $newConfiguration = str_replace('"row-'.($i++).'"', '"'.$row.'"', $newConfiguration);
        }

        return $newConfiguration;
    }

    public static function formatTableConfiguration(array $configuration, ?string $value)
    {
        if (! $value) {
            return null;
        }

        $i = 0;
        foreach ($configuration['columns'] as $column) {
            $value = str_replace($column, 'column-'.$i++, $value);
        }

        $i = 0;
        foreach ($configuration['rows'] as $row) {
            $value = str_replace($row, 'row-'.$i++, $value);
        }

        return $value;
    }

    public function forMarkdown()
    {
        return new MarkdownField(view('judging.fields.table-preview', ['field' => $this->field]));
    }

    public function forWord(): string
    {
        return view('document.fields.table', ['field' => $this->field])->render();
    }
}
