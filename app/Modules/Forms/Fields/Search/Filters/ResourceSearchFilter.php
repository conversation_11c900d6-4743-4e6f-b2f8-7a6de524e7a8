<?php

namespace AwardForce\Modules\Forms\Fields\Search\Filters;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Forms\Fields\Search\ResourceFilterMapper;
use Illuminate\Contracts\Support\Htmlable;
use Platform\Search\ApiVisibility;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\Services\SearchFilterValidator;

class ResourceSearchFilter implements ColumnatorFilter, Htmlable, SearchFilter, SearchFilterValidation
{
    use ResourceFilterMapper;
    use SearchFilterValidator;

    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (isset($this->input['resource']) && $this->input['resource'] !== '') {
            $query = $query->where('fields.resource', '=', $this->realResource(Vertical::replace($this->input['resource'], true)));
        }

        return $query;
    }

    /**
     * Get content as a string of HTML.
     *
     * @return string
     */
    public function toHtml()
    {
        return view('field.search.filters.resource')->render();
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return isset($this->input['resource']) && $this->input['resource'] !== '';
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['resource']);
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $this->validateValueInArray($filterName, $filterValue, Vertical::replaceArray([
            'attachments',
            'contributors',
            'entries',
            'users',
        ]));
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
