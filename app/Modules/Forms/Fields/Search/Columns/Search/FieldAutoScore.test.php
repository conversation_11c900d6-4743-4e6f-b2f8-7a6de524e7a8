<?php

namespace AwardForce\Modules\Forms\Fields\Search\Columns\Search;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class FieldAutoScoreTest extends BaseTestCase
{
    use Laravel;

    public function testItReturnsCorrectScoreValue(): void
    {
        $field = new Field();
        $field->autoScoring = true;
        $column = new FieldAutoScore($field);
        $entry = new Entry();
        $entry->scores = [
            $field->slug.'_auto_score' => 100,
        ];

        $this->assertEquals('100.00', $column->value($entry));
    }

    public function testItReturnsCorrectOrderFieldString(): void
    {
        $field = new Field();
        $field->slug = 'test_field';
        $column = new FieldAutoScore($field);

        $this->assertEquals("entries.scores->'$.test_field'", $column->orderField);
    }
}
