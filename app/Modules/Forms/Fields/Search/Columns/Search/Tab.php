<?php

namespace AwardForce\Modules\Forms\Fields\Search\Columns\Search;

use AwardForce\Modules\Api\V2\Services\ApiFieldTransformer;
use AwardForce\Modules\Forms\Fields\Search\Filters\TabSearchFilter;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;

class Tab implements ApiColumn, Column
{
    use ApiFieldTransformer;

    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return trans('fields.table.columns.tab');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'fields.tab';
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
            TabSearchFilter::class,
        ]);
    }

    /**
     * Returns the name of the field in the query that should be present. This is useful for specifying the exact field. In some cases,
     * columns do not reference a specific table field, and may need some additional filtering or work, so a return value is not always
     * necessary. If nothing is returned, it will be ignored for the query. If null is returned, it is expected the the value method
     * will still return an appropriate value.
     *
     * @return string|null
     */
    public function field()
    {
        return 'fields.tab_id';
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return view('field.search.tab', ['field' => $record])->render();
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return new HtmlString(view('field.search.tab', ['field' => $record])->render());
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('all');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return 20;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }

    /**
     * {@inheritDoc}
     */
    public function apiName()
    {
        return 'tab';
    }

    /**
     * {@inheritDoc}
     */
    public function apiValue($record)
    {
        return $this->fetchApiRelatedResource($record, 'tab', 'tab');
    }

    /**
     * Returns a ApiVisibility value object that consists of a value representing the views that the column is available on.
     */
    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
