<?php

namespace AwardForce\Modules\Forms\Fields\View\Compose;

use AwardForce\Modules\Forms\Fields\Configurations\Table;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class TablePreviewComposer
{
    public function configuration(View $view): Table|Collection
    {
        return translate($view->getData()['field']->getConfiguration());
    }

    public function rows(View $view): Collection
    {
        return $this->configuration($view)->rows()->merge(array_flip($view->getData()['field']?->value['dynamicRows'] ?? []));
    }

    public function compose(View $view): void
    {
        $view->with([
            'configuration' => translate($this->configuration($view)),
            'columns' => $this->configuration($view)->columns(),
            'rows' => $this->rows($view),
            'filters' => $this->configuration($view)->filters(),
            'calculations' => $this->configuration($view)->calculations(),
            'index' => 1,
        ]);
    }
}
