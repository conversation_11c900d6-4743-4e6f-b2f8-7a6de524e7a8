<?php

namespace AwardForce\Modules\Forms\Fields\View\Compose;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Contracts\View\View;
use Mockery as m;

final class DateFieldComposerTest extends \PHPUnit\Framework\TestCase
{
    protected function tearDown(): void
    {
        m::close();
        parent::tearDown();
    }

    public function testEmptyDateFieldReturnsValidObject(): void
    {
        $view = m::mock(View::class);
        $view->shouldReceive('with')->once()->with('value', ['date' => '', 'timezone' => '']);

        $view->field = new Field;
        $view->field->type = 'date';

        (new DateFieldComposer)->compose($view);
    }

    public function testValidDatetimeFieldValue(): void
    {
        $view = m::mock(View::class);
        $view->shouldReceive('with')->once()->with('value', ['datetime' => '12/12/12', 'timezone' => 'US']);

        $view->field = new Field;
        $view->field->type = 'datetime';
        $view->field->value = ['datetime' => '12/12/12', 'timezone' => 'US'];

        (new DateFieldComposer)->compose($view);
    }

    public function testInvalidTimezone(): void
    {
        $view = m::mock(View::class);
        $view->shouldReceive('with')->once()->with('value', ['datetime' => '01/03/98', 'timezone' => '']);

        $view->field = new Field;
        $view->field->type = 'datetime';
        $view->field->value = ['datetime' => '01/03/98'];
        $view->field->includeTimezone = true;

        (new DateFieldComposer)->compose($view);
    }

    public function testMissingFieldType(): void
    {
        $view = m::mock(View::class);
        $view->shouldReceive('with')->once()->with('value', ['datetime' => '', 'timezone' => 'oz']);

        $view->field = new Field;
        $view->field->type = 'datetime';
        $view->field->value = ['timezone' => 'oz'];
        $view->field->includeTimezone = true;

        (new \AwardForce\Modules\Forms\Fields\View\Compose\DateFieldComposer)->compose($view);
    }
}
