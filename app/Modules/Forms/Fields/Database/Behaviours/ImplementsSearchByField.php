<?php

namespace AwardForce\Modules\Forms\Fields\Database\Behaviours;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\LazyCollection;

trait ImplementsSearchByField
{
    public function searchByField(Field $field): LazyCollection
    {
        $table = $this->getModel()->getTable();

        return $this->getQueryForSearchByField($field)
            ->whereNotNull("{$table}.values->{$field->slug}")
            ->cursor();
    }

    protected function getQueryForSearchByField(Field $field): Builder
    {
        return $this->getQuery();
    }
}
