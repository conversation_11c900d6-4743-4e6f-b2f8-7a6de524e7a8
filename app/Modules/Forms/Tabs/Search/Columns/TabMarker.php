<?php

namespace AwardForce\Modules\Forms\Tabs\Search\Columns;

use AwardForce\Library\Search\Columns\ReactiveMarker;
use Facades\Platform\Strings\Output;
use Illuminate\Support\HtmlString;

class TabMarker extends ReactiveMarker
{
    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        $label = htmlspecialchars(Output::text($this->label($record)));
        $disabled = ! empty($record->locked) ? 'true' : 'false';

        return new HtmlString("<reactive-marker :id=\"{$record->id}\" label=\"$label\" v-model=\"selected\" :disabled=\"$disabled\"/>");
    }

    protected function label($record): string
    {
        return trans('buttons.checkbox_for_resource', ['resource' => $record->resourceLabel()]);
    }
}
