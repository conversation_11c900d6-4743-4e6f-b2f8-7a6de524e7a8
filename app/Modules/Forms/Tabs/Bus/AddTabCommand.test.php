<?php

namespace AwardForce\Modules\Forms\Tabs\Bus;

use AwardForce\Modules\Forms\Fields\Database\DataAccess\ImageDimensionConstraints;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Entities\TabFactory;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Tectonic\LaravelLocalisation\Database\TranslationService;
use Tests\IntegratedTestCase;

final class AddTabCommandTest extends IntegratedTestCase
{
    /** @var TabRepository */
    private $tabs;

    /** @var TabFactory */
    private $tabFactory;

    /** @var TranslationService */
    private $translationService;

    /** @var SeasonRepository */
    private $seasons;

    public function init()
    {
        $this->tabs = app(TabRepository::class);
        $this->tabFactory = app(TabFactory::class);
        $this->translationService = app(TranslationService::class);
        $this->seasons = app(SeasonRepository::class);
    }

    public function testAddAttachmentsTab(): void
    {
        $command = new AddTabCommand(
            SeasonFilter::getId(),
            FormSelector::get()->id,
            Tab::TYPE_ATTACHMENTS,
            100,
            null,
            [],
            [],
            'all',
            [],
            false,
            true,
            new ImageDimensionConstraints(40, 30, 20, 10),
            ['max-filesize' => 10, 'min-filesize' => 1],
            []
        );

        $handler = new AddTabCommandHandler(
            $this->tabs,
            $this->tabFactory,
            $this->translationService,
            $this->seasons
        );

        $tab = $handler->handle($command);

        $this->assertEquals(10, $tab->getSetting('max-filesize'));
        $this->assertEquals(1, $tab->getSetting('min-filesize'));
        $this->assertNotEmpty($tab->imageDimensionConstraints->toArray());
    }

    public function testAddEligibilityTab(): void
    {
        $command = new AddTabCommand(
            SeasonFilter::getId(),
            FormSelector::get()->id,
            Tab::TYPE_ATTACHMENTS,
            100,
            null,
            [],
            [],
            'all',
            [],
            false,
            true,
            new ImageDimensionConstraints,
            ['max-filesize' => 10, 'min-filesize' => 1],
            []
        );

        $handler = new AddTabCommandHandler(
            $this->tabs,
            $this->tabFactory,
            $this->translationService,
            $this->seasons
        );

        $tab = $handler->handle($command);

        $this->assertEquals(10, $tab->getSetting('max-filesize'));
        $this->assertEquals(1, $tab->getSetting('min-filesize'));
    }

    public function testAddRefereeTab()
    {
        $command = new AddTabCommand(
            SeasonFilter::getId(),
            FormSelector::get()->id,
            Tab::TYPE_REFEREES,
            100,
            null,
            [],
            [],
            'all',
            [],
            false,
            true,
            new ImageDimensionConstraints,
            ['review-stage' => 'as2SW23'],
            []
        );

        $handler = new AddTabCommandHandler(
            $this->tabs,
            $this->tabFactory,
            $this->translationService,
            $this->seasons
        );

        $tab = $handler->handle($command);

        $this->assertEquals('as2SW23', $tab->getSetting('review-stage'));
    }
}
