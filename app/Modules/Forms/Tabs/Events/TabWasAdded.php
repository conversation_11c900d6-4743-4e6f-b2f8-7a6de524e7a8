<?php

namespace AwardForce\Modules\Forms\Tabs\Events;

use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;

class TabWasAdded implements Activity
{
    /** @var \AwardForce\Modules\Forms\Tabs\Database\Entities\Tab */
    public $tab;

    /**
     * Create a new instance.
     */
    public function __construct(Tab $tab)
    {
        $this->tab = $tab;
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('tab'),
            'added',
            'audit.tab.added',
            $this->tab,
            $this->tab->id,
            (string) $this->tab->slug,
        );
    }
}
