<?php

namespace AwardForce\Modules\Forms\Collaboration\Services;

use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Identity\Users\Models\User;

class CollaboratorMapper
{
    public function __construct(protected Collaborator $collaborator, protected Submittable $submittable)
    {
    }

    public function toArray(): array
    {
        $user = $this->collaborator->user;

        return [
            'slug' => (string) $this->collaborator->slug,
            'wasInvited' => $user->wasInvited,
            'privilege' => $this->collaborator->privilege->get(),
            'owner' => $this->collaborator->userId === $this->collaborator->submittable->getUserId(),
            'manager' => $this->isManager($this->collaborator->user),
            'user' => (string) $user->slug,
            'profilePhoto' => $user->profilePhoto()->toArray(100, 100),
            'firstName' => $user->firstName,
            'lastName' => $user->lastName,
            'fullName' => $user->name,
            'initials' => $user->initials,
            'email' => $user->email,
        ];
    }

    private function isManager(User $user): bool
    {

        if ($user->isProjectManager()) {
            return true;
        }

        if ($user->isChapterManager()) {
            return $user->managedChapters->contains($this->submittable->getChapterId());
        }

        return false;
    }
}
