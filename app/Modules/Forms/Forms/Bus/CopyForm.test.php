<?php

namespace AwardForce\Modules\Forms\Forms\Bus;

use AwardForce\Library\Copier\CopierOptions;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Identity\Roles\Models\Role;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class CopyFormTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItShouldCopyFormWithAssignedRoles()
    {
        Event::fake();
        $roles = $this->muffins(2, Role::class);
        $form = $this->muffin(Form::class);
        $form->roles()->sync(collect($roles)->pluck('id')->all());

        $command = new CopyForm($form, new CopierOptions([]));
        $handler = app(CopyFormHandler::class);
        $newForm = $handler->handle($command);

        $this->assertCount(2, $form->roles);
        $this->assertCount(2, $newForm->roles);
        $this->assertEqualsCanonicalizing([$roles[0]->id, $roles[1]->id], $newForm->roles->pluck('id')->toArray());
    }
}
