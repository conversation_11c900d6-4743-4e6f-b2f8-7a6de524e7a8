<?php

namespace AwardForce\Modules\Forms\Forms\Search;

use AwardForce\Modules\Forms\Forms\Search\Columns\CallToAction;
use AwardForce\Modules\Forms\Forms\Search\Columns\ChapterOption;
use AwardForce\Modules\Forms\Forms\Search\Columns\ContentBlock;
use AwardForce\Modules\Forms\Forms\Search\Columns\Name;
use AwardForce\Modules\Forms\Forms\Search\Columns\Season;
use AwardForce\Modules\Forms\Forms\Search\Columns\Type;
use AwardForce\Modules\Forms\Forms\Search\Filters\FormSlugFilter;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;

class FormApiColumnator extends FormColumnator implements ApiColumnator
{
    use ApiColumns;

    /**
     * Base columns are those that are static and essentially not columns based off any custom
     * field configuration. For example, a user's first and last name are base columns.
     */
    protected function baseColumns(): Columns
    {
        return new Columns([
            Slug::forResource('forms'),
            new Name,
            new CallToAction,
            new Season,
            new Type,
            new ContentBlock,
            new ChapterOption,
            Created::forResource('forms', consumer()->dateLocale()),
            Updated::forResource('forms', consumer()->dateLocale()),
        ]);
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = parent::availableDependencies($view);
        $columns = $this->columns($view);

        // Filters
        $dependencies->add(new FormSlugFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(OrderFilter::fromColumns($columns, $this->input, 'forms.created')->uniqueColumn('forms.id'));
        $dependencies->add(new TranslatedColumnSearchFilter($columns->translatedColumns([
            'forms.name',
            'forms.callToAction',
        ]), 'Form', current_account_id(), $this->input));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('form'), 'Form', current_account_id())));

        return $dependencies;
    }

    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    public static function key(): string
    {
        return 'forms.api_search';
    }
}
