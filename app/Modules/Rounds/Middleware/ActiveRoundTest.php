<?php

namespace AwardForce\Modules\Rounds\Middleware;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Services\Rounds;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Translation\Translator;
use Mockery as m;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ActiveRoundTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private Rounds|m\MockInterface $rounds;
    private Translator|m\MockInterface $translator;
    private Manager|m\MockInterface $manager;
    private Request|m\MockInterface $request;
    private ActiveRound $middleware;
    private Entry $entry;

    private function setMocks(): void
    {
        $form = new Form;
        $form->id = 1;

        $this->entry = $this->muffin(Entry::class, ['deadline_at' => now()->addDay()->toDateTimeString(), 'deadline_timezone' => 'UTC', 'form_id' => $form->id]);

        FormSelector::spy();
        FormSelector::shouldReceive('getForEntry')->once()->andReturn($form);

        $this->rounds = m::mock(Rounds::class);
        $this->translator = m::mock(Translator::class)->shouldIgnoreMissing('translation');
        $this->manager = m::mock(Manager::class);
        $this->request = m::mock(Request::class)->shouldIgnoreMissing(null);

        $this->middleware = new ActiveRound($this->rounds, $this->translator, $this->manager);
    }

    public function testThrow403IfRoundNotActive(): void
    {
        $this->setMocks();
        $this->rounds->shouldReceive('hasActive')->with('entry', 1)->andReturn(false);
        $this->manager->shouldReceive('isManager')->andReturn(false);
        $this->request->shouldReceive('route')->with('entry')->andReturn(null);

        try {
            $this->middleware->handle($this->request, function () {
                throw new \Exception('This should not be thrown.');
            }, 'entry');
        } catch (HttpException $e) {
            $this->assertEquals(403, $e->getStatusCode());
        }
    }

    public function testHasActiveRound(): void
    {
        $this->setMocks();
        $this->rounds->shouldReceive('hasActive')->with('entry', 1)->andReturn(true);
        $this->manager->shouldReceive('isManager')->andReturn(false);
        $this->request->shouldReceive('route')->with('entry')->andReturn(null);

        $response = $this->middleware->handle($this->request, function (Request $request) {
            $this->assertEquals($this->request, $request);

            return 'One Ring to rull them all...';
        }, 'entry');

        $this->assertEquals('One Ring to rull them all...', $response);
    }

    public function testUserIsManager(): void
    {
        $this->setMocks();
        $this->rounds->shouldReceive('hasActive')->with('entry', 1)->andReturn(false);
        $this->manager->shouldReceive('isManager')->andReturn(true);
        $this->request->shouldReceive('route')->with('entry')->andReturn(null);

        $response = $this->middleware->handle($this->request, function (Request $request) {
            $this->assertEquals($this->request, $request);

            return 'One Ring to rull them all...';
        }, 'entry');

        $this->assertEquals('One Ring to rull them all...', $response);
    }

    public function testReturnJsonIfRequested(): void
    {
        $this->setMocks();
        $this->rounds->shouldReceive('hasActive')->with('entry', 1)->andReturn(false);
        $this->manager->shouldReceive('isManager')->andReturn(false);
        $this->request->shouldReceive('route')->with('entry')->andReturn(null);
        $this->request->shouldReceive('wantsJson')->andReturn(true);

        $response = $this->middleware->handle($this->request, function (Request $request) {
            throw new \Exception('This should not be thrown.');
        }, 'entry');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(403, $response->getStatusCode());
        $this->assertArrayHasKey('message', $response->getData(true));
    }

    public function testItRedirectsIfDoesNotWantJson(): void
    {
        $this->setMocks();
        $this->rounds->shouldReceive('hasActive')->with('entry', 1)->andReturn(false);
        $this->manager->shouldReceive('isManager')->andReturn(false);
        $this->request->shouldReceive('route')->with('entry')->andReturn(null);
        $this->request->shouldReceive('wantsJson')->andReturn(false);

        $response = $this->middleware->handle($this->request, function (Request $request) {
            throw new \Exception('This should not be thrown.');
        }, 'entry');

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->followRedirects($response)->assertSee('message');
    }

    public function testEntryDeadlineNotPassed(): void
    {
        $this->setMocks();
        $this->request->shouldReceive('route')->with('entry')->andReturn($this->entry);

        $response = $this->middleware->handle($this->request, function (Request $request) {
            $this->assertEquals($this->request, $request);

            return 'One Ring to rull them all...';
        }, 'entry');

        $this->assertEquals('One Ring to rull them all...', $response);
    }

    public function testCopyingEntriesShouldNotCareOfOthersFormsBeenClosed(): void
    {
        $firstFormOnTheDB = Form::all()->first();
        $roundExpired = $this->muffin(Round::class, [
            'round_type' => Round::ROUND_TYPE_ENTRY,
            'enabled' => true,
            'form_id' => $firstFormOnTheDB->id,
        ]);
        $roundExpired->startAt(Carbon::now()->subDay(3)->format('Y-m-d H:i'), 'UTC');
        $roundExpired->endAt(Carbon::now()->subDay(2)->format('Y-m-d H:i'), 'UTC');
        $roundExpired->save();

        $form = $this->muffin(Form::class);
        $round = $this->muffin(Round::class, [
            'round_type' => Round::ROUND_TYPE_ENTRY,
            'enabled' => true,
            'form_id' => $form->id,
        ]);
        $round->startAt(Carbon::now()->subDay(3)->format('Y-m-d H:i'), 'UTC');
        $round->endAt(Carbon::now()->addDay(3)->format('Y-m-d H:i'), 'UTC');
        $round->save();

        $category = $this->muffin(Category::class, ['form_id' => $form->id]);
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'category_id' => $category->id]);

        $request = m::mock(Request::class)->shouldReceive('has')->with('selected')->andReturn([$entry->id])->getMock();
        $request->shouldReceive('get')->with('selected')->andReturn([$entry->id]);
        $request->shouldReceive('route')->with('entry')->andReturn(null);

        $request->shouldNotReceive('wantsJson')->andReturn(false);
        $translator = m::mock(Translator::class)->shouldNotReceive('get')->with('miscellaneous.rounds.closed.entry')->getMock();

        $middleware = new ActiveRound(app(Rounds::class), $translator, app(Manager::class));
        $middleware->handle($request, fn(Request $request) => '', 'entry');
    }
}
