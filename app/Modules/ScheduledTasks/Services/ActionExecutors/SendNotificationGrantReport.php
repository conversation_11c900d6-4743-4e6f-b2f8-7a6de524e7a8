<?php

namespace AwardForce\Modules\ScheduledTasks\Services\ActionExecutors;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\Notifications\Commands\SendSpecificNotificationCommand;
use AwardForce\Modules\Notifications\Data\Notification;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Arr;

class SendNotificationGrantReport extends SendNotification
{
    use GrantReportNotification;
    use HasNotification;

    private Notification $notification;
    private GrantReport $grantReport;

    public function validatePayload(): void
    {
        $this->validateAndSetNotification(true);
        $this->validateAndSetGrantReport();
    }

    private function validateAndSetGrantReport(): void
    {
        if (! $grantReportId = Arr::get($this->payload, 'grant_report_id')) {
            return;
        }

        if (! $grantReport = app(GrantReportRepository::class)->getById($grantReportId)) {
            throw new ModelNotFoundException(GrantReport::class." {$grantReportId} not found.");
        }

        $this->grantReport = $grantReport;
    }

    public static function create(CarbonImmutable $sendTime, ?Notification $notification = null, ?GrantReport $grantReport = null)
    {
        $instance = new self($sendTime, ['notification_id' => $notification->id, 'grant_report_id' => $grantReport->id]);
        $instance->validatePayload();

        return $instance;
    }

    public static function action(): string
    {
        return 'send_time_grant_report';
    }

    protected function sendNotification(): bool
    {
        $this->validateAndSetNotification();

        if (! $this->grantReport->form) {
            throw new ModelNotFoundException(Form::class." {$this->grantReport->formId} not found.");
        }

        if ($this->shouldSkip()) {
            return true; // We don't send it but mark the task as executed
        }

        $recipient = $this->getRecipient($this->grantReport->entry->entrant);
        $entry = $this->grantReport->entry;

        $data = $this->whenGrantReportEvent($this->grantReport);

        $this->dispatch(new SendSpecificNotificationCommand($this->notification, $recipient, $data, [], $entry->seasonId));

        return true;
    }

    private function shouldSkip(): bool
    {
        return ! $this->notification->active ||
            ! $this->notification->appliesToForm($this->grantReport->form) ||
            ! $this->notification->appliesToGrantReportStatus($this->grantReport->status);
    }

    public function calculateNewDueDate(CarbonImmutable $dueDate): CarbonImmutable
    {
        return $this->notification->calculateSendTime($dueDate);
    }
}
