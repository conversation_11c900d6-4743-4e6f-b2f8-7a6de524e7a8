<?php

namespace AwardForce\Modules\Ecommerce\Orders\View;

use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Search\Services\SavedViews;
use Illuminate\Http\Request;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;

class OrderList extends View
{
    use SavedViews;

    /** @var ColumnatorFactory */
    private $columnators;

    /** @var Request */
    private $request;

    public function __construct(
        ColumnatorFactory $columnators,
        Request $request
    ) {
        $this->columnators = $columnators;
        $this->request = $request;
    }

    public function orders()
    {
        $search = new ColumnatorSearch($this->columnator);

        return translate($search->search());
    }

    public function columnator()
    {
        return $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function area()
    {
        return 'orders.search';
    }

    public function tooltipText()
    {
        return trans('orders.search.tooltip.keywords');
    }

    public function ordersIds()
    {
        return $this->orders->pluck('id')->toArray();
    }
}
