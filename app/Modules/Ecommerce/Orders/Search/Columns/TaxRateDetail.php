<?php

namespace AwardForce\Modules\Ecommerce\Orders\Search\Columns;

use AwardForce\Modules\Api\V2\Services\ApiFieldTransformer;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use Illuminate\Support\Collection;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;

class TaxRateDetail implements ApiColumn, Column
{
    use ApiFieldTransformer;

    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title()
    {
        return '';
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'order.tax_rate_detail';
    }

    /**
     * Returns the column's search filter dependencies.
     */
    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
        ]);
    }

    /**
     * Returns the name of the field in the query that should be present
     */
    public function field()
    {
        return null;
    }

    /**
     * Return the value required in $record.
     *
     * @param  Order  $record
     * @return mixed
     */
    public function value($record)
    {
        return null;
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @param  Order  $record
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return '';
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('none');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Give columns with particularly important information a higher visibility priority.
     */
    public function priority(): int
    {
        return 60;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }

    /**
     * @param  Order  $record
     * @return array|null
     */
    public function apiValue($record)
    {
        $relatedTax = $record->relatedTax;
        if (! $relatedTax) {
            return null;
        }

        $slug = (string) $relatedTax->slug;

        return [
            'country' => $relatedTax->country,
            'link' => $this->link('tax', $slug),
            'name' => $relatedTax->name,
            'rate' => $relatedTax->rate,
            'region' => $relatedTax->region,
            'slug' => $slug,
            'term' => $relatedTax->term,
        ];
    }

    public function apiName()
    {
        return 'tax_rate_details';
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
