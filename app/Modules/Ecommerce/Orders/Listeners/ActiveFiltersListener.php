<?php

namespace AwardForce\Modules\Ecommerce\Orders\Listeners;

use AwardForce\Library\Search\RouteAwareActiveFiltersListener;
use Illuminate\Support\Collection;
use Platform\Search\ActiveFilter;

class ActiveFiltersListener extends RouteAwareActiveFiltersListener
{
    /**
     * Return an array of route names this listener applies to.
     *
     * @return array
     */
    protected function routes()
    {
        return ['order.index'];
    }

    /**
     * Handle filters.
     */
    protected function handleFilters(Collection $filters, array $context)
    {
        if ($filters->has('test')) {
            $this->test($filters->get('test'));
        }

        if ($filters->has('status')) {
            $this->status($filters->get('status'));
        }
    }

    private function test(ActiveFilter $filter)
    {
        $value = $filter->value == 'only' ? trans('orders.status.test') : trans('orders.status.live');

        $filter->value = strtolower($value);
        $filter->text = trans('orders.table.columns.test');
    }

    private function status(ActiveFilter $filter)
    {
        $filter->value = trans('orders.status.'.$filter->value);
        $filter->text = trans('orders.table.columns.status');
    }
}
