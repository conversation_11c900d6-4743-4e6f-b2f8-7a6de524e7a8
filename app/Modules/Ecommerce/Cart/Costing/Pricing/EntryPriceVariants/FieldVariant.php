<?php

namespace AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryPriceVariants;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Payments\Models\PriceAmount;
use Illuminate\Support\Arr;

class FieldVariant implements Variant
{
    use ChecksCategories;

    /** @var ValuesService */
    private $values;

    public function __construct(ValuesService $values)
    {
        $this->values = $values;
    }

    /**
     * Return true if the variant can return an amount for the conditions supplied.
     *
     * @return bool
     */
    public function appliesTo(PriceAmount $amount, Entry $entry)
    {
        return (empty($amount->chapterId) || $amount->chapterId == $entry->chapterId) &&
            (empty($amount->categoryId) || $this->categoryApplies($amount->categoryId, $entry)) &&
            $this->appliesFieldValue($amount, $entry);
    }

    protected function appliesFieldValue(PriceAmount $amount, Entry $entry)
    {
        /** @var Field $field */
        $field = $entry->fields->filter(function (Field $field) use ($amount) {
            return $field->id == $amount->fieldId;
        })->first();

        return $field ? $this->checkField($field, $amount, $entry) : false;
    }

    private function checkField(Field $field, PriceAmount $amount, Entry $entry): bool
    {
        $values = $this->values->getFieldValuesForObjects($field->id, collect([$entry]));
        if ($field->optionable() && ! $values->isEmpty()) {
            if ($field->type === 'checkboxlist') {
                $options = Arr::get($values, $entry->id);
            } else {
                $options = explode_options(Arr::get($values, $entry->id));
            }
            foreach ($options as $option) {
                if ($option == $amount->fieldValue) {
                    return true;
                }
            }

            return false;
        }

        if ($field->type === 'checkbox') {
            if (! Arr::get($values, $entry->id) && $amount->fieldValue == 0) {
                return true;
            }
        }

        $value = Arr::get($values, $entry->id);

        return $value && $value === $amount->fieldValue;
    }
}
