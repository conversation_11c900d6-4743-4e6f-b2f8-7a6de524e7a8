<?php

namespace AwardForce\Modules\Ecommerce\Cart\Services;

use AwardForce\Library\Values\Amount;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Item;
use AwardForce\Modules\Ecommerce\Cart\TagItem;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Repositories\PriceAmountRepository;
use Tectonic\LaravelLocalisation\Translator\Engine;

class FeeItemizer
{
    /**
     * @var Cart
     */
    private $cart;

    /**
     * @var PriceAmountRepository
     */
    private $amounts;

    /**
     * @var Engine
     */
    private $translator;

    public function __construct(Cart $cart, PriceAmountRepository $amounts, Engine $translator)
    {
        $this->cart = $cart;
        $this->amounts = $amounts;
        $this->translator = $translator;
    }

    /**
     * @throws \InvalidArgumentException
     */
    public function payTagFee(Entry $entry, Price $price): Item
    {
        $currency = $this->cart->currency();
        $priceAmount = $this->amounts->firstForPrice($price->id);
        $fee = $priceAmount->amountForCurrency($currency) ?: 0; // force null to 0 for Amount support

        $this->translator->translate($price);

        return new TagItem($price->title, $entry, $price, new Amount($fee, $currency));
    }
}
