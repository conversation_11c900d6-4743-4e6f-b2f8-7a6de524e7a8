<?php

namespace AwardForce\Modules\Ecommerce\Cart\Commands;

use AwardForce\Library\Values\Amount;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\EntrantItem;
use AwardForce\Modules\Ecommerce\Cart\EntrantItemiser;
use Platform\Events\EventDispatcher;
use Tectonic\LaravelLocalisation\Facades\Translator;

class AddEntrantItemToCartCommandHandler
{
    use EventDispatcher;

    /**
     * @var EntrantItemiser
     */
    private $itemiser;

    /**
     * @var Cart
     */
    private $cart;

    public function __construct(Cart $cart, EntrantItemiser $itemiser)
    {
        $this->itemiser = $itemiser;
        $this->cart = $cart;
    }

    /**
     * Handles the command requirement.
     */
    public function handle(AddEntrantItemToCartCommand $command)
    {
        // @TODO: Add this to a verifier, and out of this command
        if ($this->itemiser->userHasPaidOrWillPayEntrantFee($command->userId, $command->seasonId)) {
            return;
        }

        $price = $this->itemiser->getPrice($command->seasonId);

        // Entrant prices may not be configured, or needed
        if (! $price) {
            return;
        }

        $fee = $this->itemiser->getEntrantFee($price);
        $price = Translator::translate($price);

        $this->cart->addItem(new EntrantItem(
            lang($price, 'title'),
            $price,
            new Amount($fee, $this->cart->currency())
        ));

        $this->dispatch($this->cart->releaseEvents());
    }
}
