<?php

namespace AwardForce\Modules\Menu\Settings\Links\General;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class Seasons extends Link
{
    public function name(): string
    {
        return 'settings-seasons';
    }

    public function text(): string
    {
        return trans('seasons.titles.main');
    }

    public function link(): string
    {
        return route('season.index');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return Consumer::can('view', 'Seasons');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
