<?php

namespace AwardForce\Modules\Menu\Settings\Links\Users;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class Roles extends Link
{
    public function name(): string
    {
        return 'user-roles';
    }

    public function text(): string
    {
        return trans('roles.titles.main');
    }

    public function link(): string
    {
        return route('role.index');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return Consumer::can('view', 'Roles');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
