<?php

namespace AwardForce\Modules\Menu\Grants\Links;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage as ManageContextItem;
use Platform\Menu\Context\Link;

class Manage extends Link
{
    public function name(): string
    {
        return 'manage-grants';
    }

    public function text(): string
    {
        return trans('shared.manage');
    }

    public function link(): string
    {
        return route('grant.manager.index');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return feature_enabled('grants') && Consumer::can('view', 'Grants');
    }

    public function contexts(): array
    {
        return [new ManageContextItem];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
