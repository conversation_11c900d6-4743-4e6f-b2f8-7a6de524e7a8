<?php

namespace AwardForce\Modules\Menu\Galleries;

use AwardForce\Modules\Menu\Galleries\Links\Gallery as GalleryLink;
use Platform\Menu\Context\Menu;

class Gallery extends Menu
{
    public function name(): string
    {
        return 'gallery';
    }

    public function text(): string
    {
        return trans('gallery.titles.main');
    }

    public function menuItems(): array
    {
        return [
            new GalleryLink,
        ];
    }

    public function icon(): string
    {
        return 'gallery';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
