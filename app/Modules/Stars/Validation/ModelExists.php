<?php

namespace AwardForce\Modules\Stars\Validation;

use Illuminate\Contracts\Validation\Rule;

class ModelExists implements Rule
{
    protected $model;
    private $modelClass;

    public function __construct(string $model)
    {
        $this->modelClass = $model;
        $this->model = new $model;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        return $this->model->newQuery()
            ->whereSlug($value)
            ->count() > 0;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->modelClass.' with this slug does not exist.';
    }
}
