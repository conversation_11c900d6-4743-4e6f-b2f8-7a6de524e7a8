<?php

namespace AwardForce\Modules\Categories\Commands;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Library\Filesystem\Courier;
use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Features\Services\Feature;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\FileProcessor;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Support\Collection;
use Platform\Events\EventDispatcher;

class SeedDefaultCategoriesCommandHandler
{
    use EventDispatcher;

    /** @var CategoryRepository */
    private $categories;

    /** @var SeasonRepository */
    private $seasons;

    /** @var ChapterRepository */
    private $chapters;

    /** @var Courier */
    private $courier;

    /** @var Feature */
    private $features;

    public function __construct(
        CategoryRepository $categories,
        SeasonRepository $seasons,
        ChapterRepository $chapters,
        Storage $storage,
        Feature $features
    ) {
        $this->categories = $categories;
        $this->seasons = $seasons;
        $this->chapters = $chapters;
        $this->courier = $storage->courier();
        $this->features = $features;
    }

    public function handle(SeedDefaultCategoriesCommand $command)
    {
        $seasonId = $this->seasons->getActiveId();

        foreach (config('seeds.'.Vertical::translationsKey().'.categories') as $alias => $data) {
            $category = Category::add($seasonId, FormSelector::defaultForSeason($seasonId)->id, true);

            $this->categories->save($category);

            $this->syncChapters($category, $seasonId);

            $this->addTranslations($category, $alias, $command->account->languages);

            if ($this->features->enabled('sponsors') && strlen($data['image'])) {
                $this->attachCategoryImage($category->id, $data['image']);
            }

            $this->dispatch($category->releaseEvents());
        }
    }

    private function attachCategoryImage(int $category, $image)
    {
        $seedFile = base_path(implode(DIRECTORY_SEPARATOR, ['resources', 'assets', 'img', $image]));
        $remoteFile = FileProcessor::filename('png');

        $this->courier->copySeedToRemote($seedFile, $remoteFile);

        $record = File::prepare(
            'category-sponsor.png',
            $remoteFile,
            File::RESOURCE_CATEGORIES,
            $category
        );
        $record->update([
            'mime' => 'image/png',
            'size' => filesize($seedFile),
            'status' => File::STATUS_OK,
        ]);

        $this->dispatch($record->releaseEvents());
    }

    /**
     * @param  Category  $category
     * @param  int  $seasonId
     */
    private function syncChapters($category, $seasonId)
    {
        $chapterIds = $this->chapters->getForSeason($seasonId)->just('id');

        $this->categories->syncChapters($category, $chapterIds);
    }

    /**
     * @param  Category  $category
     * @param  string  $alias
     * @param  Collection  $supportedLanguages
     */
    private function addTranslations($category, $alias, $supportedLanguages)
    {
        $this->addTranslation($category, $alias, $supportedLanguages, 'name');
        $this->addTranslation($category, $alias, $supportedLanguages, 'description');
        $this->addTranslation($category, $alias, $supportedLanguages, 'shortcode');
        if ($this->features->enabled('sponsors')) {
            $this->addTranslation($category, $alias, $supportedLanguages, 'image_heading');
        }
    }

    /**
     * @param  Category  $category
     * @param  string  $alias
     * @param  Collection  $supportedLanguages
     * @param  string  $key
     */
    private function addTranslation($category, $alias, $supportedLanguages, $key)
    {
        $translationKey = 'category.'.Vertical::translationsKey().'.'.$alias.'.'.$key;
        foreach ($supportedLanguages as $language) {
            $category->saveTranslation(
                $langCode = $language->code,
                $key,
                (trans()->has($translationKey, $langCode) ? trans($translationKey, [], $langCode) : ''),
                $category->accountId
            );
        }
    }
}
