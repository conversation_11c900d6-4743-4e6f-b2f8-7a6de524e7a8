<?php

namespace AwardForce\Modules\ScoreSets\Commands;

use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\ScoreSets\Events\ScoreSetWasCreated;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSetSettings;
use AwardForce\Modules\ScoreSets\Values\RefereeSettings;
use Illuminate\Support\Facades\Event;
use Mockery as m;
use Tectonic\LaravelLocalisation\Database\TranslationService;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class CreateScoreSetCommandHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function test_create_score_set_from_input(): void
    {
        Event::fake();

        $scoreSets = m::mock(ScoreSetRepository::class);
        $scoreSets->shouldReceive('save')
            ->with(m::type(ScoreSet::class))
            ->andReturnUsing(fn(ScoreSet $scoreSet) => $scoreSet->setAttribute('id', 1))
            ->once();

        $translations = m::mock(TranslationService::class);
        $translations->shouldReceive('sync')->with(m::type(ScoreSet::class), ['field' => 'value'])->once();

        $files = m::mock(FileRepository::class);
        $files->shouldReceive('updateResourceByTokens')->with(['token_string'], 1)->once();

        $handler = new CreateScoreSetCommandHandler($scoreSets, $translations, $files);

        $result = $handler->handle(new CreateScoreSetCommand(
            11,
            12,
            ['field' => 'value'],
            'top_pick',
            new ScoreSetSettings(),
            null,
            null,
            22,
            'token_string',
            [33, 44],
            [],
            ['comments' => 1],
            [],
            [],
            [],
            [],
            ['topPickWinners' => 2],
            [],
            [],
            []
        ));

        Event::assertDispatched(ScoreSetWasCreated::class);
        $this->assertInstanceOf(ScoreSet::class, $result);
        $this->assertEquals(11, $result->seasonId);
        $this->assertEquals(2, $result->topPickWinners);
    }

    public function testCreateScoreSetWithSettings()
    {
        Event::fake();

        $scoreSets = m::mock(ScoreSetRepository::class);
        $scoreSets->shouldReceive('save')->with(m::type(ScoreSet::class))->once();

        $translations = m::mock(TranslationService::class);
        $translations->shouldReceive('sync')->with(m::type(ScoreSet::class), ['field' => 'value'])->once();

        $files = m::mock(FileRepository::class);
        $files->shouldNotReceive('updateResourceByTokens');

        $handler = new CreateScoreSetCommandHandler($scoreSets, $translations, $files);

        $result = $handler->handle(new CreateScoreSetCommand(
            seasonId: 11,
            formId: 12,
            translated: ['field' => 'value'],
            mode: 'top_pick',
            settings: new ScoreSetSettings(new RefereeSettings(true)),
            comments: ['internalComments' => true]
        ));

        Event::assertDispatched(ScoreSetWasCreated::class);
        $this->assertInstanceOf(ScoreSetSettings::class, $result->settings);
        $this->assertInstanceOf(RefereeSettings::class, $result->settings->referee);
        $this->assertTrue($result->settings->referee->displayReferees);
        $this->assertTrue($result->internalComments);
    }
}
