<?php

namespace AwardForce\Modules\ScoreSets\Commands;

use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Platform\Events\EventDispatcher;

class DeleteScoreSetsCommandHandler
{
    use EventDispatcher;

    /** @var ScoreSetRepository */
    private $scoreSets;

    public function __construct(ScoreSetRepository $scoreSets)
    {
        $this->scoreSets = $scoreSets;
    }

    public function handle(DeleteScoreSetsCommand $command)
    {
        $this->scoreSets->getByIds($command->selected)
            ->reject(function (ScoreSet $scoreSet) {
                return $scoreSet->protected;
            })->each(function (ScoreSet $scoreSet) {
                $this->scoreSets->delete($scoreSet);
                $this->dispatch($scoreSet->releaseEvents());
            });
    }
}
