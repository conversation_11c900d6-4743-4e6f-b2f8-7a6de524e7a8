<?php

namespace AwardForce\Modules\ScoreSets\Models;

use Platform\Database\Eloquent\Collection;

class ScoreSetCollection extends Collection
{
    public function withoutGalleries()
    {
        return $this->reject(function ($scoreSet) {
            return $scoreSet->mode == ScoreSet::MODE_GALLERY;
        });
    }

    public function searchFieldIds(): array
    {
        return $this->map(function (ScoreSet $scoreSet) {
            return $scoreSet->searchFields ?: [];
        })->collapse()->unique()->all();
    }

    /**
     * @return Collection
     */
    public function withSlideshow()
    {
        return $this->filter(function ($scoreSet) {
            return $scoreSet->slideshow;
        });
    }

    /**
     * Returns true if at least one ScoreSet in the collection has the given setting enabled.
     */
    public function settingEnabled(callable|string $setting): bool
    {
        return $this->max($setting) ?: false;
    }

    /**
     * Returns true if all of the ScoreSets in the collection have the given setting enabled.
     */
    public function settingAllEnabled(callable|string $setting): bool
    {
        return $this->isNotEmpty() && $this->every($setting);
    }

    /**
     * Returns the order mode with highest priority found in the collection.
     */
    public function orderControl(): string
    {
        foreach (ScoreSet::$orderModes as $orderMode) {
            if ($this->contains('orderControl', $orderMode)) {
                return $orderMode;
            }
        }

        return ScoreSet::ORDER_RANDOM;
    }

    public function forGroupedSelect(bool $withEmptyOption = false)
    {
        $grouped = $this->groupBy('formId')->values();
        $mapped = $grouped->count() > 1 ? $this->mapWithGroups($grouped) : $this->mapWithoutGroups($grouped);

        return $withEmptyOption ? $mapped->prepend(['id' => '', 'name' => '']) : $mapped;
    }

    protected function mapWithGroups(ScoreSetCollection $scoreSets)
    {
        return $scoreSets->map(
            fn(ScoreSetCollection $scoreSets) => [
                'id' => $scoreSets->first()->formId,
                'name' => $scoreSets->first()->form?->name,
                'children' => $scoreSets->map(fn(ScoreSet $scoreSet) => ['id' => $scoreSet->id, 'name' => $scoreSet->name])
                    ->sortBy('name', SORT_STRING | SORT_FLAG_CASE)
                    ->values(),
            ])
            ->sortBy('name', SORT_STRING | SORT_FLAG_CASE)
            ->values();
    }

    protected function mapWithoutGroups(ScoreSetCollection $scoreSets)
    {
        return $scoreSets->flatten()
            ->map(fn(ScoreSet $scoreSet) => ['id' => $scoreSet->id, 'name' => $scoreSet->name])
            ->sortBy('name', SORT_STRING | SORT_FLAG_CASE)
            ->values();
    }
}
