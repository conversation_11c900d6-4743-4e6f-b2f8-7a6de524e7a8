<?php

namespace AwardForce\Modules\ScoreSets;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\ScoreSets\Events\ScoreSetWasCopied;
use AwardForce\Modules\ScoreSets\Listeners\ActiveSeasonListener;
use AwardForce\Modules\ScoreSets\Listeners\CopyScoreSetListener;
use AwardForce\Modules\ScoreSets\Models\EloquentScoreSetRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\Seasons\Events\SeasonWasMarkedAsActive;
use AwardForce\Modules\Seasons\Events\SeasonWasMarkedAsArchived;
use Platform\Localisation\Listeners\CopyTranslationsListener;

class ScoreSetServiceProvider extends ServiceProvider
{
    /**
     * @var bool
     */
    public $defer = true;

    /**
     * An array of the listeners that need to be registered with the system. The key should
     * refer to the event that is fired, and the value should be the class name and method
     * that will handle that event.
     *
     * @var array
     */
    protected $listeners = [
        ScoreSetWasCopied::class => [
            CopyTranslationsListener::class.'@whenModelWasCopied',
            CopyScoreSetListener::class,
        ],
        SeasonWasMarkedAsActive::class => ActiveSeasonListener::class.'@whenSeasonWasMarkedAsActive',
        SeasonWasMarkedAsArchived::class => ActiveSeasonListener::class.'@whenSeasonWasMarkedAsArchived',
    ];

    /**
     * The repository bindings for the Entries module.
     *
     * @var array
     */
    protected $repositories = [
        ScoreSetRepository::class => EloquentScoreSetRepository::class,
    ];
}
