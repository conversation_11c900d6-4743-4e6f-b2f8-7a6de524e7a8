<?php

namespace AwardForce\Modules\Grants\Listeners;

use AwardForce\Modules\Accounts\Events\AccountWasCreated;
use AwardForce\Modules\Grants\Commands\SeedDefaultGrantStatuses;
use Illuminate\Foundation\Bus\DispatchesJobs;

class Account
{
    use DispatchesJobs;

    public function whenAccountWasCreated(AccountWasCreated $event)
    {
        if ($event->account->isGoodGrants()) {
            $this->dispatch(new SeedDefaultGrantStatuses($event->account));
        }
    }
}
