<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Modules\Payments\Contracts\NAB as NABInterface;
use AwardForce\Modules\Payments\Response;
use DOMDocument;
use Exception;
use Illuminate\Support\Str;
use SimpleXMLElement;

class NABTransact extends AbstractGateway implements NABInterface
{
    /**
     * NAB API merchant ID
     *
     * @var string
     */
    protected $merchantId;

    /**
     * NAB API password
     *
     * @var string
     */
    protected $password;

    /**
     * Test mode
     *
     * @var bool
     */
    protected $testMode;

    /**
     * Currency code to use for transaction
     *
     * @var string
     */
    protected $currency;

    /**
     * Lists the codes that facilitate an approved purchase
     *
     * @var array
     */
    public static $approvedCodes = ['00', '08', '11', '16'];

    /**
     * @param  string  $merchantId
     * @param  string  $password
     * @param  bool  $testMode
     */
    public function __construct($merchantId, $password, $testMode = false)
    {
        $this->merchantId = $merchantId;
        $this->password = $password;
        $this->testMode = $testMode;
    }

    /**
     * Handle making the purchase
     *
     * @param  array  $data
     * @return \AwardForce\Modules\Payments\Contracts\Response
     *
     * @throws Exception
     */
    public function purchase($amount, $data = [])
    {
        $payload = $this->generatePayload($amount, $data);
        $raw = $this->createRequest($payload);

        if (empty($raw)) {
            throw new Exception('There appears to be a configuration problem with your provider setup. Check to ensure your merchant ID is set correctly, and that you have test mode set correctly (true if testing, false otherwise)');
        }
        // Execute and parse the response from NAB Transact
        $xml = $this->parseResponse($raw);

        // Return the response for the transaction
        return $this->response($xml);
    }

    /**
     * Get test mode
     *
     * @return bool
     */
    public function getTestMode()
    {
        return $this->testMode;
    }

    /**
     * Set test mode
     *
     *
     * @return void
     */
    public function setTestMode($mode)
    {
        $this->testMode = $mode;
    }

    /**
     * Get currency code
     *
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * Set the currency code
     *
     * @param  string  $currency
     * @return void
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * Formulates the response and returns a Response object
     *
     *
     * @return Response
     */
    public function response(SimpleXMLElement $xml)
    {
        // If the Payment property is not available, then it's likely ya configuration error.
        if (! $xml->Payment) {
            return new Response(
                false,
                $xml->Status->statusDescription
            );
        }

        // Success is determined by first looking at the status provided by NAB Transact.
        // We then also check to ensure that the code provided matches the approved codes above.
        $success = $this->xmlValue($xml->Payment->TxnList->Txn->responseText) === 'Approved' &&
            in_array(substr($xml->Payment->TxnList->Txn->responseCode, 0, 2), self::$approvedCodes);

        // Next we look at the XML object and return a standard Response
        return new Response(
            $success,
            $this->parseMessage($this->xmlValue($xml->Payment->TxnList->Txn->responseText)),
            $this->xmlValue($xml->Payment->TxnList->Txn->txnID),
            $this->formatAmount($this->xmlValue($xml->Payment->TxnList->Txn->amount)),
            $this->xmlValue($xml->Payment->TxnList->Txn->currency)
        );
    }

    /**
     * Generate XML payload
     *
     * @param  array  $data
     * @return string
     */
    private function generatePayload($amount, $data = [])
    {
        $name = $this->parseName(array_get($data, 'name', ''));

        // Create the XML document
        $dom = new DOMDocument('1.0', 'iso-8859-1');
        $dom->formatOutput = true; // Allows for better debugging

        // Create the XML document's root element
        $root = $dom->createElement('NABTransactMessage');

        // Message info
        $message_info = $dom->createElement('MessageInfo');
        $message_info->appendChild($dom->createElement('messageId', 'AF'.time()));
        $message_info->appendChild($dom->createElement('messageTimestamp', (new \DateTime())->format('Ydmis').'000000+600'));
        $message_info->appendChild($dom->createElement('timeoutValue', '60'));
        $message_info->appendChild($dom->createElement('apiVersion', 'xml-4.2'));

        // Merchant info
        $merchant_info = $dom->createElement('MerchantInfo');
        $merchant_info->appendChild($dom->createElement('merchantID', $this->merchantId));
        $merchant_info->appendChild($dom->createElement('password', $this->password));

        // Payment info
        $payment_info = $dom->createElement('Payment');
        $txn_list = $dom->createElement('TxnList');
        $txn_list->setAttribute('count', 1);
        $txn = $dom->createElement('Txn');
        $txn->setAttribute('ID', 1);
        $txn->appendChild($dom->createElement('txnType', 0));
        $txn->appendChild($dom->createElement('txnSource', 23));
        $txn->appendChild($dom->createElement('txnChannel', 0));
        $txn->appendChild($dom->createElement('amount', number_format($amount * 100, 0, '.', '')));
        $txn->appendChild($dom->createElement('currency', $this->getCurrency()));
        $txn->appendChild($dom->createElement('purchaseOrderNo', Str::random(16)));

        // CC info
        $cc_info = $dom->createElement('CreditCardInfo');
        $cc_info->appendChild($dom->createElement('cardHolderName', array_get($data, 'name', '')));
        $cc_info->appendChild($dom->createElement('cardNumber', array_get($data, 'cardNumber', '')));
        $cc_info->appendChild($dom->createElement('expiryDate', array_get($data, 'expiryMonth', '').'/'.array_get($data, 'expiryYear', '')));
        $cc_info->appendChild($dom->createElement('cvv', array_get($data, 'cvv', '')));
        $cc_info->appendChild($dom->createElement('recurringFlag', 'No'));

        // Buyer info
        $buyer_info = $dom->createElement('BuyerInfo');
        $buyer_info->appendChild($dom->createElement('firstName', $name['first']));
        $buyer_info->appendChild($dom->createElement('lastName', $name['last']));

        $txn->appendChild($cc_info);
        $txn->appendChild($buyer_info);
        $txn_list->appendChild($txn);
        $payment_info->appendChild($txn_list);

        // Append sub elements to root
        $root->appendChild($message_info);
        $root->appendChild($merchant_info);
        $root->appendChild($dom->createElement('RequestType', 'Payment'));
        $root->appendChild($payment_info);

        // Now append the root element to the document
        $dom->appendChild($root);

        return $dom->saveXML();
    }

    /**
     * Creates the request and returns the raw request XML
     *
     *
     * @return string
     */
    private function createRequest($payload)
    {
        $ch = curl_init($this->gatewayURL());

        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 240);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);

        return $response;
    }

    /**
     * Returns the string for the response message
     *
     * @param  string  $message
     * @return string
     */
    public function parseMessage($message)
    {
        return is_numeric(substr($message, 0, 2)) ? substr($message, 4) : $message;
    }

    /**
     * Parses the raw response returned from the gateway, and returns an
     * XML dom object that can be easily queried for data.
     *
     * @param  string  $response
     * @return \SimpleXMLElement
     */
    private function parseResponse($response)
    {
        return new SimpleXMLElement($response);
    }

    /**
     * Returns the true string value of a SimpleXMLElement
     *
     * @param  SimpleXMLElement  $element
     * @return string
     */
    protected function xmlValue($element)
    {
        return sprintf('%s', $element);
    }

    /**
     * Switches the gateway to be used, based on the test mode
     *
     * @return string
     */
    private function gatewayURL()
    {
        return $this->getTestMode() ? 'https://transact.nab.com.au/test/xmlapi/payment' : 'https://transact.nab.com.au/live/xmlapi/payment';
    }

    /**
     * Parses a name string into first/last name array elements.
     *
     * @param  string  $name
     * @return array
     */
    private function parseName($name)
    {
        $name = explode(' ', $name);

        $first = array_shift($name);
        $last = implode(' ', $name);

        return compact('first', 'last');
    }

    protected function gatewayName(): string
    {
        return 'nabtransact';
    }
}
