<?php

namespace AwardForce\Modules\Payments\Listeners;

use AwardForce\Modules\Ecommerce\Cart\Events\CartWasProcessed;
use AwardForce\Modules\Payments\Commands\LogDiscountCodeUseCommand;
use Illuminate\Foundation\Bus\DispatchesJobs;

class Cart
{
    use DispatchesJobs;

    public function whenCartWasProcessed(CartWasProcessed $event)
    {
        $discount = $event->cart->get('discountCode');

        if (! $discount) {
            return;
        }

        $command = new LogDiscountCodeUseCommand(
            $event->cart->get('discountCode'),
            $event->cart->userId()
        );

        $this->dispatch($command);
    }
}
