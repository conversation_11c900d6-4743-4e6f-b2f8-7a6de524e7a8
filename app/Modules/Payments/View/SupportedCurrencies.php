<?php

namespace AwardForce\Modules\Payments\View;

use AwardForce\Modules\Accounts\Contracts\SupportedCurrencyRepository;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Support\Collection;
use Platform\View\View;

class SupportedCurrencies extends View
{
    private $supportedCurrencies;
    private $repository;

    public function __construct(
        SupportedCurrencyRepository $supportedCurrencies,
        SettingRepository $repository
    ) {
        $this->supportedCurrencies = $supportedCurrencies;
        $this->repository = $repository;
    }

    public function settings()
    {
        return $this->repository->getAllAsKeyValue();
    }

    public function supportedCurrencies(): Collection
    {
        return $this->supportedCurrencies->getAll()->sortBy('code');
    }

    public function defaultCurrency()
    {
        $defaultCurrency = $this->supportedCurrencies->getDefault();

        return $defaultCurrency ? $defaultCurrency->code : null;
    }
}
