<?php

namespace AwardForce\Modules\Payments\View;

use AwardForce\Modules\Payments\Services\MaskedSettings;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class GatewaysTest extends BaseTestCase
{
    use Laravel;

    public function testSettingsReturnsUnmaskedMaskedAndPartiallyMaskedValues(): void
    {
        $repositoryMock = $this->mock(SettingRepository::class);
        $repositoryMock->shouldReceive('getAllAsKeyValue')
            ->once()
            ->andReturn([
                'key1' => 'value1234',
                'key2' => 'value2345',
                'key3' => 'value3456',
            ]);

        Config::shouldReceive('array')
            ->with('awardforce.masked-settings')
            ->once()
            ->andReturn(['key1']);
        Config::shouldReceive('array')
            ->with('awardforce.partially-masked-settings')
            ->once()
            ->andReturn(['key2']);

        $view = new Gateways(new MaskedSettings($repositoryMock), $this->mock(Request::class));
        $result = $view->settings();

        $this->assertEquals([
            'key1' => '*********',
            'key2' => 'value2***',
            'key3' => 'value3456',
        ], $result);
    }
}
