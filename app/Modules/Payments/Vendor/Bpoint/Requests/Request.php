<?php

namespace AwardForce\Modules\Payments\Vendor\Bpoint\Requests;

use AwardForce\Modules\Payments\Vendor\Bpoint\Utils\RequestSender;
use AwardForce\Modules\Payments\Vendor\Bpoint\Utils\URLDirectory;

abstract class Request
{
    protected $url;
    protected $mode;
    protected $method;
    protected $authHeader;
    private $username;
    private $password;
    private $merchantNumber;
    private $baseUrl;
    private $urlSuffix;
    private $userAgent;
    private $timeout;

    public function __construct()
    {
        $this->mode = null;
        $this->username = null;
        $this->password = null;
        $this->merchantNumber = null;
        $this->userAgent = 'Premier.Billpay.API.BPOINT.PHP-V1.0';
        $this->timeout = 100000;
    }

    abstract public function submit();

    public function setCredentials($credentials)
    {
        $this->setMode($credentials->getMode());
        $this->setUsername($credentials->getUsername());
        $this->setPassword($credentials->getPassword());
        $this->setMerchantNumber($credentials->getMerchantNumber());
    }

    public function setMode($mode)
    {
        $this->mode = $mode;
        $this->baseUrl = URLDirectory::getBaseURL($this->mode);
    }

    public function setUsername($username)
    {
        $this->username = $username;
        $this->setAuthHeader();
    }

    protected function setAuthHeader()
    {
        if ($this->username === null || $this->password === null || $this->merchantNumber === null) {
            return;
        }
        $this->authHeader = base64_encode($this->username.'|'.$this->merchantNumber.':'.$this->password);

        if ($this->userAgent != null) {
            RequestSender::setUserAgent($this->userAgent);
        }

        RequestSender::setTimeout($this->timeout);
    }

    public function setTimeout($timeout)
    {
        $this->timeout = $timeout;
    }

    public function setPassword($password)
    {
        $this->password = $password;
        $this->setAuthHeader();
    }

    public function setMerchantNumber($merchantNumber)
    {
        $this->merchantNumber = $merchantNumber;
        $this->setAuthHeader();
    }

    protected function setURL($suffix)
    {
        $this->urlSuffix = $suffix;
        if ($this->baseUrl == null) {
            return;
        }
        $this->url = $this->baseUrl.$this->urlSuffix;
    }

    protected function getMethod()
    {
        return $this->method;
    }

    protected function setMethod($method)
    {
        $this->method = $method;
    }

    protected function prepare()
    {
        $this->url = $this->baseUrl.$this->urlSuffix;
        $this->setAuthHeader();
    }
}
