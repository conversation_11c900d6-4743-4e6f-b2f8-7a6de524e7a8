<?php

namespace AwardForce\Modules\Payments\Vendor\Bpoint\Requests;

use AwardForce\Modules\Payments\Vendor\Bpoint\Responses\TokenResponse;
use AwardForce\Modules\Payments\Vendor\Bpoint\Utils\RequestSender;

class UpdateDVToken extends Request
{
    private $dvtoken;

    public function __construct($dvtoken = null)
    {
        parent::__construct();
        $this->setMethod('PUT');
        $this->dvtoken = $dvtoken;
    }

    public function setToken($dvtoken)
    {
        $this->dvtoken = $dvtoken;
    }

    public function getToken()
    {
        return $this->dvtoken;
    }

    public function submit()
    {
        $payload = $this->createPayload();
        $this->setURL('/dvtokens/'.$this->dvtoken);

        $response = RequestSender::send($this->url, $this->authHeader, $payload, $this->method);

        return new TokenResponse($response);
    }
}
