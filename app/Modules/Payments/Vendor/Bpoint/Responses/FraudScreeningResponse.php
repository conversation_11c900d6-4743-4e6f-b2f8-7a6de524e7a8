<?php

namespace AwardForce\Modules\Payments\Vendor\Bpoint\Responses;

class FraudScreeningResponse
{
    private $txnRejected;
    private $responseCode;
    private $responseMessage;
    private $reDResponse;

    public function __construct($responseArray)
    {
        if (isset($responseArray->TxnRejected)) {
            $this->txnRejected = $responseArray->TxnRejected;
        }
        if (isset($responseArray->ResponseCode)) {
            $this->responseCode = $responseArray->ResponseCode;
        }
        if (isset($responseArray->ResponseMessage)) {
            $this->responseMessage = $responseArray->ResponseMessage;
        }
        if (isset($responseArray->ReDResponse)) {
            $this->reDResponse = $responseArray->ReDResponse;
        }
    }

    public function getTxnRejected()
    {
        return $this->txnRejected;
    }

    public function setTxnRejected($txnRejected)
    {
        $this->txnRejected = $txnRejected;

        return $this;
    }

    public function getResponseCode()
    {
        return $this->responseCode;
    }

    public function setResponseCode($responseCode)
    {
        $this->responseCode = $responseCode;

        return $this;
    }

    public function getResponseMessage()
    {
        return $this->responseMessage;
    }

    public function setResponseMessage($responseMessage)
    {
        $this->responseMessage = $responseMessage;

        return $this;
    }

    public function getReDResponse()
    {
        return $this->reDResponse;
    }

    public function setReDResponse($reDResponse)
    {
        $this->reDResponse = $reDResponse;

        return $this;
    }
}
