<?php

namespace AwardForce\Modules\Payments;

use AwardForce\Modules\Payments\Contracts\Response as ResponseInterface;

class Response implements ResponseInterface
{
    /**
     * @var bool
     */
    protected $success;

    /**
     * @var string
     */
    protected $message;

    /**
     * @var string
     */
    protected $transactionRef;

    /**
     * @var float
     */
    protected $amount;

    /**
     * @var string
     */
    protected $currency;

    /**
     * @param  bool  $success
     * @param  string  $message
     * @param  string  $transactionRef
     * @param  float  $amount
     * @param  string  $currency
     */
    public function __construct($success, $message = '', $transactionRef = '', $amount = 0.00, $currency = '')
    {
        $this->success = $success;
        $this->message = $message;
        $this->transactionRef = $transactionRef;
        $this->amount = $amount;
        $this->currency = $currency;
    }

    /**
     * Return the transaction reference returned by the payment provider
     *
     * @return string
     */
    public function transactionRef()
    {
        return $this->transactionRef;
    }

    /**
     * Determine if the payment was successful
     *
     * @return bool
     */
    public function success()
    {
        return $this->success == true;
    }

    /**
     * Determine if the payment failed
     *
     * @return bool
     */
    public function failed()
    {
        return $this->success == false;
    }

    /**
     * Return the message success/error returned by the payment provider
     *
     * @return string
     */
    public function message()
    {
        return $this->message;
    }

    /**
     * Return the amount charged
     *
     * @return float
     */
    public function amount()
    {
        return $this->amount;
    }

    /**
     * Return the currency used
     *
     * @return string
     */
    public function currency()
    {
        return $this->currency;
    }

    /**
     * Is it a redirect?
     *
     * @return bool
     */
    public function isRedirect()
    {
        return false;
    }
}
