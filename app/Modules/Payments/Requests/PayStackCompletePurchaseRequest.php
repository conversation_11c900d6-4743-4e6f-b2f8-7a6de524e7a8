<?php

namespace AwardForce\Modules\Payments\Requests;

use AwardForce\Modules\Payments\Exceptions\InvalidRequestException;
use AwardForce\Modules\Payments\Responses\PayStackCompletePurchaseResponse;
use Exception;

class PayStackCompletePurchaseRequest extends PayStackAbstractRequest
{
    /**
     * {@inheritDoc}
     */
    public function getData(): array
    {
        return [];
    }

    /**
     * {@inheritDoc}
     */
    public function sendData($data)
    {
        try {
            $response = $this->sendRequest('GET', $this->getApiEndpoint(), $data);
        } catch (Exception $e) {
            throw new InvalidRequestException(trans('payments.transaction_status.failed'), $e->getCode(), $e);
        }

        return $this->response = new PayStackCompletePurchaseResponse($this, $response);
    }

    /**
     * {@inheritDoc}
     */
    public function getApiEndpoint()
    {
        return '/transaction/verify/'.rawurlencode($this->getParameter('reference'));
    }
}
