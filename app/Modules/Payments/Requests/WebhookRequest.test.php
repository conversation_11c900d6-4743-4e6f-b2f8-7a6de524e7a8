<?php

namespace AwardForce\Modules\Payments\Requests;

use AwardForce\Modules\Payments\Contracts\Gateway;
use AwardForce\Modules\Payments\GatewayManager;
use Eloquence\Behaviours\Slug;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class WebhookRequestTest extends BaseTestCase
{
    use Laravel;

    protected function init()
    {
        app()->instance(GatewayManager::class, $manager = mock(GatewayManager::class));
        $manager->shouldReceive('create')->once()->andReturn(new FakeGateway);
    }

    public function testExtractsCartIfAvailable()
    {
        $request = WebhookRequest::create('test', 'POST', ['cart_slug' => 'test_cart']);

        $request->setContainer(app());
        $request->validateResolved();

        $this->assertEquals(new Slug('test_cart'), $request->cartSlug);
    }

    public function testDoesNotExtractCartIfNotAvailable()
    {
        $request = WebhookRequest::create('test', 'POST');

        $request->setContainer(app());
        $request->validateResolved();

        $this->assertNull($request->cartSlug);
    }
}

class FakeGateway implements Gateway
{
    protected function gatewayName(): string
    {
        return 'fake';
    }

    public function extractCart(WebhookRequest $request): ?Slug
    {
        if ($slug = $request->input('cart_slug')) {
            return new Slug($slug);
        }

        return null;
    }

    public function validate(WebhookRequest $request): bool
    {
        return true;
    }

    public function getTestMode()
    {
    }

    public function setTestMode($mode)
    {
    }

    public function getCurrency()
    {
    }

    public function setCurrency($currency)
    {
    }

    public function purchase($amount, $data = [])
    {
    }
}
