<?php

namespace AwardForce\Modules\Payments\Search\Columns;

use Illuminate\Support\Collection;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Defaults;

class CountryName implements ApiColumn, Column
{
    public function title()
    {
        return '';
    }

    public function name(): string
    {
        return 'tax.country_name';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return 'taxes.country';
    }

    public function value($record)
    {
        return $record->countryName;
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 79;
    }

    public function sortable(): bool
    {
        return true;
    }

    public function apiValue($record)
    {
        return $this->value($record);
    }

    public function apiName()
    {
        return 'country_name';
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
