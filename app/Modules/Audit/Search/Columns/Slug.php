<?php

namespace AwardForce\Modules\Audit\Search\Columns;

use AwardForce\Modules\Audit\Data\EventLog;
use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Slug implements Column
{
    public function title()
    {
        return trans('fields.table.columns.slug');
    }

    public function name(): string
    {
        return 'event_logs.slug';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field(): string
    {
        return 'slug';
    }

    /**
     * @param  EventLog  $log
     */
    public function value($log): ?string
    {
        return $log->slug;
    }

    public function html($log)
    {
        return $this->value($log);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 60;
    }

    public function sortable(): bool
    {
        return true;
    }
}
