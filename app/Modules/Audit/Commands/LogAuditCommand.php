<?php

namespace AwardForce\Modules\Audit\Commands;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Audit\Events\SystemResource;

class LogAuditCommand
{
    /**
     * @var array
     */
    private $data;

    /**
     * @var Account|null
     */
    private $account;

    /**
     * @var int
     */
    private $userId;

    /**
     * @var string
     */
    private $resource;

    /**
     * @var string
     */
    private $action;

    /**
     * @var string
     */
    private $description;

    /**
     * @var string
     */
    private $ip;

    /**
     * @var int
     */
    private $jediId;

    /**
     * @var int
     */
    private $foreignId;

    /**
     * @var string|null
     */
    private $slug;

    /**
     * LogAuditCommand constructor.
     *
     * @param  Account|null  $account
     * @param  int|null  $userId
     * @param  int|null  $jediId
     * @param  null  $data
     *
     * @internal param int|null $seasonId
     */
    public function __construct($account, $userId, $jediId, string $ip, SystemResource $resource, string $action, string $description, $data = null, int $foreignId = 0, ?string $slug = null)
    {
        $this->account = $account;
        $this->userId = $userId;
        $this->jediId = $jediId;
        $this->ip = $ip;
        $this->resource = $resource;
        $this->action = $action;
        $this->description = $description;
        $this->data = $data;
        $this->foreignId = $foreignId;
        $this->slug = $slug;
    }

    /**
     * @return array|null
     */
    public function data()
    {
        return $this->data;
    }

    /**
     * @return Account|null
     */
    public function account()
    {
        return $this->account;
    }

    /**
     * @return int|null
     */
    public function userId()
    {
        return $this->userId;
    }

    public function ip(): string
    {
        return $this->ip;
    }

    public function resource(): SystemResource
    {
        return $this->resource;
    }

    public function action(): string
    {
        return $this->action;
    }

    public function description(): string
    {
        return $this->description;
    }

    /**
     * @return int|null
     */
    public function jediId()
    {
        return $this->jediId;
    }

    /**
     * @return int|null
     */
    public function foreignId()
    {
        return $this->foreignId;
    }

    public function slug(): ?string
    {
        return $this->slug;
    }
}
