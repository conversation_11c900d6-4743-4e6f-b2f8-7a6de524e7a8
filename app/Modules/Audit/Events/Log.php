<?php

namespace AwardForce\Modules\Audit\Events;

use AwardForce\Library\Request\RequestAudit;
use AwardForce\Modules\Accounts\Models\Account;
use Platform\Database\Eloquent\Model;

final class Log
{
    /**
     * @var string
     */
    private $ip;

    /**
     * @var Account|null
     */
    private $account;

    /**
     * @var string
     */
    private $resource;

    /**
     * @var string
     */
    private $action;

    /**
     * @var string
     */
    private $description;

    /**
     * @var string|null
     */
    private $slug;

    /**
     * @var array
     */
    private $data;

    private int $foreignId;

    /**
     * Log constructor.
     *
     * @param  null  $account
     * @param  null  $data
     */
    private function __construct(string $ip, SystemResource $resource, string $action, string $description, $account = null, $data = null, int $foreignId = 0, ?string $slug = null)
    {
        $this->ip = $ip;
        $this->resource = $resource;
        $this->action = $action;
        $this->description = $description;
        $this->account = $account;
        $this->foreignId = $foreignId;
        $this->slug = $slug;

        $this->setData($data);
    }

    /**
     * Returns a new log instance based on defaults.
     *
     * @param  null  $data
     * @return Log
     */
    public static function withDefaults(SystemResource $resource, string $action, string $description, $data = null, int $foreignId = 0, ?string $slug = null)
    {
        return new Log(RequestAudit::get()->ip(), $resource, $action, $description, current_account(), $data, $foreignId, $slug);
    }

    /**
     * Returns a new log instance based on defaults in the specified account.
     *
     * @param  null  $data
     * @return Log
     */
    public static function inAccount(SystemResource $resource, Account $account, string $action, string $description, $data = null, int $foreignId = 0, ?string $slug = null)
    {
        return new Log(RequestAudit::get()->ip(), $resource, $action, $description, $account, $data, $foreignId, $slug);
    }

    /**
     * @return Account|null
     */
    public function account()
    {
        return $this->account;
    }

    public function resource(): SystemResource
    {
        return $this->resource;
    }

    public function action(): string
    {
        return $this->action;
    }

    public function description(): string
    {
        return $this->description;
    }

    public function ip(): string
    {
        return $this->ip;
    }

    /**
     * @return array|null
     */
    public function data()
    {
        return $this->data;
    }

    public function slug(): ?string
    {
        return $this->slug;
    }

    /**
     * @return int|null
     */
    public function foreignId()
    {
        return $this->foreignId;
    }

    /**
     * Sets the data property based on the type of variable $data is.
     *
     * @param  Model|array|null  $data
     */
    private function setData($data)
    {
        if ($data instanceof Model) {
            $this->data = $data->attributesToArray();

            return;
        }

        if (is_array($data)) {
            $this->data = array_map(static fn($value) => $value instanceof Model ? $value->attributesToArray() : $value, $data);
        }
    }
}
