<?php

namespace AwardForce\Modules\Tags\Search\Columns;

use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Search\Columns\LinkedSeason;
use Illuminate\Support\Collection;
use Platform\Search\Filters\IncludeFilter;

class TagSeason extends LinkedSeason
{
    public function field()
    {
        return 'tags.season_id';
    }

    /**
     * Returns the column's search filter dependencies.
     */
    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
            SeasonalFilter::class,
        ]);
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'tag.season';
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return 1;
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return SeasonFilter::viewingAll();
    }
}
