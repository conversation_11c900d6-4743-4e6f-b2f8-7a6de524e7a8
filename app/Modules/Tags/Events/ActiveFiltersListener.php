<?php

namespace AwardForce\Modules\Tags\Events;

use AwardForce\Modules\Tags\Contracts\TagRepository;
use Illuminate\Support\Collection;

class ActiveFiltersListener
{
    private $tags;

    public function __construct(TagRepository $tags)
    {
        $this->tags = $tags;
    }

    public function handle(Collection $filters)
    {
        if ($filters->has('tag')) {
            $tag = $this->tags->getBySlugOrId($filters->get('tag')->value);

            if (! $tag) {
                return;
            }

            $filters->get('tag')->value = $tag->tag;
            $filters->get('tag')->text = trans('tags.table.columns.tag');
        }
    }
}
