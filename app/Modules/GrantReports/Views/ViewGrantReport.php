<?php

namespace AwardForce\Modules\GrantReports\Views;

use AwardForce\Modules\Forms\Forms\View\SubmittableView;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use Illuminate\Http\Request;

class ViewGrantReport extends SubmittableView
{
    protected function setSubmittable(Request $request): void
    {
        $this->submittable = $this->translator->translate($request->grantReport);
    }

    public function grantReport(): GrantReport
    {
        return $this->submittable;
    }
}
