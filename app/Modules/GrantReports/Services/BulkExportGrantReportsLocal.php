<?php

namespace AwardForce\Modules\GrantReports\Services;

use AwardForce\Modules\Entries\Services\Export\BulkExportService;
use AwardForce\Modules\Entries\Services\Export\DownloadsSubmittableResource;
use AwardForce\Modules\Forms\Forms\Database\Repositories\SubmittableRepository;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;

class BulkExportGrantReportsLocal extends BulkExportService
{
    use DownloadsSubmittableResource;
    use GrantReportDownloads;

    protected function repository(): SubmittableRepository
    {
        return app(GrantReportRepository::class);
    }
}
