<?php

namespace AwardForce\Modules\Accounts\Commands;

use AwardForce\Modules\Accounts\Services\AccountGateway;
use Platform\Events\EventDispatcher;

class DestroyGlobalAccountHandler
{
    use EventDispatcher;

    /** @var AccountGateway */
    private $gateway;

    public function __construct(AccountGateway $gateway)
    {
        $this->gateway = $gateway;
    }

    public function handle(DestroyGlobalAccount $command)
    {
        $this->gateway->destroy($command->globalAccountId());
    }
}
