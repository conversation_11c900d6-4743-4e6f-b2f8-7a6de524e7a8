<?php

namespace AwardForce\Modules\Accounts\Commands;

use AwardForce\Library\Database\DatabaseSelector;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Identity\Users\Contracts\GlobalUserRepository;

class FindManagedAccountsHandler
{
    /** @var DatabaseSelector */
    private $selector;

    /** @var AccountRepository */
    private $accounts;

    /** @var GlobalUserRepository */
    private $globalUsers;

    public function __construct(DatabaseSelector $selector, AccountRepository $accounts, GlobalUserRepository $globalUsers)
    {
        $this->selector = $selector;
        $this->accounts = $accounts;
        $this->globalUsers = $globalUsers;
    }

    public function handle(FindManagedAccounts $command)
    {
        $accounts = collect();
        $globalUser = $this->globalUsers->requireById($command->globalUser);

        foreach ($this->selector->all() as $database) {
            $this->selector->select($database);

            $accounts = $accounts->merge(
                ($localUser = $globalUser->localUser) ? $this->accounts->getManagedBy($localUser) : collect()
            );
        }

        return $accounts;
    }
}
