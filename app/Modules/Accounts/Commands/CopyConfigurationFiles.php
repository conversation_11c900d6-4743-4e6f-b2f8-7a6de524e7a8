<?php

namespace AwardForce\Modules\Accounts\Commands;

use AwardForce\Library\Bus\QueuedJob;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use <PERSON><PERSON>\SerializableClosure\SerializableClosure;

class CopyConfigurationFiles extends QueuedJob
{
    private array $files;

    public function __construct(private string $region, private ?int $resourceId, array $files, private ?SerializableClosure $onCompleteClosure = null)
    {
        $this->files = $files;
        $this->queue = region_queue_name(CurrentAccount::get()->region);
    }

    public function files(): array
    {
        return $this->files;
    }

    public function region(): string
    {
        return $this->region;
    }

    public function resourceId(): ?int
    {
        return $this->resourceId;
    }

    public function onComplete(array $imported): void
    {
        if ($invokable = $this->onCompleteClosure) {
            $invokable($imported);
        }
    }
}
