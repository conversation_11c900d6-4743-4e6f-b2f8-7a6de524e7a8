<?php

namespace AwardForce\Modules\Accounts\Models;

use AwardForce\Library\PaymentSubscriptions\Services\GatewayConfiguration;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Accounts\Exceptions\CannotRelocateToSameRegion;
use AwardForce\Modules\Accounts\Exceptions\ExistingRelocationInProgress;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\NewDashboard\DataObjects\Dashboards;
use AwardForce\Modules\NewDashboard\Enums\DashboardProvider;
use AwardForce\Modules\PaymentSubscriptions\Events\SubscriptionWasProcessed;
use AwardForce\Provisioning\Provisioners\Provisioner;
use Carbon\Carbon;
use Platform\Database\Database;
use Platform\Features\Feature;
use Platform\Features\Plan;
use Platform\Language\Language;
use Platform\Test\EventAssertions;
use Ramsey\Uuid\Uuid;
use Tests\IntegratedTestCase;

final class AccountTest extends IntegratedTestCase
{
    use EventAssertions;

    public function init()
    {
        Domain::unguard();
    }

    public function testFalconId(): void
    {
        $account = $this->muffin(Account::class);

        $databases = [
            'testing' => $account->id.'-testing',
            'what_ever-here.%&^%234_eu_02' => $account->id.'-eu-02',
            'what_ever-here.%&^%234_eu_02_au_03' => $account->id.'-au-03',
            'what_ever-here.%&^%234_euus_03' => $account->id.'-what_ever-here.%&^%234_euus_03',
            'what_ever-here.%&^%234_eu_au_03' => $account->id.'-au-03',
            'what_ever-here.%&^%234_au_02' => $account->id.'-au-02',
            'what_ever-here.%&^%234_us_02' => $account->id.'-us-02',
            'what_ever-here.%&^%234_Eu_02' => $account->id.'-eu-02',
            'what_ever-here.%&^%234_eu_02a' => $account->id.'-what_ever-here.%&^%234_eu_02a',
            'what_ever-here.%&^%234_xx_02' => $account->id.'-what_ever-here.%&^%234_xx_02',
        ];

        Database::$connections = array_keys($databases);

        $globalAccount = new GlobalAccount;
        $globalAccount->id = $account->globalId;
        $account->setRelation('globalAccount', $globalAccount);

        foreach ($databases as $database => $falconId) {
            $globalAccount->database = $database;
            $this->assertEquals($falconId, $account->falconId);
        }
    }

    public function testSuspended(): void
    {
        $account = $this->muffin(Account::class);

        $account->unsuspend();
        $this->assertFalse($account->isSuspended());
        $this->assertNull($account->suspendedAt);

        $account->suspend();
        $this->assertTrue($account->isSuspended());
        $this->assertNotNull($account->suspendedAt);
        $this->assertTrue($account->suspendedAt->gt(now()->subMinute()));
    }

    public function testItCanRetrieveThePlanFromAProductCode(): void
    {
        $account = new Account;
        $account->product = 'USD-m-enterprise-5';

        $this->assertSame('enterprise', (string) $account->plan);
    }

    public function testItSetsMaxFileSizeLimitBasedOnPlan(): void
    {
        $user = $this->muffin(User::class);

        // Init using standard account setup
        $account = Account::setup(
            owner: $user,
            name: 'world awards',
            domain: 'abc.com',
            brand: 'awardforce',
            vertical: 'awards',
            product: 'USD-m-starter-1',
            supportedLanguages: collect(),
            defaultLanguage: new Language('en_GB'),
            globalId: Uuid::uuid4(),
            dashboards: Dashboards::fromArray([]),
        );
        $account->product = 'USD-m-starter-1';

        $this->assertEquals(10, $account->maxFileSize);

        $account->product = 'USD-m-plus-2';
        $account->setMaxFileSizeBasedOnPlan();

        $this->assertEquals(10, $account->maxFileSize);

        $account->product = 'USD-m-enterprise-5';
        $account->setMaxFileSizeBasedOnPlan();

        $this->assertEquals(null, $account->maxFileSize);

        $account->product = 'USD-a-professional-5';
        $account->setMaxFileSizeBasedOnPlan();

        $this->assertEquals(null, $account->maxFileSize);
    }

    public function testReturnDefaultDomain(): void
    {
        $account = $this->muffin(Account::class);
        $account->domains()->createMany([
            ['domain' => 'secondary.domain', 'default' => false],
            ['domain' => 'default.domain', 'default' => true],
        ]);

        $this->assertEquals('default.domain', $account->defaultDomain()->domain);
    }

    public function testSupportedLanguages(): void
    {
        $account = $this->setupAccountWithSupportedLanguages();

        $this->assertInstanceOf(SupportedLanguages::class, $account->supportedLanguages());
        $this->assertCount(3, $account->supportedLanguages());
    }

    public function testSupportedLanguageCodes(): void
    {
        $account = $this->setupAccountWithSupportedLanguages();

        $this->assertEquals(['ja_JP', 'ar_AR', 'en_GB'], $account->supportedLanguageCodes());
    }

    public function testThrowExceptionOnExistingRelocation(): void
    {
        $this->account->relocate('eu');

        $this->expectException(ExistingRelocationInProgress::class);

        $this->account->relocate('us');
    }

    public function testIgnoreExpiredRelocationsWhenMakingNewOnes(): void
    {
        $this->account->relocate('eu')
            ->update(['expiredAt' => Carbon::now()]);

        $relocation = $this->account->relocate('us');

        $this->assertEquals('us', $relocation->to);
    }

    public function testIgnoreCompletedRelocationsWhenMakingNewOnes(): void
    {
        $this->account->relocate('eu')
            ->update(['completedAt' => Carbon::now()]);

        $relocation = $this->account->relocate('us');

        $this->assertEquals('us', $relocation->to);
    }

    public function testThrowExceptionOnRelocationToSameRegion(): void
    {
        $this->account->update(['region' => 'au']);

        $this->expectException(CannotRelocateToSameRegion::class);

        $this->account->relocate('au');
    }

    private function setupAccountWithSupportedLanguages()
    {
        Language::setLanguages(['en_GB' => 'English', 'ar_AR' => 'Arabic', 'ja_JP' => 'Japanese (Japan)']);

        $account = $this->muffin(Account::class);

        $account->languages()->delete();
        $account->addLanguage(new Language('ar_AR'));
        $account->addLanguage(new Language('en_GB'));
        $account->addLanguage(new Language('ja_JP'), true);

        return $account;
    }

    public function testFeaturesOnNewAccount(): void
    {
        foreach (array_keys(config('features.plans')) as $plan) {
            $planFeatures = config("features.plans.{$plan}.features");
            $account = new Account;
            $account->plan = new Plan($plan);
            $account->save();

            $accountFeatures = $account->plan->features()->filter(function (Feature $feature) {
                return $feature->enabled();
            })->map(function (Feature $feature) {
                return $feature->value();
            })->toArray();

            $this->assertSame($planFeatures, $accountFeatures);
        }
    }

    public function testSetSubscription(): void
    {
        $account = new Account;
        $account->trialEndDate = Carbon::now()->addDays(5);
        $account->setSubscription(['customerId' => 3, 'provider' => app(GatewayConfiguration::class)->getCurrentProvider()], Account::SUBSCRIPTION_SUCCESS_STATUS, 'eur-a-intro-2', 5);

        $this->assertSame('eur-a-intro-2', $account->product);
        $this->assertSame('intro', (string) $account->plan);
        $this->assertSame(5, $account->subscriptionId);
        $this->assertSame(current_account_brand(), $account->subscriptionProvider);
        $this->assertSame(3, $account->subscriptionCustomerId);
        $this->assertNull($account->trialEndDate);
        $this->assertRaised($account, SubscriptionWasProcessed::class);
    }

    public function testSetSubscriptionPendingInvoice(): void
    {
        $account = new Account;
        $account->trialEndDate = $endDate = Carbon::now()->addDays(5);
        $account->setSubscription(['customerId' => 3, 'provider' => app(GatewayConfiguration::class)->getCurrentProvider()], Account::SUBSCRIPTION_PENDING_STATUS, 'eur-a-intro-2', 5);

        $this->assertSame('eur-a-intro-2', $account->product);
        $this->assertSame('intro', (string) $account->plan);
        $this->assertSame(5, $account->subscriptionId);
        $this->assertSame(current_account_brand(), $account->subscriptionProvider);
        $this->assertSame(3, $account->subscriptionCustomerId);
        $this->assertEquals($endDate, $account->trialEndDate);
        $this->assertRaised($account, SubscriptionWasProcessed::class);
    }

    public function testBrand(): void
    {
        $account = new Account(['brand' => Account::BRAND_GOODGRANTS]);
        $this->assertTrue($account->isGoodGrants());
        $this->assertFalse($account->isAwardForce());

        $account->brand = Account::BRAND_AWARDFORCE;
        $this->assertFalse($account->isGoodGrants());
        $this->assertTrue($account->isAwardForce());
    }

    public function testDefaultCurrency(): void
    {
        $account = $this->muffin(Account::class);
        $account->setDefaultCurrency($nzd = new Currency('NZD'));

        $this->assertSame($nzd->code, $account->defaultCurrency()->code);

        $account = $this->muffin(Account::class);
        $account->addCurrency($ars = new Currency('ARS'));
        $account->save();
        $this->assertSame($ars->code, $account->defaultCurrency()->code);
    }

    public function testLongAccountNamesTrimmedForSMS(): void
    {
        $account = new Account;
        $account->addTranslation('en_GB', 'name', 'Really, really, really long account name');

        $this->assertSame('Really, really, really long a…', $account->safeSMSName());
    }

    public function testCustomAgreementTest(): void
    {
        $account = new Account;
        $defaultText = $account->defaultAgreementToTerms();
        $this->assertSame($defaultText, $account->agreementToTerms());

        $account->addTranslation('en_GB', 'agreementToTerms', $customText = 'Custom agreement to terms.');
        $this->assertSame($customText, $account->agreementToTerms());
    }

    public function testCustomConsentTest(): void
    {
        $account = new Account;
        $defaultText = $account->defaultConsentToNotificationsAndBroadcasts();
        $this->assertSame($defaultText, $account->consentToNotificationsAndBroadcasts());

        $account->addTranslation('en_GB', 'consentToNotificationsAndBroadcasts', $customText = 'Custom consent.');
        $this->assertSame($customText, $account->consentToNotificationsAndBroadcasts());
    }

    public function testIsIntro(): void
    {
        $account = new Account;

        $account->plan = new Plan(Provisioner::PLAN_ENTERPRISE);
        $this->assertFalse($account->isIntro());

        $account->plan = new Plan(Provisioner::PLAN_INTRO);
        $this->assertTrue($account->isIntro());
    }

    public function testIsGrowth(): void
    {
        $account = new Account;

        $account->plan = new Plan(Provisioner::PLAN_ENTERPRISE);
        $this->assertFalse($account->isGrowth());

        $account->plan = new Plan(Provisioner::PLAN_GROWTH);
        $this->assertTrue($account->isGrowth());
    }

    public function testIsGrowthOrIntro(): void
    {
        $account = new Account;
        $account->plan = new Plan(Provisioner::PLAN_ENTERPRISE);
        $this->assertFalse($account->isGrowthOrIntro());

        $account->plan = new Plan(Provisioner::PLAN_GROWTH);
        $this->assertTrue($account->isGrowthOrIntro());

        $account->plan = new Plan(Provisioner::PLAN_INTRO);
        $this->assertTrue($account->isGrowthOrIntro());
    }

    public function testAccountNameSanitisation(): void
    {
        $this->assertFalse(str_contains($this->account->name, 'Buzz'));

        $this->account->translated['en_GB']['name'] = 'Foo & Buzz O\'M"G';
        $this->account->save();

        // Translated property - fetched using accessor
        $this->assertEquals('Foo & Buzz O\'M"G', $this->account->name);

        // Direct access - accessor not used
        $this->assertEquals('Foo & Buzz O\'M"G', $this->account->translated['en_GB']['name']);
    }

    public function testCustomConsentReturnsFalseIfThereIsNoCustomText(): void
    {
        $this->assertFalse($this->account->customConsentToNotificationsAndBroadcasts());
    }

    public function testCustomConsentReturnsTrueIfCustomTextIsDiferrentThanDefault(): void
    {
        $this->account->addTranslation('en_GB', 'consentToNotificationsAndBroadcasts', 'Custom consent.');

        $this->assertTrue($this->account->customConsentToNotificationsAndBroadcasts());
    }

    public function testRedirectUrlReturnsVanityForUat(): void
    {
        app()->detectEnvironment(fn() => 'uat');
        $this->account->domainRedirect = true;
        $customDomain = $this->account->domains()->create([
            'domain' => 'custom.domain',
            'type' => Domain::TYPE_CUSTOM,
            'default' => true,
        ]);
        $vanityDomain = $this->account->domains->first();

        $this->assertNotEquals($vanityDomain->url(), $customDomain->url());
        $this->assertEquals($vanityDomain->url(), $this->account->redirectUrl(1));
    }

    public function testRedirectUrlReturnsCustomForNonUat(): void
    {
        app()->detectEnvironment(fn() => 'production');
        $this->account->domainRedirect = true;
        $customDomain = $this->account->domains()->create([
            'domain' => 'custom.domain',
            'type' => Domain::TYPE_CUSTOM,
            'default' => true,
        ]);

        $this->assertEquals($customDomain->url(), $this->account->redirectUrl());
    }

    public function testItSetsMaxUploadSizeLimitFromPlanWhenTrialEndDateIsRemoved(): void
    {
        $account = $this->muffin(Account::class, [
            'trial_end_date' => now()->format('Y-m-d'),
            'max_file_size' => 112,
        ]);

        $limit = str_replace('MB', '', config('features.plans.'.$account->plan.'.limits.max_upload'));

        $account->trialEndDate = null;

        $this->assertEquals($limit, $account->maxFileSize);
    }

    public function testItDoesNotSetMaxUploadSizeLimitFromPlanWhenTrialEndDateIsNotChanged(): void
    {
        $account = $this->muffin(Account::class, [
            'trial_end_date' => null,
            'max_file_size' => 112,
        ]);

        $account->trialEndDate = null;

        $this->assertEquals(112, $account->maxFileSize);
    }

    public function testItDoesSetNullDestructionDueDateWhenEmpty(): void
    {
        $account = $this->muffin(Account::class);
        $account->destructionDueDate = '';
        $account->save();

        $this->assertEmpty($account->fresh()->destructionDueDate);
    }

    public function testItCreatesDashboardsOnSetup(): void
    {
        $user = $this->muffin(User::class);
        $dashboards = [
            ['name' => 'Dashboard 1', 'provider' => DashboardProvider::Embeddable->value, 'providerId' => '123e4567-e89b-12d3-a456-************'],
            ['name' => 'Dashboard 2', 'provider' => DashboardProvider::Embeddable->value, 'providerId' => '987fcdeb-51a2-43d7-9876-************'],
        ];

        $account = Account::setup(
            owner: $user,
            name: 'Test Account',
            domain: 'test.com',
            brand: 'awardforce',
            vertical: 'awards',
            product: 'USD-m-starter-1',
            supportedLanguages: collect(),
            defaultLanguage: new Language('en_GB'),
            globalId: Uuid::uuid4(),
            dashboards: Dashboards::fromArray($dashboards),
        );

        $dashboard1 = $account->dashboards->first();
        $dashboard2 = $account->dashboards->last();

        $this->assertCount(2, $account->dashboards);
        $this->assertEquals('Dashboard 1', $dashboard1->name);
        $this->assertEquals('123e4567-e89b-12d3-a456-************', $dashboard1->providerId);
        $this->assertEquals('Dashboard 2', $dashboard2->name);
        $this->assertEquals('987fcdeb-51a2-43d7-9876-************', $dashboard2->providerId);
    }
}
