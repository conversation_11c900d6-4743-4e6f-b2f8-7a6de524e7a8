<?php

namespace AwardForce\Modules\Accounts\Search\Columns;

use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Plan implements Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title()
    {
        return trans('account.table.columns.plan');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'accounts.plan';
    }

    /**
     * Returns the column's search filter dependencies.
     */
    public function dependencies(): Collection
    {
        return collect();
    }

    /**
     * Returns the name of the field in the query that should be present.
     *
     * @return string|null
     */
    public function field()
    {
        return 'accounts.plan';
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return ucfirst($record->plan);
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return $this->value($record);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('export');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Give columns with particularly important information a higher visibility priority.
     */
    public function priority(): int
    {
        return 100;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }
}
