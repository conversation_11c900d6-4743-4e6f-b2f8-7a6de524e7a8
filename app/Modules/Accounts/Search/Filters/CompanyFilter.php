<?php

namespace AwardForce\Modules\Accounts\Search\Filters;

use Illuminate\Contracts\Support\Htmlable;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class CompanyFilter implements ColumnatorFilter, Htmlable, SearchFilter
{
    /**
     * @var array
     */
    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if ($company = array_get($this->input, 'company')) {
            $company = strtolower($company);
            $query->whereRaw('LOWER(JSON_UNQUOTE(JSON_EXTRACT(`hsd_deal`, \'$."companyName"\'))) LIKE ?', ["%{$company}%"]);
        }

        return $query;
    }

    /**
     * Get content as a string of HTML.
     *
     * @return string
     */
    public function toHtml()
    {
        return view('account.search.filters.company')->render();
    }

    /**
     * Return true if this search filter applies to the search in any way.
     */
    public function applies(): bool
    {
        return (bool) ($this->input['company'] ?? false);
    }
}
