<?php

namespace AwardForce\Modules\Accounts\Services;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Models\Account;

/**
 * Class AccountsService
 *
 * The accounts service provides some methods for working with accounts and eases the use of working with
 * 1 or more accounts when logged in as an authenticated consumer that has access to those accounts.
 */
class AccountsService
{
    public function __construct(private AccountRepository $repository)
    {
    }

    /**
     * Find a domain that has been registered with the system.
     */
    public function getAccountForDomain($domain): Account
    {
        return $this->repository->requireByDomain($domain);
    }

    /**
     * Get the total number of accounts setup for this installation.
     */
    public function totalNumberOfAccounts(): int
    {
        return $this->repository->getCount();
    }
}
