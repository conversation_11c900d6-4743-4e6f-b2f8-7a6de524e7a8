<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Copiers;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Notifications\Commands\CreateNotificationCommand;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Notifications\Data\NotificationSettings;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Platform\Events\EventDispatcher;
use Tectonic\LaravelLocalisation\Database\TranslationService;

class NotificationsCopier extends BaseCopier implements Copier, CopyMapper
{
    use CopyMapping;
    use DispatchesJobs, EventDispatcher {
        DispatchesJobs::dispatch insteadof EventDispatcher;
        EventDispatcher::dispatchAll as dispatchEvents;
    }

    protected array $ignoreCopyTerm = [
        'body',
        'smsBody',
    ];

    public function key(): string
    {
        return 'notifications';
    }

    public function export(?int $seasonId = null, ?int $formId = null): void
    {
        $this->repository()
            ->configurationExport($seasonId)
            ->each(fn($notification) => $this->setExported($notification->id, $notification->toArray()));
    }

    public function dependencies(): array
    {
        return [
            SeasonCopier::class,
            FormsCopier::class,
            RolesCopier::class,
            ScoreSetsCopier::class,
            CategoriesCopier::class,
            GrantStatusesCopier::class,
            TagsCopier::class,
            EntryFieldsCopier::class,
            UserFieldsCopier::class,
            SeasonalUserFieldsCopier::class,
        ];
    }

    public function import(
        array $data,
        ?int $seasonId = null,
        ?int $formId = null
    ): void {
        $categoryIds = $data['categoryOption'] === Notification::OPTION_ALL ? [] : array_filter($this->categoryIds($data['categories']));
        $notification = $this->dispatchSync(new CreateNotificationCommand(
            $seasonId,
            $data['trigger'],
            $data['active'],
            $data['senderName'],
            $data['senderAddress'],
            $data['recipients'],
            $data['fieldId'] ? app(FieldsCopier::class)->getImportedId($data['fieldId']) : null,
            $data['recipientOption'],
            $this->mapTranslations($data['translations']),
            $data['legalBasis'],
            NotificationSettings::transform($data['settings']),
            $data['paymentStatusOption'],
            $data['moderationOption'],
            $data['grantStatusOption'] !== GrantStatus::NO_STATUS ? app(GrantStatusesCopier::class)->getImportedId($data['grantStatusOption']) : null,
            $data['formOption'],
            $this->formIds($data['forms']),
            $categoryIds ? $data['categoryOption'] : Notification::OPTION_ALL,
            $categoryIds,
            $data['roleOption'],
            $this->roleIds($data['roles']),
            $data['sendTimeOption'],
            $data['sendTimeUnit'],
            $data['sendTimeOffset'],
            $data['grantReportStatusOption'],
            $data['scoreSetOption'],
            $this->scoreSetIds($data['scoreSets']),
            $this->getTags($data['tags']),
        ));

        $this->mapImportedId($data['id'], $notification->id);
        $this->mapImportedSlug($data['slug'], (string) $notification->slug);

        if ($this->hasMergeFields($data['translations'])) {
            $this->deferred[$notification->id]['translations'] = $data['translations'];
        }
    }

    public function defer(): void
    {
        if (! empty($this->deferred)) {
            foreach ($this->deferred as $id => $data) {
                /** @var Notification $notification */
                $notification = $this->repository()->getById($id);
                app(TranslationService::class)->sync($notification, $this->mapTranslationsWithMergeFields($data['translations']));
            }
        }
    }

    public function repository(): ?Repository
    {
        return app(NotificationRepository::class);
    }

    public function optionalDependencies(): array
    {
        return [
            GrantStatusesCopier::class,
            TagsCopier::class,
            EntryFieldsCopier::class,
            ScoreSetsCopier::class,
            CategoriesCopier::class,
            RolesCopier::class,
            SeasonalUserFieldsCopier::class,
            UserFieldsCopier::class,
        ];
    }

    protected function mapTranslationsWithMergeFields(array $translations): array
    {
        return array_map(
            fn($translation) => array_map([$this, 'replaceFields'], $translation),
            parent::mapTranslations($translations)
        );
    }

    protected function replaceFields(string $translation): string
    {
        $mergeTags = Notification::findMergeTags($translation);

        return str_replace($mergeTags,
            array_map([$this, 'getFieldSlug'],
                $mergeTags
            ),
            $translation
        );
    }

    private function hasMergeFields(mixed $translations)
    {
        foreach ($translations as $translation) {
            if (Notification::findMergeTags($translation['value'])) {
                return true;
            }
        }
    }
}
