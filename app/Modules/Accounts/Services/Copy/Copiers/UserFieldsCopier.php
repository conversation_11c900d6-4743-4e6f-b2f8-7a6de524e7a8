<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Copiers;

class UserFieldsCopier extends FieldsCopier implements <PERSON><PERSON><PERSON>, CopyMapper
{
    public function key(): string
    {
        return 'user_fields';
    }

    public function export(?int $seasonId = null, ?int $formId = null): void
    {
        $this->repository()
            ->userConfigurationExport(null)
            ->each(function ($field) {
                $field['seasonId'] = null;
                $this->setExported($field['id'], $field);
            });
    }

    public function dependencies(): array
    {
        return [
            RolesCopier::class,
        ];
    }

    public function beforeImport(?int $seasonId): void
    {
        if ($this->shouldReplace()) {
            $this->repository()->permanentlyDeleteUserFields(null);
        }
    }

    public function optionalDependencies(): array
    {
        return [
            RolesCopier::class,
        ];
    }
}
