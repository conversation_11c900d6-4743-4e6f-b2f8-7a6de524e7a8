<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Copiers;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use Platform\Language\Language;

class LanguageCopier extends BaseCopier implements <PERSON>pier, CopyMapper
{
    use CopyMapping;

    public function key(): string
    {
        return 'languages';
    }

    public function export(?int $seasonId = null, ?int $formId = null): void
    {
        current_account()
            ->languages
            ->each(fn($language) => $this->setExported($language->id, $language->toArray()));
    }

    public function import(
        array $data,
        ?int $seasonId = null,
        ?int $formId = null
    ): void {
        $language = app(AccountRepository::class)->addSupportedLanguage(
            current_account(),
            new Language($data['code']),
            $data['default']
        );

        $this->mapImportedId($data['id'], $language->id);
    }

    public function beforeImport(?int $seasonId): void
    {
        current_account()->languages()->delete();
    }

    public function hiddenDependencies(): array
    {
        return [
            DefaultLocaleCopier::class,
        ];
    }
}
