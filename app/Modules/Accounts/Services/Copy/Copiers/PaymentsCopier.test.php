<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Copiers;

use AwardForce\Modules\Accounts\Services\Copy\CopierService;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Settings\Contracts\SettingRepository;

class PaymentsCopierTest extends TestBaseCopier
{
    public function init()
    {
        $this->copier = app(PaymentsCopier::class);
        $this->setUpData();
    }

    public function testExport(): void
    {
        $this->copier->export();
        $exported = $this->copier->getExported();

        // some settings are based on configuration, so we can't guarantee the exact number
        $this->assertGreaterThanOrEqual(count($this->settings), count($exported));
        foreach ($this->settings as $key => $value) {
            if ($key === 'organisation-logo') {
                $this->assertEquals($this->file->id, $value['id']);

                continue;
            }
            if ($key === 'related-entries-field') {
                $this->assertequals($this->field->id, $value);

                continue;
            }
            $this->assertEquals($value, $exported[$key]);
        }
    }

    public function testImport(): void
    {
        $this->copier->export();
        $results = $this->importSetup();

        $this->copier->import(array_values($results));

        $imported = app(SettingRepository::class)->getAll()->pluck('value', 'key')->toArray();
        $this->assertTrue(app(CopierService::class)->hasQueuedFiles());
        // Imported settings minus organisation logo, which gets imported later after the file is copied
        $this->assertCount(count($this->settings) - 1, $imported);
        foreach ($this->settings as $key => $value) {
            if (in_array($key, ['organisation-logo', 'related-entries-field'])) {
                continue;
            }
            $this->assertEquals($value[$key], $imported[$key]);
        }
    }

    private function setUpData()
    {
        $this->settings = [];
        foreach ($this->copier->getSettings() as $setting) {
            app(SettingRepository::class)->saveSetting($setting, $value = fake()->word());
            $this->settings[$setting][$setting] = $value;
        }
        app(SettingRepository::class)->saveSetting('organisation-logo', ($this->file = $this->muffin(File::class, ['resource' => File::RESOURCE_SETTING]))->id);
        $this->settings['organisation-logo'] = $this->file;
        app(SettingRepository::class)->saveSetting('related-entries-field', ($this->field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]))->id);
        $this->settings['related-entries-field'] = $this->field->id;
    }
}
