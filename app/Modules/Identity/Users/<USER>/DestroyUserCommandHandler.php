<?php

namespace AwardForce\Modules\Identity\Users\Commands;

use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Identity\Users\Services\MembershipDestroyer;
use AwardForce\Modules\Identity\Users\Services\UserGateway;
use Platform\Events\EventDispatcher;

class DestroyUserCommandHandler
{
    use EventDispatcher;
    use UserDestruction;

    /** @var MembershipDestroyer */
    private $membershipDestroyer;

    /** @var MembershipRepository */
    private $memberships;

    /** @var UserGateway */
    private $gateway;

    public function __construct(
        MembershipDestroyer $membershipDestroyer,
        MembershipRepository $memberships,
        UserGateway $gateway
    ) {
        $this->membershipDestroyer = $membershipDestroyer;
        $this->memberships = $memberships;
        $this->gateway = $gateway;
    }

    public function handle(DestroyUserCommand $command)
    {
        // Remove global user membership
        $this->gateway->removeMembership($command->user->globalId, $command->account->globalId);

        // Remove local user membership
        $this->removeMembership($command->account->id, $command->user->id, $command->consigner->id, $command->option());

        $this->dispatch($this->membershipDestroyer->releaseEvents());
    }
}
