<?php

namespace AwardForce\Modules\Identity\Users\Validation;

use AwardForce\Modules\Identity\Users\Models\User;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class DoesNotHaveMultipleMemberships implements ValidationRule
{
    public function __construct(
        public User $user,
    ) {
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->user->hasMultipleMemberships()) {
            $fail('validation.email_password_multiple_accounts')->translate();
        }
    }
}
