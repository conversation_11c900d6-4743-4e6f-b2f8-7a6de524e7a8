<?php

namespace AwardForce\Modules\Identity\Users\Search\Enhancers;

use AwardForce\Modules\Comments\Models\Comment;
use AwardForce\Modules\Comments\Models\CommentCollection;
use AwardForce\Modules\Comments\Services\CommentManager;
use AwardForce\Modules\Comments\Tags\CommentableTag;
use AwardForce\Modules\Comments\Tags\OrTags;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Support\Collection;
use Platform\Search\Enhancers\SearchEnhancer;

class Comments implements SearchEnhancer
{
    public function __construct(private CommentManager $commentManager)
    {
    }

    /**
     * @param  Collection  $users
     */
    public function enhance($users): void
    {
        $comments = $this->retrieveComments($users);

        foreach ($users as $user) {
            $user->comments = $comments->get($user->id) ?: new CommentCollection;
        }
    }

    /**
     * Retrieve comments and group by order id.
     *
     * @return Collection
     */
    protected function retrieveComments($users)
    {
        $tags = $users->map(fn(User $user) => new CommentableTag($user));

        return $this->commentManager
            ->get(new OrTags(...$tags))
            ->groupBy(fn(Comment $comment) => $comment->getResourceId(class_basename(User::class)));
    }

    /**
     * Determines if this dependency applies to the search in any way.
     */
    public function applies(): bool
    {
        return false;
    }
}
