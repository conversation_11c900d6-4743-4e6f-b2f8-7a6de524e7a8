<?php

namespace AwardForce\Modules\Identity\Users\Commands;

use AwardForce\Modules\Identity\Users\Services\UserUpdater;

class UpdateUserViaKesselCommandHandler
{
    use UserUpdater;

    public function handle(UpdateUserViaKesselCommand $command)
    {
        if ($user = $command->globalUser()->localUser) {
            $this->updateUser($user, [
                'firstName' => $command->firstName(),
                'lastName' => $command->lastName(),
                'email' => $command->email(),
                'mobile' => $command->mobile(),
                'password' => $command->password(),
                'requireAuthenticator' => $command->requireAuthenticator(),
            ]);
        }

        return $user;
    }
}
