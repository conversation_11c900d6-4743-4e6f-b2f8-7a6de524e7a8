<?php

namespace AwardForce\Modules\Identity\Users\Search\Filters;

use AwardForce\Library\Database\Eloquent\JoinAwareness;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class AccountOwnerFilter implements ColumnatorFilter, SearchFilter
{
    use JoinAwareness;

    /**
     * @var array
     */
    private $owner;

    public function __construct(array $input)
    {
        $this->owner = $input['owner'] ?? null;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! $this->tableJoined($query->getQuery(), 'accounts')) {
            $query->leftJoin('accounts', 'accounts.user_id', '=', 'users.id');
        }

        return $query->whereNotNull('users.id');
    }

    public function applies(): bool
    {
        return ! empty($this->owner);
    }
}
