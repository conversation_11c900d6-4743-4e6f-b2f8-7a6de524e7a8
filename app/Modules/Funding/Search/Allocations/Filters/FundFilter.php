<?php

namespace AwardForce\Modules\Funding\Search\Allocations\Filters;

use AwardForce\Modules\Funding\Data\FundRepository;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Platform\Search\ApiVisibility;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\Services\SearchFilterValidator;
use Platform\Search\Services\SlugToIdMapper;

class FundFilter implements ColumnatorFilter, Htmlable, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;
    use SlugToIdMapper;

    /** @var array */
    private $input;

    /** @var FundRepository */
    private $funds;

    public function __construct(array $input, ?FundRepository $funds = null)
    {
        $this->input = $input;
        $this->funds = $funds ?: app(FundRepository::class);
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if ($fund = Arr::get($this->input, 'fund')) {
            return $query->where('fund_allocations.fund_id', $this->id($fund));
        }

        return $query;
    }

    /**
     * Get content as a string of HTML.
     *
     * @return string
     */
    public function toHtml()
    {
        $funds = $this->getFunds();

        return view('funding.allocation.search.filters.fund', compact('funds'))->render();
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return ! is_null(Arr::get($this->input, 'fund'));
    }

    /**
     * Get funds
     */
    private function getFunds()
    {
        return translate(app(FundRepository::class)->getAll());
    }

    public function repository()
    {
        return $this->funds;
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $this->validateValueIsSlug($filterName, $filterValue);
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['fund']);
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
