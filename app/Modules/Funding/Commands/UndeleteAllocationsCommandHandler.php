<?php

namespace AwardForce\Modules\Funding\Commands;

use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\Funding\Data\FundRepository;
use AwardForce\Modules\Funding\Exceptions\UnableToAllocateFundsWithinBudget;
use Platform\Database\Eloquent\Model;
use Platform\Events\EventDispatcher;

class UndeleteAllocationsCommandHandler
{
    use EventDispatcher;

    /**
     * @var AllocationRepository
     */
    private $allocations;

    /**
     * @var FundRepository
     */
    private $funds;

    public function __construct(AllocationRepository $allocations, FundRepository $funds)
    {
        $this->allocations = $allocations;
        $this->funds = $funds;
    }

    public function handle(UndeleteAllocationsCommand $command)
    {
        $events = $this->allocations->getTrashedByIds($command->allocationIds)
            ->reduce(function (array $events, Allocation $allocation) {
                $fund = $this->undelete($allocation);

                return $fund ? array_merge($events, $allocation->releaseEvents(), $fund->releaseEvents()) : $events;
            }, []);

        $this->dispatch($events);
    }

    /**
     * @return Model
     *
     * @throws \Throwable
     */
    public function undelete(Allocation $allocation)
    {
        try {
            return $this->funds->getWithLock($allocation->fundId, function (Fund $fund) use ($allocation) {
                $fund->restoreAllocation($allocation);
            });
        } catch (UnableToAllocateFundsWithinBudget $exception) {
            return null;
        }
    }
}
