<?php

namespace AwardForce\Modules\Funding\Data;

use AwardForce\Modules\Entries\Models\Entry;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class EloquentAllocationRepositoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItFiltersByEntry(): void
    {
        $entry = $this->muffin(Entry::class);
        $allocation = $this->muffin(Allocation::class, [
            'entry_id' => $entry->id,
        ]);
        $otherAllocation = $this->muffin(Allocation::class);

        $actual = app(AllocationRepository::class)
            ->fields(['fund_allocations.id'])
            ->entry($entry->id)
            ->get();

        $this->assertCount(1, $actual);
        $this->assertEquals($allocation->id, $actual->first()->id);

    }
}
