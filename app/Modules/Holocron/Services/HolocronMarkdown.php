<?php

namespace AwardForce\Modules\Holocron\Services;

use AwardForce\Library\Html\MarkdownCache;

class HolocronMarkdown
{
    public function markdown(?string $markdown)
    {
        // Markdown expects a new line character rather than <br>.
        $markdown = str_replace(['<br>', '\\n'], "\n", $markdown);

        // Remove old formatting if found
        $markdown = str_replace(['{:target="_blank"}', '{:target="_self"}'], '', $markdown);

        return app(MarkdownCache::class)->parse($markdown);
    }
}
