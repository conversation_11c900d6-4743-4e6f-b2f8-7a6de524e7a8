<?php

namespace AwardForce\Modules\Notifications\Commands;

use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Services\Notifier;
use AwardForce\Modules\Notifications\Services\Recipients\RecipientResolver;
use AwardForce\Modules\Notifications\Services\Recipients\Recipients;
use AwardForce\Modules\Notifications\Services\Recipients\Subscriptions;
use Illuminate\Support\Facades\Log;

trait NotificationHandler
{
    /**
     * Log the information/data for the pending notification to the logs.
     */
    protected function log($recipients, $command, $notification)
    {
        $recipientCount = count($recipients);
        $addresses = $recipients->asString();
        $data = json_encode($command->data);
        $accountId = current_account_id();

        Log::info(
            "Notifying [$recipientCount] recipient(s) [$addresses] with [$data] using notification [$notification->id] from account [$accountId]",
            compact('recipientCount', 'addresses', 'data', 'accountId')
        );
    }

    /**
     * Determines the recipients for the notification.
     *
     * @return Recipients|false
     */
    protected function recipients(Notification $notification, $command)
    {
        $recipients = $this->resolver()->getRecipients($notification, $command->additionalContext);

        if (is_null($recipients)) {
            $defaultRecipient = $command->defaultRecipient;
            $defaultRecipient->setNotification($notification);

            $recipients = new Recipients([$defaultRecipient]);
        }

        if ($recipients) {
            $recipients = $this->subscriptions()->verifyRecipients($notification, $recipients);
        }

        return $recipients;
    }

    private function resolver(): RecipientResolver
    {
        return app(RecipientResolver::class);
    }

    private function notifier(): Notifier
    {
        return app(Notifier::class);
    }

    private function subscriptions(): Subscriptions
    {
        return app(Subscriptions::class);
    }

    /**
     * Notify the recipients with the provided notification.
     */
    protected function notifyRecipients($command, $notification): void
    {
        $recipients = $this->recipients($notification, $command);

        // Some resolvers may return false, meaning that no email should be sent - not even to the default recipient
        if ($recipients === false) {
            return;
        }

        $this->log($recipients, $command, $notification);
        $this->notifier()->notify($recipients, $command->data, $notification);
    }
}
