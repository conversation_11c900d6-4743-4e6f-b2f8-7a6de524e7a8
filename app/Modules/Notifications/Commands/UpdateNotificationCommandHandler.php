<?php

namespace AwardForce\Modules\Notifications\Commands;

use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Tags\Services\TagManager;
use Platform\Events\EventDispatcher;
use Tectonic\LaravelLocalisation\Database\TranslationService;

class UpdateNotificationCommandHandler
{
    use EventDispatcher;

    public function __construct(
        private NotificationRepository $notifications,
        private TagManager $tags,
        private TranslationService $translations
    ) {
    }

    public function handle(UpdateNotificationCommand $command)
    {
        $notification = $command->notification;
        $notification->changeTrigger($command->trigger);
        $notification->active = $command->active;
        $notification->senderName = $command->senderName;
        $notification->senderAddress = $command->senderAddress;
        $notification->recipients = $command->recipients;
        $notification->formOption = $command->formOption;
        $notification->categoryOption = $command->categoryOption;
        $notification->roleOption = $command->roleOption;
        $notification->scoreSetOption = $command->scoreSetOption;
        $notification->legalBasis = $command->legalBasis;
        $notification->moderationOption = $command->moderationOption;
        $notification->paymentStatusOption = $command->paymentStatusOption;
        $notification->changeSendTimeSettings(
            $command->sendTimeUnit,
            $command->sendTimeOption,
            $command->sendTimeOffset
        );
        $notification->grantStatusOption = $command->grantStatusOption;
        $notification->grantReportStatusOption = $command->grantReportStatusOption;

        $notification->setRecipientOption(
            $command->recipientOption,
            $command->recipients,
            $command->field
        );

        $notification->settings = $command->settings;

        $this->notifications->save($notification);
        $this->translations->sync($notification, $command->translated);

        $notification->forms()->sync($command->forms);
        $notification->categories()->sync($command->categories);
        $notification->roles()->sync($command->roles);
        $notification->scoreSets()->sync($command->scoreSets);

        $this->tags->retag($notification, $command->tags);

        $this->dispatch($notification->releaseEvents());
    }
}
