<?php

namespace AwardForce\Modules\Notifications\Search\Columns;

use AwardForce\Library\Search\Columns\TranslatedColumnWithFallback;
use Illuminate\Support\Collection;
use Platform\Search\Defaults;

class Description extends TranslatedColumnWithFallback
{
    public function title(): string
    {
        return trans('notifications.form.description.label');
    }

    public function name(): string
    {
        return 'notifications.description';
    }

    public function fieldName(): string
    {
        return 'description';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function html($record)
    {
        return $this->value($record) ?? '—';
    }

    public function default(): Defaults
    {
        return new Defaults('search');
    }

    public function priority(): int
    {
        return 7;
    }

    public function sortable(): bool
    {
        return true;
    }
}
