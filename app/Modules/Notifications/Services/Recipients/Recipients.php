<?php

namespace AwardForce\Modules\Notifications\Services\Recipients;

use Illuminate\Support\Collection;

class Recipients extends Collection
{
    /**
     * Returns the recipient collection as a mail-friendly string.
     *
     * @return string
     */
    public function asString()
    {
        return implode(',', $this->items);
    }

    /**
     * Typecasts the collection to a string.
     *
     * @return string
     */
    public function __toString()
    {
        return $this->asString();
    }
}
