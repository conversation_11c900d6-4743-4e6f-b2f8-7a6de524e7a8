<?php

namespace AwardForce\Modules\Notifications\Services\Recipients;

use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Services\Recipients\Resolvers\Resolver;

class RecipientResolver
{
    /**
     * Collection of resolvers that may be able to return the appropriate recipient.
     *
     * @var Resolver[]
     */
    private $resolvers = [];

    /**
     * Retrieves the appropriate recipient for the notification. Sometimes this may actually be null,
     * and should be handled accordingly for each individual case.
     *
     * @return Recipients|null
     *
     * @throws NoResolverFoundException
     */
    public function getRecipients(Notification $notification, array $context = [])
    {
        foreach ($this->resolvers as $resolver) {
            if ($resolver->canResolve($notification)) {
                return $resolver->recipients($notification, $context);
            }
        }

        throw new NoResolverFoundException;
    }

    /**
     * Registers a new resolver.
     */
    public function registerResolver(Resolver $resolver)
    {
        $this->resolvers[] = $resolver;
    }
}
