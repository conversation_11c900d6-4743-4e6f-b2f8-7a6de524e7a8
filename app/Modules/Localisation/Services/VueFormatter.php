<?php

namespace AwardForce\Modules\Localisation\Services;

class VueFormatter
{
    public static function formatTranslated(array $translated = []): array
    {
        return collect($translated)->map(function (array $translations) {
            return collect($translations)
                ->map(function ($value) {
                    if ($value === 'null') {
                        return null;
                    }

                    return $value;
                })
                ->toArray();
        })->toArray();
    }
}
