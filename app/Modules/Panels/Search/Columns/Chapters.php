<?php

namespace AwardForce\Modules\Panels\Search\Columns;

class Chapters extends CountColumn
{
    /**
     * @var bool
     */
    private $multiChapter;

    /**
     * Chapters constructor.
     */
    public function __construct(bool $multiChapter)
    {
        parent::__construct('chapter');

        $this->multiChapter = $multiChapter;
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return $this->multiChapter;
    }
}
