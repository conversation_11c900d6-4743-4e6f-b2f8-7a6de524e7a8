<?php

namespace AwardForce\Modules\Panels\Search\Filters;

use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Services\SlugToIdMapper;

class RoundFilter implements ColumnatorFilter, Htmlable, SearchFilter
{
    use SlugToIdMapper;

    private $input;
    private $roundRepository;

    public function __construct($input)
    {
        $this->input = $input;

        $this->roundRepository = app(RoundRepository::class);
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        $roundRequest = Arr::get($this->input, 'round');

        $query->join('panel_round', 'panel_round.panel_id', 'panels.id')
            ->join('rounds', function ($join) use ($roundRequest) {
                $join->on('rounds.id', '=', 'panel_round.round_id');
                $join->where('rounds.id', '=', $this->id($roundRequest));
            });

        return $query;
    }

    public function toHtml()
    {
        return view('panel.search.filters.round-filter', ['filterableRounds' => $this->filterableRounds()])->render();
    }

    public function filterableRounds(): array
    {
        $rounds = translate($this->roundRepository->getForSeason(current_account()->activeSeason()->id));

        return for_select_sorted($rounds, ['slug', 'name'], true);
    }

    /**
     * Determines if this dependency applies to the search in any way.
     */
    public function applies(): bool
    {
        return ! empty(Arr::get($this->input, 'round'));
    }

    /**
     * @return mixed
     */
    protected function repository()
    {
        return $this->roundRepository;
    }
}
