<?php

namespace AwardForce\Modules\Seasons\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Exceptions\CannotDeactivateSeasonException;
use AwardForce\Modules\Seasons\Exceptions\NoActiveSeasonException;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class EloquentSeasonRepository extends Repository implements SeasonRepository
{
    use HasRequestCache;

    /**
     * @var Season
     */
    protected $activeSeason;

    /**
     * Make sure we assign the required model.
     */
    public function __construct(Season $model)
    {
        $this->model = $model;
    }

    /**
     * Returns the Active Season for the current Account.
     *
     * @return Season
     */
    public function getActive()
    {
        $this->activeSeason = $this->activeSeason ?: $this->getOneBy('status', Season::STATUS_ACTIVE);

        return $this->activeSeason;
    }

    /**
     * Returns the Active Season for the current Account.
     *
     * @return int
     */
    public function getActiveId()
    {
        $this->activeSeason = $this->activeSeason ?: $this->getOneBy('status', Season::STATUS_ACTIVE);

        return $this->activeSeason->id;
    }

    /**
     * Returns the Season given the chapter.
     *
     * @return Season
     */
    public function getFromChapter(Chapter $chapter)
    {
        return $chapter->season;
    }

    /**
     * Returns the Seasons given the chapters.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getFromChapters(Collection $chapters)
    {
        return $this->getByIds($chapters->pluck('seasonId')->unique()->toArray());
    }

    /**
     * Returns the non-archived seasons IDs (Active and Draft).
     *
     * @return array
     */
    public function getNonArchivedIds()
    {
        return $this->getQuery()->whereIn('status', [Season::STATUS_ACTIVE, Season::STATUS_DRAFT])->pluck('id')->toArray();
    }

    /**
     * Marks the specified Season as Active, and archives the existing active Season.
     *
     * @return Season
     */
    public function markAsActive(Season $season)
    {
        $markAsActive = function () use ($season) {
            // Reset internal cache.
            $this->activeSeason = null;

            // Only one season, no need to do anything fancy.
            if ($this->hasOnlyOneSeason()) {
                return $season->markAsActive()->save();
            }

            // Load Active Season
            $activeSeason = $this->getActive();

            // If no existing active, mark $season as active and return
            if (! $activeSeason) {
                return $season->markAsActive()->save();
            }

            // Check if the currently active season is the specified one
            if ($activeSeason->id == $season->id) {
                return $season;
            }

            // Swap active season.
            $this->activeSeason = $season->swapActiveFrom($activeSeason);

            return $this->activeSeason;
        };

        $markAsActive->bindTo($this);

        DB::transaction($markAsActive);
    }

    /**
     * Marks the specified Season as Archived.
     *
     * @param  bool  $guardAgainstActive
     * @return Season
     *
     * @throws NoActiveSeasonException
     */
    public function markAsArchived(Season $season, $guardAgainstActive = true)
    {
        $markAsArchived = function () use ($season, $guardAgainstActive) {
            // Check if active
            if ($guardAgainstActive) {
                $this->guardAgainstActiveSeason($season, 'Archived');
            }

            // Reset internal cache.
            $this->activeSeason = null;

            // Set as Archived, and save.
            return $season->markAsArchived()->save();
        };

        $markAsArchived->bindTo($this);

        DB::transaction($markAsArchived);
    }

    /**
     * Marks the specified Season as Draft.
     *
     * @param  bool  $guardAgainstActive
     * @return Season
     *
     * @throws NoActiveSeasonException
     */
    public function markAsDraft(Season $season, $guardAgainstActive = true)
    {
        $markAsDraft = function () use ($season, $guardAgainstActive) {
            // Check if active
            if ($guardAgainstActive) {
                $this->guardAgainstActiveSeason($season, 'Draft');
            }

            // Reset internal cache.
            $this->activeSeason = null;

            // Set as Archived, and save.
            return $season->markAsDraft()->save();
        };

        $markAsDraft->bindTo($this);

        DB::transaction($markAsDraft);
    }

    /**
     * Returns true if there is only one season.
     *
     * @return bool
     */
    public function hasOnlyOneSeason()
    {
        return $this->countAll() == 1;
    }

    /**
     * Throws exception if the specified season is the Active season.
     *
     * @param  string  $status
     *
     * @throws CannotDeactivateSeasonException
     */
    protected function guardAgainstActiveSeason(Season $season, $status)
    {
        if ($this->getActive()->id == $season->id) {
            throw new CannotDeactivateSeasonException('Cannot mark Active Season as '.$status.'.');
        }
    }

    /**
     * Returns a collection of non-archived seasons.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getNonArchived()
    {
        return $this->getQuery()->whereIn('status', [Season::STATUS_ACTIVE, Season::STATUS_DRAFT])->get();
    }

    /**
     * Returns a collection of non-draft seasons.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getNonDraft()
    {
        return $this->getQuery()->whereIn('status', [Season::STATUS_ACTIVE, Season::STATUS_ARCHIVED])->get();
    }

    /**
     * Returns a collection of draft seasons.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getDrafts()
    {
        return $this->getByStatus(Season::STATUS_DRAFT);
    }

    /**
     * Returns a collection of archived seasons.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getArchived()
    {
        return $this->getQuery()
            ->whereStatus(Season::STATUS_ARCHIVED)
            ->get();
    }

    /**
     * Returns a collection of all seasons excluding the specified season.
     *
     * @param  int  $seasonId
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllExcept($seasonId)
    {
        return $this->getQuery()
            ->where('id', '!=', $seasonId)
            ->get();
    }

    /**
     * Returns true if any of the seasons, specified by id, were created before the given timestamp.
     *
     * @param  array|int[]  $ids
     * @param  string|\DateTime  $date
     */
    public function wereCreatedBefore(array $ids, $date): bool
    {
        return $this->getQuery()
            ->whereIn('id', $ids)
            ->where('created_at', '<', $date)
            ->exists();
    }

    public function getMaxJob(): bool
    {
        return (bool) $this->getQuery()
            ->max('seasons.job_status');
    }

    public function configurationExport(?int $seasonId = null, ?int $formId = null)
    {
        return $this->getQuery()
            ->forConfiguration()
            ->whereId($seasonId)
            ->first();
    }
}
