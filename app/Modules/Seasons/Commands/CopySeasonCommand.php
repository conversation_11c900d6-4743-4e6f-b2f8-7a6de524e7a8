<?php

namespace AwardForce\Modules\Seasons\Commands;

use AwardForce\Library\Bus\ConsumerJob;
use AwardForce\Library\Bus\QueuedJob;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Seasons\Models\Season;
use Platform\Authorisation\Consumer;
use Platform\Bus\JobStatus;

class CopySeasonCommand extends QueuedJob
{
    use ConsumerJob;
    use JobStatus;

    /**
     * @var Account
     */
    public $account;

    /**
     * @var Season
     */
    public $season;

    /**
     * @var int
     */
    public $existing;

    /**
     * @var Consumer
     */
    public $consumer;

    /**
     * CopySeasonCommand constructor.
     */
    public function __construct(Season $season, $existing)
    {
        $this->season = $season;
        $this->existing = $existing;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function jobStatusResource()
    {
        return $this->season;
    }

    /**
     * Unique debounce key, used to detect duplicate commands.
     */
    protected function debounceKey(): string
    {
        return $this->season->id.'.'.$this->existing;
    }
}
