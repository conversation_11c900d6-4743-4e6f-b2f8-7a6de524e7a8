<?php

namespace AwardForce\Modules\Api\V2\Commands;

use AwardForce\Modules\Accounts\Models\Account;

class GenerateApiKey
{
    public function __construct(
        private Account $account,
        private string $name,
        private string $scope,
        private ?string $notificationEmails = null,
        private ?string $applicability = null
    ) {
    }

    public function account(): Account
    {
        return $this->account;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function scope(): string
    {
        return $this->scope;
    }

    public function notificationEmails(): ?string
    {
        return $this->notificationEmails;
    }

    public function applicability(): ?string
    {
        return $this->applicability;
    }
}
