<?php

namespace AwardForce\Modules\Api\V2\Search\Columnators\V2_2;

use AwardForce\Modules\Chapters\Search\ChapterColumnator as BaseColumnator;
use Platform\Search\Columns;
use Platform\Search\Dependencies;

class ChapterColumnator extends BaseColumnator
{
    /**
     * @return mixed
     */
    protected function customColumns(): Columns
    {
        return new Columns;
    }

    protected function customDependencies(): Dependencies
    {
        return new Dependencies;
    }
}
