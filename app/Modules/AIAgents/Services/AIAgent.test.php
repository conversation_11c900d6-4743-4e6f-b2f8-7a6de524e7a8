<?php

namespace AwardForce\Modules\AIAgents\Services;

use AwardForce\Library\AIAgents\Contracts\TextGenerator;
use AwardForce\Library\AIAgents\Enums\Model as ModelEnum;
use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use AwardForce\Library\AIAgents\ValueObjects\Response;
use AwardForce\Library\AIAgents\ValueObjects\TokenUsage;
use AwardForce\Modules\AIAgents\Boundary\Composite;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\AIAgents\Events\AITokensConsumed;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use Illuminate\Support\Facades\Event;
use Mockery\MockInterface;
use Platform\Database\Eloquent\Model;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class AIAgentTest extends BaseTestCase
{
    use Laravel;

    private ContextResolver|MockInterface $contextResolver;
    private TextGenerator|MockInterface $textGenerator;
    private AIAgent $aiAgent;

    protected function init(): void
    {
        $this->contextResolver = $this->mock(ContextResolver::class);
        $this->textGenerator = $this->mock(TextGenerator::class);
        $this->aiAgent = new AIAgent($this->contextResolver, $this->textGenerator);
    }

    public function testPromptContextDelegatesToContextResolver(): void
    {
        $resource = new Resource(ResourceType::Entry, 123);
        $requiredContexts = ['foo', 'bar'];
        $expected = $this->mock(PromptContext::class);

        $this->contextResolver
            ->shouldReceive('generateFor')
            ->once()
            ->with($resource, $requiredContexts)
            ->andReturn($expected);

        $result = $this->aiAgent->promptContext($resource, $requiredContexts);

        $this->assertSame($expected, $result);
    }

    public function testGenerateTextDelegatesToInternalAIAgent(): void
    {
        $resource = new Resource(ResourceType::Entry, random_int(10000, 10000000));
        $prompt = new Prompt(ModelEnum::Claude35Sonnet, 'tell me a story');
        $metaData = ['foo' => 'bar'];
        $expected = 'generated text';

        Event::fake();
        $this->textGenerator->shouldReceive('prompt')
            ->once()
            ->with($prompt)
            ->andReturn(new Response($expected, new TokenUsage(10, 20)));

        $result = $this->aiAgent->generateText($resource, $prompt, $metaData);

        Event::assertDispatched(AITokensConsumed::class);
        $this->assertSame($expected, $result);
    }

    public function testModelDelegatesToContextResolverComposite(): void
    {
        $resource = new Resource(ResourceType::Entry, $id = random_int(10000, 10000000));
        $composite = $this->mock(Composite::class);
        $model = $this->mock(Model::class);

        $this->contextResolver
            ->shouldReceive('composite')
            ->once()
            ->with($resource->type)
            ->andReturn($composite);

        $composite
            ->shouldReceive('model')
            ->once()
            ->with($id)
            ->andReturn($model);

        $result = $this->aiAgent->model($resource);

        $this->assertSame($model, $result);
    }

    public function testTriggersDelegatesToContextResolverComposite(): void
    {
        $resourceType = ResourceType::Entry;
        $composite = $this->mock(Composite::class);
        $fieldTrigger = AIFieldTrigger::class;

        $this->contextResolver
            ->shouldReceive('composite')
            ->once()
            ->with($resourceType)
            ->andReturn($composite);

        $composite
            ->shouldReceive('triggers')
            ->once()
            ->andReturn([$fieldTrigger]);

        $result = $this->aiAgent->triggers($resourceType);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertSame($fieldTrigger, $result[0]);
    }

    public function testContextsDelegatesToContextResolverComposite(): void
    {
        $resourceType = ResourceType::Entry;
        $composite = $this->mock(Composite::class);
        $fieldContexts = AIFieldContext::class;

        $this->contextResolver
            ->shouldReceive('composite')
            ->once()
            ->with($resourceType)
            ->andReturn($composite);

        $composite
            ->shouldReceive('contexts')
            ->once()
            ->andReturn([$fieldContexts]);

        $result = $this->aiAgent->contexts($resourceType);

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertSame($fieldContexts, $result[0]);
    }
}
