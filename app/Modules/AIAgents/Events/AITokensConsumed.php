<?php

namespace AwardForce\Modules\AIAgents\Events;

use AwardForce\Library\AIAgents\ValueObjects\TokenUsage;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\Billing\Contracts\UsageTrackable;
use AwardForce\Modules\Billing\Data\Usage;
use AwardForce\Modules\Billing\Services\LoggableFactory;

readonly class AITokensConsumed implements UsageTrackable
{
    public function __construct(
        public Resource $resource,
        public TokenUsage $tokenUsage,
        public array $metaData,
    ) {
    }

    public function usage(LoggableFactory $loggableFactory): Usage
    {
        return Usage::withDefaults(
            event: 'ai_tokens_consumed',
            metrics: $this->tokenUsage->toArray(),
            loggable: $loggableFactory->createFromAIResource($this->resource),
            metadata: $this->metaData,
        );
    }
}
