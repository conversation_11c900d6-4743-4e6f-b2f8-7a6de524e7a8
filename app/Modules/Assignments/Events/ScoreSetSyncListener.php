<?php

namespace AwardForce\Modules\Assignments\Events;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Assignments\Commands\SyncScoreSetCommand;
use AwardForce\Modules\Categories\Events\CategoryWasDeleted;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Events\ChapterWasDeleted;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Events\EntryDivisionWasChanged;
use AwardForce\Modules\Entries\Events\EntryWasArchived;
use AwardForce\Modules\Entries\Events\EntryWasConfirmedAsDuplicated;
use AwardForce\Modules\Entries\Events\EntryWasDeleted;
use AwardForce\Modules\Entries\Events\EntryWasModerated;
use AwardForce\Modules\Entries\Events\EntryWasRestored;
use AwardForce\Modules\Entries\Events\EntryWasRevertedToInProgress;
use AwardForce\Modules\Entries\Events\EntryWasSubmitted;
use AwardForce\Modules\Entries\Events\EntryWasUnarchived;
use AwardForce\Modules\Grants\Events\EntryLockScoringWasChanged;
use AwardForce\Modules\Grants\Events\GrantStatusLockScoringUpdated;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Events\RolesWereRevoked;
use AwardForce\Modules\Judging\Events\JudgeWasRecusedFromEntry;
use AwardForce\Modules\Judging\Events\JudgeWasRecusedFromEntryScoreSet;
use AwardForce\Modules\Judging\Events\JudgeWasUnrecusedFromEntry;
use AwardForce\Modules\Judging\Events\JudgeWasUnrecusedFromEntryScoreSet;
use AwardForce\Modules\Panels\Events\PanelWasCreated;
use AwardForce\Modules\Panels\Events\PanelWasDeleted;
use AwardForce\Modules\Panels\Events\PanelWasUndeleted;
use AwardForce\Modules\Panels\Events\PanelWasUpdated;
use AwardForce\Modules\Panels\Events\ScoreSetWasChanged;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\Tags\Events\TagWasSelectedForDeletion;
use AwardForce\Modules\Tags\Models\Tag;
use Platform\Authorisation\FeatureRoles\Judge;

class ScoreSetSyncListener
{
    use SyncsAssignments;

    protected function events(): array
    {
        return [
            CategoryWasDeleted::class => 'whenCategoryWasDeleted',
            ChapterWasDeleted::class => 'whenChapterWasDeleted',
            EntryDivisionWasChanged::class => 'whenEntryEvent',
            EntryWasArchived::class => 'whenEntryEvent',
            EntryWasConfirmedAsDuplicated::class => 'whenEntryEvent',
            EntryWasDeleted::class => 'whenEntryEvent',
            EntryWasModerated::class => 'whenEntryEvent',
            EntryWasRestored::class => 'whenEntryEvent',
            EntryWasSubmitted::class => 'whenEntryEvent',
            EntryWasRevertedToInProgress::class => 'whenEntryEvent',
            EntryWasUnarchived::class => 'whenEntryEvent',
            EntryLockScoringWasChanged::class => 'whenEntryEvent',
            JudgeWasRecusedFromEntry::class => 'whenJudgeEntryEvent',
            JudgeWasRecusedFromEntryScoreSet::class => 'whenJudgeEntryScoreSetEvent',
            JudgeWasUnrecusedFromEntry::class => 'whenJudgeEntryEvent',
            JudgeWasUnrecusedFromEntryScoreSet::class => 'whenJudgeEntryScoreSetEvent',
            PanelWasCreated::class => 'whenPanelEvent',
            PanelWasUpdated::class => 'whenPanelEvent',
            PanelWasDeleted::class => 'whenPanelEvent',
            PanelWasUndeleted::class => 'whenPanelEvent',
            RolesWereRevoked::class => 'whenRolesWereRevoked',
            ScoreSetWasChanged::class => 'whenScoreSetWasChanged',
            TagWasSelectedForDeletion::class => 'whenTagWasSelectedForDeletion',
            GrantStatusLockScoringUpdated::class => 'whenGrantStatusLockScoringChanged',
        ];
    }

    public function whenChapterWasDeleted(ChapterWasDeleted $event)
    {
        $this->detachAndSync($event->chapter);
    }

    public function whenCategoryWasDeleted(CategoryWasDeleted $event)
    {
        $this->detachAndSync($event->category);
    }

    /**
     * @param  EntryDivisionWasChanged|EntryWasDeleted|EntryWasModerated|EntryWasRestored|EntryWasArchived|EntryWasUnarchived|EntryLockScoringWasChanged  $event
     */
    public function whenEntryEvent($event)
    {
        $this->syncEntry($event->entry ?? $event->model);
    }

    /**
     * @param  JudgeWasRecusedFromEntry|JudgeWasUnrecusedFromEntry  $event
     */
    public function whenJudgeEntryEvent($event)
    {
        $this->scoreSets($event->entry->seasonId)
            ->each(function (ScoreSet $scoreSet) use ($event) {
                $this->dispatchSync($scoreSet->id, [$event->entry->id], [$event->userId]);
            });
    }

    /**
     * @param  GrantStatusLockScoringUpdated  $event
     */
    public function whenGrantStatusLockScoringChanged($event): void
    {
        foreach ($event->grantStatus->entries as $entry) {
            $this->syncEntry($entry);
        }
    }

    /**
     * @param  JudgeWasRecusedFromEntryScoreSet|JudgeWasUnrecusedFromEntryScoreSet  $event
     */
    public function whenJudgeEntryScoreSetEvent($event)
    {
        $this->dispatchSync($event->scoreSetId, [$event->entry->id], [$event->userId]);
    }

    /**
     * @param  PanelWasDeleted|PanelWasUndeleted  $event
     */
    public function whenPanelEvent($event)
    {
        $this->dispatch(SyncScoreSetCommand::syncPanel($event->panel));
    }

    public function whenScoreSetWasChanged(ScoreSetWasChanged $event)
    {
        $this->dispatch(new SyncScoreSetCommand($event->scoreSetId));
    }

    public function whenRolesWereRevoked(RolesWereRevoked $event)
    {
        $users = $event->users();
        $panels = $event->role()->panels->pluck('id');

        $judges = $users->filter(function ($user) {
            return Judge::appliesTo(new UserConsumer($user));
        });

        $judges->each(function ($user) use ($panels) {
            $panels->merge($user->panels->pluck('id'));
            $user->panels()->detach();
        });

        app(ScoreSetRepository::class)->getUnprotectedForPanels($panels->toArray())
            ->each(function (int $scoreSet) use ($judges) {
                $this->dispatchSync($scoreSet, [], $judges->pluck('id')->toArray());
            });
    }

    public function whenTagWasSelectedForDeletion(TagWasSelectedForDeletion $event)
    {
        $this->detachAndSync($event->tag);
    }

    /**
     * @param  Category|Chapter|Role|Tag  $model
     */
    private function detachAndSync($model)
    {
        $panels = $model->panels()->get();
        $model->panels()->detach();

        app(ScoreSetRepository::class)->getUnprotectedForPanels($panels->pluck('id')->toArray())
            ->each(function (int $scoreSet) {
                $this->dispatchSync($scoreSet);
            });
    }
}
