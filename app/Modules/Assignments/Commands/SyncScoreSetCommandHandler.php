<?php

namespace AwardForce\Modules\Assignments\Commands;

use AwardForce\Modules\Assignments\Services\SyncFilter;
use AwardForce\Modules\Assignments\Services\Synchroniser;
use AwardForce\Modules\Judging\Events\RecalculationRequested;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Illuminate\Support\Facades\Cache;
use Platform\Events\EventDispatcher;
use SebastianBergmann\Timer\Timer;

class SyncScoreSetCommandHandler
{
    use EventDispatcher;

    /**
     * @var Synchroniser
     */
    private $synchroniser;

    /**
     * @var ScoreSetRepository
     */
    private $scoreSets;

    public function __construct(Synchroniser $synchroniser, ScoreSetRepository $scoreSets)
    {
        $this->synchroniser = $synchroniser;
        $this->scoreSets = $scoreSets;
    }

    public function handle(SyncScoreSetCommand $command)
    {
        $timer = new Timer();
        $timer->start();

        $scoreSet = $this->scoreSets->getUnprotectedById($command->scoreSet);

        if (! $scoreSet) {
            return; // score set deleted, no need to sync
        }

        $this->sync($scoreSet, $command->entries, $command->judges);

        if ($command->recalculate) {
            $this->dispatch(new RecalculationRequested($scoreSet->id));
        }

        $scoreSet->flushCacheTag();

        Cache::forever('scoreset.sync.duration:'.$scoreSet->id, $timer->stop()->asMicroseconds());
    }

    private function sync(ScoreSet $scoreSet, array $entries, array $judges)
    {
        if (! $entries && ! $judges) {
            $this->synchroniser->sync(new SyncFilter($scoreSet));

            return;
        }

        if ($entries) {
            $this->synchroniser->sync(new SyncFilter($scoreSet, $entries));
        }

        if ($judges) {
            $this->synchroniser->sync(new SyncFilter($scoreSet, [], $judges));
        }
    }
}
