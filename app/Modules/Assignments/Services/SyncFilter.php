<?php

namespace AwardForce\Modules\Assignments\Services;

use AwardForce\Modules\Assignments\Exceptions\CannotSyncWithComplexFilter;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;

class SyncFilter
{
    /**
     * @var ScoreSet
     */
    private $scoreSet;

    /**
     * @var array
     */
    private $entries;

    /**
     * @var array
     */
    private $judges;

    public function __construct(ScoreSet $scoreSet, array $entries = [], array $judges = [])
    {
        $this->scoreSet = $scoreSet;
        $this->entries = $entries;
        $this->judges = $judges;

        if ($this->entries && $this->judges) {
            throw new CannotSyncWithComplexFilter('Score Set Sync attempted with both entry and judge filters, only one can be provided.');
        }
    }

    public function scoreSet(): ScoreSet
    {
        return $this->scoreSet;
    }

    public function scoreSetId(): int
    {
        return $this->scoreSet->id;
    }

    /**
     * @return array|int[]
     */
    public function entries(): array
    {
        return $this->entries;
    }

    /**
     * @return array|int[]
     */
    public function judges(): array
    {
        return $this->judges;
    }
}
