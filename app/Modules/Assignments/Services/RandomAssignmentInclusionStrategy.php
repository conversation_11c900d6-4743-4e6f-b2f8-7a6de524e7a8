<?php

namespace AwardForce\Modules\Assignments\Services;

abstract class RandomAssignmentInclusionStrategy
{
    const IN_ADDITION_TO_EXISTING = 'in_addition';

    const MINUS_EXISTING = 'minus_existing';

    protected int $howMany;

    public function __construct(int $howMany)
    {
        $this->howMany = $howMany;
    }

    abstract public function calculateNumberOfAssignments(int $numOfExistingAssignments, int $numOfRelatedModelsSelected): int;

    public function howMany(): int
    {
        return $this->howMany;
    }

    public function canCreateAssignmentBetween(int $entry, int $judge, RandomAssigner $randomAssigner): bool
    {
        if ($this->isTherePendingAssignmentBetween($entry, $judge, $randomAssigner)) {
            return false;
        }

        if ($this->isThereExistingAssignmentBetween($entry, $judge, $randomAssigner)) {
            return false;
        }

        return true;
    }

    protected function isTherePendingAssignmentBetween(int $entryId, int $judgeId, RandomAssigner $randomAssigner)
    {
        $assignmentsGroupedByEntries = $randomAssigner->pendingAssignments()->groupBy(['entry_id', 'judge_id']);

        if ($assignmentsGroupedByEntries->get($entryId)) {
            return $assignmentsGroupedByEntries->get($entryId)->has($judgeId);
        }

        return false;
    }

    protected function isThereExistingAssignmentBetween(int $entryId, int $judgeId, RandomAssigner $randomAssigner): bool
    {
        return $randomAssigner->judgesAssignedTo($entryId)->contains($judgeId);
    }
}
