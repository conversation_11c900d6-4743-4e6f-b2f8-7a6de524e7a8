<?php

namespace AwardForce\Modules\Assignments\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Search\Services\SavedViews;
use Illuminate\Http\Request;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class SearchAssignments extends View
{
    use SavedViews;

    /**
     * @var Request
     */
    private $request;

    /**
     * @var ColumnatorFactory
     */
    private $columnatorFactory;

    /**
     * @var Engine
     */
    private $translator;

    public function __construct(
        Request $request,
        ColumnatorFactory $columnatorFactory,
        Engine $translator
    ) {
        $this->request = $request;
        $this->columnatorFactory = $columnatorFactory;
        $this->translator = $translator;
    }

    /**
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function assignments()
    {
        $search = new ColumnatorSearch($this->columnator);
        $results = $this->translator->translate($search->search());

        return $results;
    }

    /**
     * @return string
     */
    public function area()
    {
        return 'assignments.search';
    }

    /**
     * @return \Platform\Search\Columnator
     */
    public function columnator()
    {
        return $this->columnatorFactory->forArea('assignments.search', $this->request->all());
    }

    /**
     * @return bool
     */
    public function canExport()
    {
        return Consumer::can('view', 'ScoresAll');
    }

    public function assignmentIds(): array
    {
        return $this->assignments->pluck('id')->toArray();
    }

    public function nonAutoAssignmentIds(): array
    {
        return $this->assignments->whereIn('method', [Assignment::METHOD_MANUAL, Assignment::METHOD_RANDOM])->pluck('id')->toArray();
    }
}
