<?php

namespace AwardForce\Modules\Assignments\Search\Columns;

use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;

class JudgeSlug implements Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return trans('assignments.table.columns.judge_slug');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'assignments.judge_slug';
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
        ]);
    }

    /**
     * Returns the name of the field in the query that should be present. This is useful for specifying the exact field. In some cases,
     * columns do not reference a specific table field, and may need some additional filtering or work, so a return value is not always
     * necessary. If nothing is returned, it will be ignored for the query. If null is returned, it is expected the the value method
     * will still return an appropriate value.
     *
     * @return string|null
     */
    public function field()
    {
        return 'assignments.judge_id';
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        if ($record->isJudgeBased()) {
            return (string) $record->judge?->slug ?? '';
        }
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return $this->value($record);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('export');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return 100;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }
}
