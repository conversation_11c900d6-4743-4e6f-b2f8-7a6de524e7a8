<?php

namespace AwardForce\Modules\Dashboard\Widgets;

use AwardForce\Modules\Dashboard\View\Widgets\Orders;

class OrdersWidget extends Widget
{
    /**
     * @var Orders
     */
    private $view;

    public function __construct(Orders $view)
    {
        $this->view = $view;
    }

    /**
     * Return the name of the template to be used for the widget.
     *
     * @return string
     */
    protected function template()
    {
        return 'dashboard.widgets.orders';
    }

    /**
     * Return the data to be used for the widget. The resulting object should be a
     * View object (See: Platform\View\View).
     *
     * @return mixed
     */
    protected function data()
    {
        return $this->view;
    }
}
