<?php

namespace AwardForce\Modules\Dashboard\Reports\CategoryEntries\Search\Dependencies;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Contracts\Support\Htmlable;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class SeasonFilter implements ColumnatorFilter, Htmlable, SearchFilter
{
    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    public function applyToEloquent($query)
    {
        if ($seasonId = array_get($this->input, 'season')) {
            $query->where('categories.season_id', $seasonId);
        }

        return $query;
    }

    public function toHtml()
    {
        $seasons = translate(app(SeasonRepository::class)->getAll());
        $active = CurrentAccount::activeSeasonId();

        $options = for_select($seasons, ['id', 'name'], true);
        $options[$active] = trans('seasons.selector.active', ['name' => $options[$active]]);

        return view('dashboard.reports.category-entries.filters.season', ['seasons' => $options])->render();
    }

    public function applies(): bool
    {
        return array_has($this->input, 'season');
    }
}
