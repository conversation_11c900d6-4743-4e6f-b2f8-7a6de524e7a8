<?php

namespace AwardForce\Modules\Integrations\Services\Copier;

use AwardForce\Library\Copier\Map;
use AwardForce\Library\Copier\Processor;
use AwardForce\Library\Copier\SlugMap;

class CRMSettingsUpdater implements SettingsUpdater
{
    /**
     * Updates all settings that may have references to copied resources, such as fields and tabs.
     */
    public function updateSettings(array $settings, Map $idMap, SlugMap $slugMap): array
    {
        foreach ($settings as $resource => $resourceSettings) {
            $resourceSettings['unique-field'] = $slugMap->get(array_get($resourceSettings, 'unique-field', ''));
            $resourceSettings['fields'] = $this->updateFields(array_get($resourceSettings, 'fields', []), $slugMap);
            $resourceSettings['fixed-values'] = $this->copyFixedValues(array_get($resourceSettings, 'fixed-values', []));
            $resourceSettings['tab'] = $idMap->get(Processor::TABS, array_get($resourceSettings, 'tab'));

            $settings[$resource] = array_filter($resourceSettings);
        }

        return $settings;
    }

    /**
     * @return mixed
     */
    private function updateFields(array $fields, SlugMap $slugMap)
    {
        $keys = array_map(function ($slug) use ($slugMap) {
            return $slugMap->get($slug);
        }, array_keys($fields));

        return array_combine($keys, array_values($fields));
    }

    /**
     * @return array
     */
    private function copyFixedValues(array $fixedValues)
    {
        if (count($fixedValues)) {
            return [
                'fields' => array_keys($fixedValues),
                'values' => array_values($fixedValues),
            ];
        }

        return [];
    }
}
