<?php

namespace AwardForce\Modules\Integrations\Actions;

use Exception;
use Log;

class ActionSequenceCommandHandler
{
    /** @var array */
    private $results = [];

    /**
     * @return mixed
     */
    public function handle(ActionSequenceCommand $command)
    {
        foreach ($command->sequence->steps() as $step) {
            try {
                $action = $step->createAction();

                if ($result = $action->execute($this->results)) {
                    $this->results[$step->integrationId()] = $result;
                }
            } catch (Exception $ex) {
                Log::error($ex->getMessage());
            }
        }

        if ($aggregate = $command->resultAggregator) {
            return $aggregate($this->results);
        }
    }
}
