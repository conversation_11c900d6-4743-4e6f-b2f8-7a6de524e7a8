<?php

namespace AwardForce\Modules\Files\Services;

use Assert\Assertion;

class TranscodingStatusMapper
{
    private static $types = [
        'waiting' => 'info',
        'warning' => 'info',
        'progressing' => 'info',
        'completed' => 'success',
        'error' => 'error',
    ];
    private static $messages = [
        'waiting' => 'files.messages.file_transcoding_queued',
        'warning' => 'files.messages.file_transcoding_warning',
        'progressing' => 'files.messages.file_transcoding_progressing',
        'completed' => 'files.messages.file_transcoding_complete',
        'error' => 'files.messages.file_transcoding_error',
    ];

    /**
     * Return the class required for showing notifications based on the status.
     *
     * @param  string  $status
     * @return string
     */
    public static function toasterClass($status)
    {
        self::validate($status, self::$types);

        return self::$types[$status];
    }

    /**
     * Returns the location of the message to be used for output.
     *
     * @param  string  $status
     * @return string
     */
    public static function message($status)
    {
        self::validate($status, self::$messages);

        return self::$messages[$status];
    }

    /**
     * Throws an exception if the $status provided does not exist in $data.
     *
     * @param  string  $status
     */
    private static function validate($status, array $data)
    {
        Assertion::keyExists($data, $status);
    }
}
