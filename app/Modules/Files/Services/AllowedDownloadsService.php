<?php

namespace AwardForce\Modules\Files\Services;

use AwardForce\Modules\Files\Models\File;
use Illuminate\Session\Store as Session;

class AllowedDownloadsService
{
    const SESSION_KEY = __CLASS__;

    /**
     * @var Session
     */
    protected $session;

    public function __construct(Session $session)
    {
        $this->session = $session;
    }

    /**
     * @return self
     */
    public function allow(File $file)
    {
        $this->session->push(self::SESSION_KEY, $file->id);

        return $this;
    }

    public function isAllowed($id)
    {
        return in_array($id, $this->session->get(self::SESSION_KEY, []));
    }
}
