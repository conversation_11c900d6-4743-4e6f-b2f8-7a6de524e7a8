<?php

namespace AwardForce\Modules\Files\Commands;

use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\Transcode;
use AwardForce\Modules\Files\Services\Transcoding;
use Platform\Events\EventDispatcher;

class ReTranscodeFilesCommandHandler
{
    use EventDispatcher;

    /**
     * @var Transcoding
     */
    private $transcoding;

    /**
     * @var FileRepository
     */
    private $files;

    /**
     * TranscodeFilesCommandHandler constructor.
     */
    public function __construct(FileRepository $files, Transcoding $transcoding)
    {
        $this->files = $files;
        $this->transcoding = $transcoding;
    }

    public function handle(ReTranscodeFilesCommand $command)
    {
        // First we fetch and clear out the transcoding files for each file
        $files = $this->files->getByIds($command->fileIds);

        foreach ($files as $file) {
            $file->retranscode();
            $this->transcoding->cleanup($file);
        }

        // Now we setup a new transcoding job
        $transcode = Transcode::log($command->user->id, $command->fileIds);

        $this->dispatch($transcode->releaseEvents());
    }
}
