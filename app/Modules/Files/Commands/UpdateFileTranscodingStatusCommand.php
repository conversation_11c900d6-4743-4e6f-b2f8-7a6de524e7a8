<?php

namespace AwardForce\Modules\Files\Commands;

class UpdateFileTranscodingStatusCommand
{
    public $status;
    public $transcodeId;
    public $fileId;
    public $outputs;

    public function __construct($status, $transcodeId, $fileId, array $outputs)
    {
        $this->status = $status;
        $this->transcodeId = $transcodeId;
        $this->fileId = $fileId;
        $this->outputs = $outputs;
    }
}
