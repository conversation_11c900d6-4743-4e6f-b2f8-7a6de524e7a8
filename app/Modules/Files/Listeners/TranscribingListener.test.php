<?php

use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Files\Commands\TranscribeFile;
use AwardForce\Modules\Files\Events\FileProcessed;
use AwardForce\Modules\Files\Listeners\TranscribingListener;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class TranscribingListenerTest extends BaseTestCase
{
    use Laravel;

    private TranscribingListener $listener;

    public function init(): void
    {
        Bus::fake();
        $this->listener = app(TranscribingListener::class);

        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(true);
    }

    public function testWhenFileProcessedOnlyProcessTranscribableFiles()
    {
        Feature::shouldReceive('enabled')->with('auto_caption')->andReturn(false);
        $file = new \AwardForce\Modules\Files\Models\File();
        $file->mime = 'video/mp4';
        $file->status = \AwardForce\Modules\Files\Models\File::STATUS_OK;

        $event = new FileProcessed($file);
        $listener = new TranscribingListener();
        $listener->whenFileProcessed($event);

        Bus::assertNotDispatched(TranscribeFile::class);
    }

    public function testWhenFileProcessedDispatchesTranscribeCommand()
    {
        Feature::shouldReceive('enabled')->with('auto_caption')->andReturn(true);
        $file = new \AwardForce\Modules\Files\Models\File();
        $file->id = 1;
        $file->mime = 'video/mp4';
        $file->status = \AwardForce\Modules\Files\Models\File::STATUS_OK;

        $event = new FileProcessed($file);
        $listener = new TranscribingListener();
        $listener->whenFileProcessed($event);

        Bus::assertDispatched(TranscribeFile::class);
    }
}
