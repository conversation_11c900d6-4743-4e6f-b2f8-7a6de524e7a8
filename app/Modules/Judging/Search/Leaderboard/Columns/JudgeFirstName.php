<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard\Columns;

use AwardForce\Modules\Assignments\Models\Assignment;
use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;

class JudgeFirstName implements Column
{
    public function title()
    {
        return trans('judging.export.heading.judge-first-name');
    }

    public function name(): string
    {
        return 'leaderboard.judge_first_name';
    }

    public function dependencies(): Collection
    {
        return collect([
            IncludeFilter::class,
        ]);
    }

    public function field()
    {
        return null;
    }

    /**
     * @param  Assignment  $assignment
     * @return string
     */
    public function value($assignment)
    {
        return $assignment->judge->firstName ?? null;
    }

    /**
     * @param  Assignment  $assignment
     * @return string
     */
    public function html($assignment)
    {
        return $this->value($assignment);
    }

    public function default(): Defaults
    {
        return new Defaults('export');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 100;
    }

    public function sortable(): bool
    {
        return false;
    }
}
