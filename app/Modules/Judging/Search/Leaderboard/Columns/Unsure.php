<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard\Columns;

use AwardForce\Modules\Judging\Data\Decision;
use AwardForce\Modules\Judging\Search\Filters\AssignmentDecisionFilter;
use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Unsure implements Column
{
    public function title()
    {
        return trans('judging.export.heading.unsure');
    }

    public function name(): string
    {
        return 'qualifying_decisions.unsure';
    }

    public function dependencies(): Collection
    {
        return collect([
            AssignmentDecisionFilter::class,
        ]);
    }

    public function field()
    {
        return 'assignment_decisions.decision as raw_decision';
    }

    public function value($assignment)
    {
        return $assignment->rawDecision == Decision::DECISION_UNSURE ? 1 : '';
    }

    public function html($assignment)
    {
        return $this->value($assignment);
    }

    public function default(): Defaults
    {
        return new Defaults('export');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 100;
    }

    public function sortable(): bool
    {
        return false;
    }
}
