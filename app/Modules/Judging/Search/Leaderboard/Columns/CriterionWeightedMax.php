<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard\Columns;

use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class CriterionWeightedMax implements Column
{
    /** @var ScoringCriterion */
    private $criterion;

    public function __construct(ScoringCriterion $criterion)
    {
        $this->criterion = $criterion;
    }

    public function title()
    {
        return trans('judging.export.heading.criterion-weighted-max', ['shortcode' => $this->criterion->shortcode]);
    }

    public function name(): string
    {
        return "{$this->criterion->slug}_weighted_max";
    }

    public function dependencies(): Collection
    {
        return collect([]);
    }

    public function field()
    {
        return null;
    }

    public function value($assignment)
    {
        return $this->criterion->weightedMaximumScore;
    }

    public function html($assignment)
    {
        return $this->value($assignment);
    }

    public function default(): Defaults
    {
        return new Defaults('export');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 100;
    }

    public function sortable(): bool
    {
        return false;
    }
}
