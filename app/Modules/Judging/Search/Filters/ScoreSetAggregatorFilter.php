<?php

namespace AwardForce\Modules\Judging\Search\Filters;

use Illuminate\Database\Eloquent\Builder;
use Platform\Search\Filters\ColumnatorFilter;

class ScoreSetAggregatorFilter implements ColumnatorFilter
{
    /**
     * @param  Builder  $query
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        $aggregations = [
            \DB::raw("CAST(JSON_EXTRACT(JSON_OBJECTAGG(assignments.score_set_id, assignments.id), '$') AS JSON) AS score_assignment_ids"),
        ];

        return $query->addSelect($aggregations);
    }

    /**
     * {@inheritDoc}
     */
    public function applies(): bool
    {
        return true;
    }
}
