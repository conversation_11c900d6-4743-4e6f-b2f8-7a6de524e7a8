<?php

namespace AwardForce\Modules\Judging\Search\Filters;

use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class TopPickWinnersFilter implements ColumnatorFilter, SearchFilter
{
    public function applyToEloquent($query)
    {
        return $query->where('assignments.top_pick_winner', true);
    }

    public function applies(): bool
    {
        return true;
    }
}
