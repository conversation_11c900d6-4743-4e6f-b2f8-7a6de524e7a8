<?php

namespace AwardForce\Modules\Judging\Search\Progress\Columns;

use AwardForce\Modules\Assignments\Models\Assignment;
use HTML;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Judge implements Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title()
    {
        return trans('judging.table.columns.judge');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'progress.judge';
    }

    /**
     * Returns the column's search filter dependencies.
     */
    public function dependencies(): Collection
    {
        return collect();
    }

    /**
     * Returns the name of the field in the query that should be present.
     *
     * @return string|null
     */
    public function field()
    {
        return DB::raw('CONCAT_WS(" ", users.first_name, users.last_name) as full_name');
    }

    /**
     * Return the value required in $record.
     *
     * @param  Assignment  $record
     * @return mixed
     */
    public function value($record)
    {
        $judge = $record->judge;

        return $judge->guest() ? trans('users.guest').' '.$judge->slug : $judge->fullName();
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @param  Assignment  $record
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return new HtmlString(HTML::userLink($record->judge));
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('all');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Give columns with particularly important information a higher visibility priority.
     */
    public function priority(): int
    {
        return 100;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }
}
