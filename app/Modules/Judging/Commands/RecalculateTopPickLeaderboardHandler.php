<?php

namespace AwardForce\Modules\Judging\Commands;

use AwardForce\Modules\Judging\Services\TopPick\Manager as TopPickManager;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;

class RecalculateTopPickLeaderboardHandler
{
    public function __construct(private TopPickManager $topPick, private ScoreSetRepository $scoreSets)
    {
    }

    public function handle(RecalculateTopPickLeaderboard $command)
    {
        $scoreSet = $this->scoreSets->requireById($command->scoreSetId);
        $this->topPick->calculate($scoreSet);
    }
}
