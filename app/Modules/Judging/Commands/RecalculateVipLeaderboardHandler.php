<?php

namespace AwardForce\Modules\Judging\Commands;

use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Judging\Services\RecalculationScoreKeeper;

class RecalculateVipLeaderboardHandler
{
    public function __construct(private RecalculationScoreKeeper $scoreKeeper, private AssignmentRepository $assignments)
    {
    }

    public function handle(RecalculateVipLeaderboard $command)
    {
        $vipAssignments = $this->assignments
            ->fields(['assignments.judge_id', 'assignments.entry_id'])
            ->with(['entry'])
            ->ids($command->assignments->toArray())
            ->get();

        foreach ($vipAssignments as $assignment) {
            if (! $assignment->entry) {
                continue;
            }

            $this->scoreKeeper->sync(new AssignmentUser($assignment->judgeId), $assignment->entry);
        }
    }
}
