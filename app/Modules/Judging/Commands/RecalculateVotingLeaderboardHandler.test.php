<?php

namespace AwardForce\Modules\Judging\Commands;

use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Judging\Data\Vote;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class RecalculateVotingLeaderboardHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testVotingRecalculation(): void
    {
        $one = $this->muffin(Assignment::class);
        $one->votes()->saveMany([new Vote(['votes' => 5]), new Vote(['votes' => 7])]);

        $two = $this->muffin(Assignment::class, ['score_set_id' => $one->scoreSetId]);
        $two->votes()->saveMany([new Vote(['votes' => 3]), new Vote(['votes' => 0])]);

        $other = $this->muffin(Assignment::class);
        $other->votes()->saveMany([new Vote(['votes' => 2]), new Vote(['votes' => 5])]);

        $command = new RecalculateVotingLeaderboard($one->scoreSet->id);
        app(RecalculateVotingLeaderboardHandler::class)->handle($command);

        $this->assertEquals(12, $one->fresh()->totalVotes);
        $this->assertEquals(3, $two->fresh()->totalVotes);
        $this->assertEquals(0, $other->fresh()->totalVotes);
    }
}
