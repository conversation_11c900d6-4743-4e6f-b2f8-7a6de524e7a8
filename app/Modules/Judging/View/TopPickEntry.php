<?php

namespace AwardForce\Modules\Judging\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Assignments\Services\CurrentPreferences;
use AwardForce\Modules\Comments\Services\Pages\TopPickComments;
use AwardForce\Modules\Entries\Services\VisibleAttachments;
use AwardForce\Modules\Forms\Fields\Services\ConditionalFieldsFilterer;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\AttachmentTypes;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Factory;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\VisibleFields;
use AwardForce\Modules\Judging\Services\TagEntryPermissions;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Tectonic\LaravelLocalisation\Translator\Engine;

class TopPickEntry extends View
{
    use JudgingView;

    /** @var CurrentAssignments */
    private $assignments;

    /** @var CurrentPreferences */
    private $preferences;

    /** @var ConditionalFieldsFilterer */
    private $filterer;

    /** @var VisibleFields */
    private $visibleFields;

    /** @var VisibleAttachments */
    private $visibleAttachments;

    /** @var AttachmentTypes */
    private $attachmentTypes;

    private TagEntryPermissions $tagEntryPermissions;

    public function __construct(
        Request $request,
        Engine $translator,
        Manager $manager,
        CurrentAssignments $assignments,
        CurrentPreferences $preferences,
        ConditionalFieldsFilterer $filterer,
        Factory $factory,
        VisibleAttachments $visibleAttachments,
        AttachmentTypes $attachmentTypes,
        TagEntryPermissions $tagEntryPermissions
    ) {
        parent::__construct($request, $translator, $manager);

        $this->assignments = $assignments;
        $this->preferences = $preferences;
        $this->filterer = $filterer;
        $this->visibleAttachments = $visibleAttachments;
        $this->attachmentTypes = $attachmentTypes;
        $this->visibleFields = $factory->scoreSetBased($this->scoreSet);
        $this->tagEntryPermissions = $tagEntryPermissions;
        $this->registerTranslations();
    }

    /**
     * @return \AwardForce\Modules\Assignments\Models\Assignment
     */
    public function assignment()
    {
        $assignment = $this->assignments->forJudgeEntry(
            $this->scoreSet->id,
            new AssignmentUser($this->consumer()),
            $this->entry->id
        )->first();

        if (! $assignment) {
            throw new ModelNotFoundException;
        }

        return $assignment;
    }

    public function currentPreferences()
    {
        return $this->preferences->judgePreferencesForView($this->scoreSet, new AssignmentUser($this->consumer()), $this->entry);
    }

    /**
     * @return \AwardForce\Modules\Categories\Models\Category
     */
    public function category()
    {
        $category = $this->request->category;

        return $category && $category->exists ? $category : null;
    }

    /**
     * @return \AwardForce\Modules\Entries\Models\Entry
     */
    public function entry()
    {
        return $this->translator->translate($this->request->entry);
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function attachments()
    {
        $attachments = $this->visibleAttachments->attachments($this->entry, $this->scoreSet);
        $attachmentTypes = $this->attachmentTypes->fromScoreSet($this->scoreSet, $this->entry->category->attachmentTypes);

        $attachmentFields = $this->visibleFields->forAttachments($this->entry, $attachments, $attachmentTypes);

        return $this->translator->translate($attachmentFields);
    }

    /**
     * @return \Platform\Database\Eloquent\Collection
     */
    public function links()
    {
        return $this->visibleAttachments->links($this->entry, $this->scoreSet);
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function fields()
    {
        return $this->translator->translate($this->visibleFields->forSubmittable($this->entry));
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function contributors()
    {
        return $this->translator->translate($this->visibleFields->forContributors($this->entry));
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function userFields()
    {
        return $this->translator->translate($this->visibleFields->forUser($this->entry));
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function galleryFromFileFields()
    {
        return $this->fields->onlyImages();
    }

    /**
     * @return bool
     */
    public function displayMetadata()
    {
        return $this->scoreSet->metadata;
    }

    /**
     * @return array
     */
    public function plagiarismScans()
    {
        if (! $this->scoreSet->displayPlagiarismScan) {
            return null;
        }

        return $this->entry->completedPlagiarismScans->forView();
    }

    public function topPickComments()
    {
        if (Consumer::isGuest() || ! $this->scoreSet->comments) {
            return [];
        }

        return (new TopPickComments($this->scoreSet, true))->forEntry($this->entry);
    }

    public function registerTranslations(): void
    {
        VueData::registerTranslations([
            'judging.picks.top',
            'judging.picks.bottom',
            'files.metadata.title',
            'files.metadata.labels.size',
            'files.metadata.not_found',
        ]);
    }

    public function routes(): void
    {
        VueData::registerRoutes([
            'file.metadata',
        ]);
    }

    public function canTag(): bool
    {
        return $this->tagEntryPermissions->canTagEntry($this->entry());
    }
}
