<?php

namespace AwardForce\Modules\Judging\Composers;

use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Panels\Models\PanelRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Facades\Translator;

class ProgressComposer
{
    /**
     * @var ScoreSetRepository
     */
    private $scoreSets;

    /**
     * @var SeasonRepository
     */
    private $seasons;

    /**
     * @var PanelRepository
     */
    private $panels;

    /**
     * @var EntryRepository
     */
    private $entries;

    private $seasonFilter;

    public function __construct(
        ScoreSetRepository $scoreSets,
        SeasonRepository $seasons,
        PanelRepository $panels,
        EntryRepository $entries,
        SeasonFilterService $seasonFilter
    ) {
        $this->scoreSets = $scoreSets;
        $this->seasons = $seasons;
        $this->panels = $panels;
        $this->entries = $entries;
        $this->seasonFilter = $seasonFilter;
    }

    public function compose(View $view)
    {
        // Active season ID
        $seasonId = $this->seasonFilter->getId();

        // Score sets
        $view->with('scoreSets', $this->getScoreSets($seasonId)?->forGroupedSelect());

        // Panels
        $view->with('panels', Translator::translate($this->panels->getForSeason($seasonId)));

        // Abstensions (vip judging)
        if ($view->scoreSet && $view->scoreSet->mode == ScoreSet::MODE_VIP) {
            $view->with('abstensions', $this->loadAbstensions($view->judges->getCollection()));
        }
    }

    private function getScoreSets($seasonId)
    {
        if ($this->seasonFilter->viewingAll()) {
            return translate($this->scoreSets->getAll()->withoutGalleries()?->load('form:id'));
        }

        return translate($this->scoreSets->getForSeason($seasonId, [ScoreSet::MODE_GALLERY])?->load('form:id'));
    }

    /**
     * Intelligently loads abstension (entry) records based off the provided judges collection.
     *
     * @return Collection
     */
    private function loadAbstensions(Collection $judges)
    {
        // Clean up IDs into a simple array
        $ids = merge_group_concat($judges->pluck('abstensionIds'));

        // Retrieve entries, and related categories
        $entries = $this->entries->getByIds($ids);
        $entries->load('category');

        return Translator::translate($entries);
    }
}
