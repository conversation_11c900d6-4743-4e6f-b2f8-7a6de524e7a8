<?php

namespace AwardForce\Modules\Judging\Services;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;

class DefaultConfigurationGenerator
{
    public static function forScoreSet(string $name, string $mode): array
    {
        return (new ScoreSetConfiguration)->generate(
            $name,
            trans('judging.fast-start.resources.score-set'),
            ['mode' => $mode]
        );
    }

    public static function forScoringCriterionView(string $name): array
    {
        $config = self::forScoringCriterion(self::fallbackTranslationsForName($name));
        $config['translated'] = self::translationsForView($config['translated']);

        return $config;
    }

    /** @return array<string, string> */
    public static function fallbackTranslationsForName(string $name): array
    {
        return [
            Consumer::languageCode() => $name,
            CurrentAccount::defaultLanguage()->code => $name,
        ];
    }

    public static function forScoringCriterion(array $nameTranslations): array
    {
        return (new ScoringCriterionConfiguration)->generate($nameTranslations);
    }

    public static function forRound(string $name): array
    {
        return (new RoundConfiguration)->generate($name, trans('audit.resources.round'));
    }

    public static function forPanel(string $name): array
    {
        return (new PanelConfiguration)->generate($name, trans('panels.table.columns.name'));
    }

    private static function translationsForView(array $translated): array
    {
        $viewTranslations = [];
        foreach ($translated as $key => $translation) {
            foreach ($translation as $langCode => $value) {
                $viewTranslations[$langCode][$key] = $value;
            }
        }

        return $viewTranslations;
    }
}
