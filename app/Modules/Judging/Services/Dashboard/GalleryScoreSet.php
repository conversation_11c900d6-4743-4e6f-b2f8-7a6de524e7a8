<?php

namespace AwardForce\Modules\Judging\Services\Dashboard;

use AwardForce\Modules\Judging\Services\Dashboard\Contracts\GalleryableScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Illuminate\Support\Arr;

class GalleryScoreSet implements GalleryableScoreSet
{
    public function __construct(
        protected ScoreSet $scoreSet,
        protected array $statistics,
        protected array $coverImage,
        protected ?array $status
    ) {
    }

    public function getForVue(): array
    {
        return collect(array_merge($this->getAllowedScoreSetAttributes(), [
            'name' => $this->scoreSet->name,
            'status' => $this->status,
            'coverImage' => $this->coverImage,
            'order' => $this->scoreSet->order,
            'statisticsUrl' => route('gallery.score-sets-statistics.index', [
                'scoreSet' => (string) $this->scoreSet->slug,
            ]),
        ]))

            ->sortBy('order')
            ->toArray();
    }

    private function getAllowedScoreSetAttributes(): array
    {
        return Arr::only($this->scoreSet->attributesToArray(), [
            'id', 'slug',
        ]);
    }
}
