<?php

namespace AwardForce\Modules\Judging\Services\Dashboard;

use AwardForce\Modules\Judging\Search\Progress\ProgressColumnator;
use AwardForce\Modules\Judging\Search\Progress\VotingColumnator;

class VotingProgress extends Progress
{
    protected function columnator(): ProgressColumnator
    {
        return new VotingColumnator([], ['score-set' => $this->scoreSet->id]);
    }

    public function getProgress(): array
    {
        $results = $this->search();

        $total = $results->sum('totalVotes');

        return [
            'completed' => $total,
            'total' => $total,
            'progress' => null,
            'data' => $this->data($total, $total),
            'label' => trans_choice('judging.dashboard.votes.label', $total),
        ];
    }
}
