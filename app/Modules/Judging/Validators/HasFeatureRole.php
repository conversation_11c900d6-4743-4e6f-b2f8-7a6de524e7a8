<?php

namespace AwardForce\Modules\Judging\Validators;

use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Platform\Authorisation\FeatureRoles\FeatureRole;

class HasFeatureRole implements ValidationRule
{
    public function __construct(protected FeatureRole $featureRole, protected UserRepository $users, protected string $errorMessage)
    {
    }

    public function validate(string $attribute, mixed $slugs, Closure $fail): void
    {
        $authorizedUserSlugs = $this->users
            ->slugs($slugs)
            ->joinPermissions()
            ->allowedWith(...$this->featureRole::permissions())
            ->fields(['users.slug'])
            ->get()
            ->pluck('slug')
            ->all();

        $unauthorizedUserSlugs = array_diff($slugs, $authorizedUserSlugs);

        if (! empty($unauthorizedUserSlugs)) {
            $fail($this->errorMessage)->translate([
                'users' => implode(', ', $unauthorizedUserSlugs),
            ]);
        }
    }
}
