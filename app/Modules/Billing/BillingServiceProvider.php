<?php

namespace AwardForce\Modules\Billing;

use AwardForce\Library\Providers\Providable;
use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\AIAgents\Events\AITokensConsumed;
use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\ElasticSearchUsageLogRepository;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Gateways\ChargebeeUsageBillingGateway;
use AwardForce\Modules\Billing\Gateways\ChargebeeUsageEventPayloadFactory;
use AwardForce\Modules\Billing\Listeners\LogUsage;
use AwardForce\Modules\PaymentSubscriptions\Events\SubscriptionWasProcessed;

class BillingServiceProvider extends ServiceProvider
{
    use Providable;

    protected $repositories = [
        UsageLogRepository::class => ElasticSearchUsageLogRepository::class,
    ];

    /**
     * Define the listeners for this module.
     *
     * @var array
     */
    protected $listeners = [
        SubscriptionWasProcessed::class => 'AwardForce\Modules\Billing\Listeners\BillingListener@whenSubscriptionWasProcessed',
        AITokensConsumed::class => LogUsage::class,
    ];

    public function register(): void
    {
        parent::register();

        $this->registerChargebee();
    }

    public function registerChargebee(): void
    {
        $this->app->bind(UsageBillingGateway::class, ChargebeeUsageBillingGateway::class);
        $this->app->bind(UsageEventPayloadFactory::class, ChargebeeUsageEventPayloadFactory::class);
    }
}
