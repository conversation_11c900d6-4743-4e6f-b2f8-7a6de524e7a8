<?php

declare(strict_types=1);

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Library\Database\ElasticSearch\Repository;
use AwardForce\Modules\Billing\Enums\Status;
use Elasticsearch\Client as ElasticsearchClient;
use Elasticsearch\ClientBuilder;

class ElasticSearchUsageLogRepository extends Repository implements UsageLogRepository
{
    private ElasticsearchClient $elasticsearch;

    public function __construct(UsageLog $model)
    {
        $this->model = $model;
        $this->elasticsearch = ClientBuilder::create()->build();
    }

    public function updateStatusByIds(array $ids, Status $status): void
    {
        if (empty($ids)) {
            return;
        }

        $this->elasticsearch->updateByQuery([
            'index' => $this->model->getIndex(),
            'refresh' => true,
            'body' => [
                'query' => [
                    'terms' => ['_id' => $ids],
                ],
                'script' => [
                    'source' => 'ctx._source.status = params.status; ctx._source.processing_id = params.processingId',
                    'lang' => 'painless',
                    'params' => [
                        'status' => $status->value,
                        'processingId' => null,
                    ],
                ],
            ],
        ]);
    }

    public function resetBatchToReady(string $processingId): void
    {
        $this->elasticsearch->updateByQuery([
            'index' => $this->model->getIndex(),
            'refresh' => true,
            'body' => [
                'query' => [
                    'term' => ['processing_id' => $processingId],
                ],
                'script' => [
                    'source' => 'ctx._source.status = params.status; ctx._source.processing_id = params.processingId',
                    'lang' => 'painless',
                    'params' => [
                        'status' => Status::Ready->value,
                        'processingId' => null,
                    ],
                ],
            ],
        ]);
    }

    public function claimEventsForProcessing(
        string $processingId,
        array $accountIds,
        int $maxEvents
    ): void {
        if (empty($accountIds)) {
            return;
        }

        $this->elasticsearch->updateByQuery([
            'index' => $this->model->getIndex(),
            'refresh' => true, // Ensure changes are visible for the subsequent search
            'size' => $maxEvents,
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            ['term' => ['status' => Status::Ready->value]],
                            ['terms' => ['account_id' => $accountIds]],
                        ],
                    ],
                ],
                'sort' => [['created_at' => 'asc']],
                'script' => [
                    'source' => 'ctx._source.status = params.status; ctx._source.processing_id = params.processingId',
                    'lang' => 'painless',
                    'params' => ['status' => Status::Processing->value, 'processingId' => $processingId],
                ],
            ],
        ]);
    }
}
