<?php

declare(strict_types=1);

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\Status;

interface UsageLogRepository
{
    /**
     * Update the status of usage logs by their IDs.
     */
    public function updateStatusByIds(array $ids, Status $status): void;

    /**
     * Reset all usage logs with the given processing ID back to Ready status.
     */
    public function resetBatchToReady(string $processingId): void;

    /**
     * Atomically claim usage logs for processing.
     * This prevents race conditions where multiple processes grab the same logs.
     */
    public function claimEventsForProcessing(
        string $processingId,
        array $accountIds,
        int $maxEvents
    ): void;
}
