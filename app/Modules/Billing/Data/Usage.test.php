<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UsageTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItBeCanInitiatedWithDefaults(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 150];
        $loggable = new Loggable(LoggableType::Entry, random_int(10000, 1000000));
        $metadata = ['source' => 'test'];

        $usage = Usage::withDefaults($event, $metrics, $loggable, $metadata);

        $this->assertSame($event, $usage->event);
        $this->assertSame($metrics, $usage->metrics);
        $this->assertSame($loggable, $usage->loggable);
        $this->assertSame($metadata, $usage->metadata);
        $this->assertSame(Status::Ready, $usage->status);
    }

    public function testItCanBeInitiatedWithoutTrackableModelAndMetadataParameters(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 150];

        $usage = Usage::withDefaults(
            event: $event,
            metrics: $metrics
        );

        $this->assertSame($event, $usage->event);
        $this->assertSame($metrics, $usage->metrics);
        $this->assertNull($usage->loggable);
        $this->assertSame([], $usage->metadata);
        $this->assertSame(Status::Ready, $usage->status);
    }
}
