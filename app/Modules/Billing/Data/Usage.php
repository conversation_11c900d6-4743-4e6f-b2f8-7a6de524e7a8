<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Webmozart\Assert\Assert;

final readonly class Usage
{
    private function __construct(
        public string $event,
        public array $metrics,
        public Status $status,
        public int $accountId,
        public ?int $userId,
        public ?Loggable $loggable = null,
        public array $metadata = [],
    ) {
        Assert::inArray($status, [Status::Pending, Status::Ready]);
    }

    public static function withDefaults(
        string $event,
        array $metrics,
        ?Loggable $loggable = null,
        array $metadata = [],
    ): self {
        return new self(
            event: $event,
            metrics: $metrics,
            status: Status::Ready,
            accountId: current_account_id(),
            userId: consumer_id(),
            loggable: $loggable,
            metadata: $metadata,
        );
    }
}
