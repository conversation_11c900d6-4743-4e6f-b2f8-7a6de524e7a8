<?php

namespace AwardForce\Modules\Billing\Gateways;

use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\UsageLog;
use RuntimeException;

class ChargebeeUsageEventPayloadFactory implements UsageEventPayloadFactory
{
    public function create(UsageLog $usageLog): array
    {
        if (! ($subscriptionId = $usageLog->account?->subscriptionId)) {
            throw new RuntimeException("Account {$usageLog->accountId} does not have a subscription ID.");
        }

        return [
            'deduplication_id' => $usageLog->getID(),
            'subscription_id' => $subscriptionId,
            'usage_timestamp' => $usageLog->createdAt,
            'properties' => array_merge(
                [
                    'event_id' => $usageLog->getID(),
                    'event' => $usageLog->event,
                ],
                $usageLog->metrics,
                $usageLog->metadata
            ),
        ];
    }
}
