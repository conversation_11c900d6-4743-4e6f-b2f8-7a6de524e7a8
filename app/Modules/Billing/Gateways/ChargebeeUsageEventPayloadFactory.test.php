<?php

namespace AwardForce\Modules\Billing\Gateways;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Billing\Data\UsageLog;
use RuntimeException;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ChargebeeUsageEventPayloadFactoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testCreatesPayloadSuccessfully(): void
    {
        $this->freezeTime();

        $usageLog = $this->muffin(UsageLog::class, [
            'account_id' => $this->muffin(Account::class, ['subscriptionId' => 'sub_123'])->id,
            'metrics' => ['metric1' => 'value1'],
            'metadata' => ['meta1' => 'value2'],
            'event' => 'ai_tokens_consumed',
        ]);

        $factory = new ChargebeeUsageEventPayloadFactory();
        $result = $factory->create($usageLog);

        $this->assertEqualsCanonicalizing([
            'deduplication_id' => $usageLog->getID(),
            'subscription_id' => 'sub_123',
            'usage_timestamp' => $usageLog->createdAt,
            'properties' => ['event_id' => $usageLog->getID(), 'event' => 'ai_tokens_consumed', 'metric1' => 'value1', 'meta1' => 'value2'],
        ], $result);
    }

    public function testThrowsExceptionWhenSubscriptionIdIsMissing(): void
    {
        $usageLog = $this->muffin(UsageLog::class, [
            'account_id' => $this->muffin(Account::class, ['subscriptionId' => null])->id,
        ]);

        $factory = new ChargebeeUsageEventPayloadFactory();

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage("Account $usageLog->accountId does not have a subscription ID.");

        $factory->create($usageLog);
    }
}
