<?php

namespace AwardForce\Modules\Billing\Gateways;

use Chargebee\ChargebeeClient;
use Chargebee\Responses\UsageEventResponse\BatchIngestUsageEventResponse;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ChargebeeUsageBillingGatewayTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testIngestBatchReturnsEmptyResultForEmptyPayload(): void
    {
        $gateway = app(ChargebeeUsageBillingGateway::class);

        $result = $gateway->ingestBatch([], 'awardforce');

        $this->assertSame([], $result->succeededIds);
        $this->assertSame([], $result->failedIds);
    }

    public function testIngestBatchReturnsCorrectSucceededAndFailedIds(): void
    {
        $chargebeeMock = $this->mock(ChargebeeClient::class);
        $chargebeeMock->shouldReceive('usageEvent->batchIngest')
            ->once()
            ->andReturn(BatchIngestUsageEventResponse::from([
                'batch_id' => str_random(),
                'failed_events' => [
                    ['deduplication_id' => 'id2', 'properties' => ['event_id' => 'id2']],
                    ['deduplication_id' => 'id4', 'properties' => ['event_id' => 'id4']],
                ],
            ]));

        $factoryMock = $this->mock(ChargebeeClientFactory::class);
        $factoryMock->shouldReceive('make')
            ->with('awardforce')
            ->once()
            ->andReturn($chargebeeMock);

        $gateway = new ChargebeeUsageBillingGateway($factoryMock);
        $result = $gateway->ingestBatch([
            ['deduplication_id' => 'id1', 'properties' => ['event_id' => 'id1']],
            ['deduplication_id' => 'id2', 'properties' => ['event_id' => 'id2']],
            ['deduplication_id' => 'id3', 'properties' => ['event_id' => 'id3']],
            ['deduplication_id' => 'id4', 'properties' => ['event_id' => 'id4']],
        ], 'awardforce');

        $this->assertEquals(['id1', 'id3'], $result->succeededIds);
        $this->assertEquals(['id2', 'id4'], $result->failedIds);
    }
}
