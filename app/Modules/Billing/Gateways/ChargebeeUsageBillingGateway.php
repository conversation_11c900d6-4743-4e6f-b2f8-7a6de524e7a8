<?php

namespace AwardForce\Modules\Billing\Gateways;

use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;

class ChargebeeUsageBillingGateway implements UsageBillingGateway
{
    public function __construct(
        private readonly ChargebeeClientFactory $clientFactory,
    ) {
    }

    public function ingestBatch(array $eventsPayload, string $subscriptionProvider): BatchIngestionResult
    {
        if (empty($eventsPayload)) {
            return new BatchIngestionResult([], []);
        }

        $result = $this->clientFactory->make($subscriptionProvider)
            ->usageEvent()
            ->batchIngest(['events' => $eventsPayload]);

        $allSentIds = array_column($eventsPayload, 'deduplication_id');
        $failedIds = array_map(static fn(array $event) => array_get($event, 'properties.event_id'), $result->failed_events ?? []);
        $succeededIds = array_values(array_diff($allSentIds, $failedIds));

        return new BatchIngestionResult($succeededIds, $failedIds);
    }

    public function maxEventsPerBatch(): int
    {
        return 500;
    }

    public function maxBatchSizeBytes(): int
    {
        return 490 * 1024; // A maximum of 500 KB of data is allowed. But we'll do 490 KB max to be safe.
    }
}
