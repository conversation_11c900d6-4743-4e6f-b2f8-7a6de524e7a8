<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\Billing\Data\Usage;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Events\UsageLogged;

class UsageTracker
{
    public function __construct(private readonly UsageLogRepository $usageLogRepository)
    {
    }

    public function log(Usage $usage): UsageLog
    {
        $usageLog = new UsageLog;
        $usageLog->event = $usage->event;
        $usageLog->status = $usage->status;
        $usageLog->metrics = $usage->metrics;
        $usageLog->metadata = $usage->metadata;
        $usageLog->accountId = $usage->accountId;
        $usageLog->userId = $usage->userId;

        if ($usage->loggable) {
            $usageLog->loggableType = $usage->loggable->type;
            $usageLog->loggableId = $usage->loggable->id;
        }

        $usageLog->createdAt = $now = now()->getTimestampMs();
        $usageLog->updatedAt = $now;

        $this->usageLogRepository->save($usageLog);

        event(new UsageLogged($usageLog));

        return $usageLog;
    }
}
