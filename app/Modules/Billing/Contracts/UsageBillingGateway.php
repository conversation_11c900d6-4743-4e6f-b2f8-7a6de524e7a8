<?php

namespace AwardForce\Modules\Billing\Contracts;

use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;

interface UsageBillingGateway
{
    /**
     * Ingest a batch of usage events to the usage billing provider.
     */
    public function ingestBatch(array $eventsPayload, string $subscriptionProvider): BatchIngestionResult;

    public function maxEventsPerBatch(): int;

    public function maxBatchSizeBytes(): int;
}
