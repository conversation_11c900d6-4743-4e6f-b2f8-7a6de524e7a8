<?php

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Elasticsearch\Client as ElasticsearchClient;
use Elasticsearch\ClientBuilder;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use RuntimeException;
use Throwable;

readonly class SyncUsageLogsHandler
{
    private ElasticsearchClient $elasticsearch;

    public function __construct(
        private AccountRepository $accounts,
        private UsageEventPayloadFactory $usageEventPayloadFactory,
        private UsageBillingGateway $usageBillingGateway,
    ) {
        $this->elasticsearch = ClientBuilder::create()->build();
    }

    public function handle(SyncUsageLogs $command): void
    {
        $processingId = uniqid('sync-job-', true);

        try {
            $this->claimEventsForProcessing($processingId, $command->subscriptionProvider);

            $eventsToProcess = UsageLog::where('status', Status::Processing)
                ->where('processing_id', $processingId)
                ->orderBy('created_at')
                ->get();

            if ($eventsToProcess->isEmpty()) {
                return;
            }

            $this->processBatch($eventsToProcess, $command->subscriptionProvider);
        } catch (Throwable $e) {
            $this->resetBatchToReady($processingId);
            throw $e;
        }
    }

    private function processBatch(Collection $eventsToProcess, string $subscriptionProvider): void
    {
        $usageEvents = $this->createUsageEvents($eventsToProcess);

        if (mb_strlen(Json::encode($usageEvents), '8bit') > $this->usageBillingGateway->maxBatchSizeBytes()) {
            throw new RuntimeException('Batch size too large.');
        }

        $result = $this->usageBillingGateway->ingestBatch($usageEvents, $subscriptionProvider);
        $this->updateUsageLogStatuses($result);
    }

    private function createUsageEvents(Collection $events): array
    {
        return $events
            ->map(fn(UsageLog $usageLog) => $this->usageEventPayloadFactory->create($usageLog))
            ->all();
    }

    private function updateUsageLogStatuses(BatchIngestionResult $result): void
    {
        if (! empty($result->succeededIds)) {
            $this->updateStatusByIds($result->succeededIds, Status::Synced);
        }

        if (! empty($result->failedIds)) {
            defer(static fn() => Log::error('Failed to sync usage logs', $result->failedIds));
            $this->updateStatusByIds($result->failedIds, Status::Failed);
        }
    }

    private function updateStatusByIds(array $ids, Status $status): void
    {
        $this->elasticsearch->updateByQuery([
            'index' => (new UsageLog())->getIndex(),
            'refresh' => true,
            'body' => [
                'query' => [
                    'terms' => ['_id' => $ids],
                ],
                'script' => [
                    'source' => 'ctx._source.status = params.status; ctx._source.processing_id = params.processingId',
                    'lang' => 'painless',
                    'params' => [
                        'status' => $status->value,
                        'processingId' => null,
                    ],
                ],
            ],
        ]);
    }

    private function resetBatchToReady(string $processingId): void
    {
        $this->elasticsearch->updateByQuery([
            'index' => (new UsageLog())->getIndex(),
            'refresh' => true,
            'body' => [
                'query' => [
                    'term' => ['processing_id' => $processingId],
                ],
                'script' => [
                    'source' => 'ctx._source.status = params.status; ctx._source.processing_id = params.processingId',
                    'lang' => 'painless',
                    'params' => [
                        'status' => Status::Ready->value,
                        'processingId' => null,
                    ],
                ],
            ],
        ]);
    }

    /**
     * Atomically claims a batch of logs.
     * This prevents race conditions where multiple processes grab the same logs.
     */
    private function claimEventsForProcessing(string $processingId, string $subscriptionProvider): void
    {
        $accountIds = $this->accounts->subscriptionProvider($subscriptionProvider)->just('id');

        $this->elasticsearch->updateByQuery([
            'index' => (new UsageLog())->getIndex(),
            'refresh' => true, // Ensure changes are visible for the subsequent search
            'size' => $this->usageBillingGateway->maxEventsPerBatch(),
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [
                            ['term' => ['status' => Status::Ready->value]],
                            ['terms' => ['account_id' => $accountIds]],
                        ],
                    ],
                ],
                'sort' => [['created_at' => 'asc']],
                'script' => [
                    'source' => 'ctx._source.status = params.status; ctx._source.processing_id = params.processingId',
                    'lang' => 'painless',
                    'params' => ['status' => Status::Processing->value, 'processingId' => $processingId],
                ],
            ],
        ]);
    }
}
