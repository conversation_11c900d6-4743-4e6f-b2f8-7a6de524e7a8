<?php

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class SyncUsageLogsHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected function init(): void
    {
        UsageLog::all()->each->delete();
    }

    public function testItCanSyncUsageLogs(): void
    {
        $usageLogs = $this->muffins(3, UsageLog::class, ['status' => Status::Ready]);
        $failingUsageLog = $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $usageBillingGateway = $this->mock(UsageBillingGateway::class);
        $usageBillingGateway->shouldReceive('ingestBatch')
            ->once()
            ->andReturn(new BatchIngestionResult(collect($usageLogs)->just('_id'), [$failingUsageLog->getID()]));
        $usageBillingGateway->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBillingGateway->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);

        $command = new SyncUsageLogs(current_account()->subscriptionProvider);
        $handler = new SyncUsageLogsHandler(app(AccountRepository::class), app(UsageEventPayloadFactory::class), $usageBillingGateway);
        $handler->handle($command);
        UsageLog::refreshIndex();

        foreach ($usageLogs as $usageLog) {
            $this->assertEquals(Status::Synced, UsageLog::find($usageLog->getID())->status);
        }

        $this->assertEquals(Status::Failed, UsageLog::find($failingUsageLog->getID())->status);
    }
}
