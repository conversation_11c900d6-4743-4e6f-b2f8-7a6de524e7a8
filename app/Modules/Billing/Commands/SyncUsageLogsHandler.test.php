<?php

declare(strict_types=1);

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Contracts\UsageBillingGateway;
use AwardForce\Modules\Billing\Contracts\UsageEventPayloadFactory;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class SyncUsageLogsHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected function init(): void
    {
        UsageLog::all()->each->delete();
    }

    public function testItCanSyncUsageLogs(): void
    {
        $processingId = 'test-processing-id';
        $usageLogs = $this->muffins(3, UsageLog::class, [
            'status' => Status::Processing,
            'processing_id' => $processingId,
        ]);
        $failingUsageLog = $this->muffin(UsageLog::class, [
            'status' => Status::Processing,
            'processing_id' => $processingId,
        ]);

        UsageLog::refreshIndex();

        $usageBillingGateway = $this->mock(UsageBillingGateway::class);
        $usageBillingGateway->shouldReceive('ingestBatch')
            ->once()
            ->andReturn(new BatchIngestionResult(collect($usageLogs)->just('_id'), [$failingUsageLog->getID()]));
        $usageBillingGateway->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBillingGateway->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);

        $usageLogRepository = $this->mock(UsageLogRepository::class);
        $usageLogRepository->shouldReceive('claimEventsForProcessing')
            ->once()
            ->with(\Mockery::type('string'), \Mockery::type('array'), 500)
            ->andReturnUsing(function ($id, $accountIds, $maxEvents) use ($processingId, $usageLogs, $failingUsageLog) {
                // Simulate the repository claiming the events by setting their processing_id
                foreach ($usageLogs as $log) {
                    $log->processing_id = $id;
                    $log->status = Status::Processing;
                    $log->save();
                }
                $failingUsageLog->processing_id = $id;
                $failingUsageLog->status = Status::Processing;
                $failingUsageLog->save();
                UsageLog::refreshIndex();
            });
        $usageLogRepository->shouldReceive('updateStatusByIds')
            ->once()
            ->with(collect($usageLogs)->just('_id'), Status::Synced);
        $usageLogRepository->shouldReceive('updateStatusByIds')
            ->once()
            ->with([$failingUsageLog->getID()], Status::Failed);

        $command = new SyncUsageLogs(current_account()->subscriptionProvider);
        $handler = new SyncUsageLogsHandler(
            app(AccountRepository::class),
            app(UsageEventPayloadFactory::class),
            $usageBillingGateway,
            $usageLogRepository
        );
        $handler->handle($command);
    }

    public function testItResetsProcessingStatusOnError(): void
    {
        $processingId = 'error-test-id';

        // Create a processing log to simulate claimed events
        $processingLog = $this->muffin(UsageLog::class, [
            'status' => Status::Processing,
            'processing_id' => $processingId,
        ]);
        UsageLog::refreshIndex();

        $usageBillingGateway = $this->mock(UsageBillingGateway::class);
        $usageBillingGateway->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBillingGateway->shouldReceive('ingestBatch')
            ->once()
            ->andThrow(new \RuntimeException('Test error'));
        $usageBillingGateway->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);

        $usageLogRepository = $this->mock(UsageLogRepository::class);
        $usageLogRepository->shouldReceive('claimEventsForProcessing')
            ->once()
            ->with(\Mockery::type('string'), \Mockery::type('array'), 500)
            ->andReturnUsing(function ($id, $accountIds, $maxEvents) use ($processingLog) {
                // Simulate the repository claiming the event
                $processingLog->processing_id = $id;
                $processingLog->status = Status::Processing;
                $processingLog->save();
                UsageLog::refreshIndex();
            });
        $usageLogRepository->shouldReceive('resetBatchToReady')
            ->once()
            ->with(\Mockery::type('string'));

        $command = new SyncUsageLogs(current_account()->subscriptionProvider);
        $handler = new SyncUsageLogsHandler(
            app(AccountRepository::class),
            app(UsageEventPayloadFactory::class),
            $usageBillingGateway,
            $usageLogRepository
        );

        $this->expectException(\RuntimeException::class);
        $this->expectExceptionMessage('Test error');

        $handler->handle($command);
    }
}
