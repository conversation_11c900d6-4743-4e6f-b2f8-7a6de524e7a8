<?php

namespace AwardForce\Modules\PaymentScheduleTemplates\View;

use AwardForce\Modules\Localisation\Services\VueFormatter;
use AwardForce\Modules\PaymentScheduleTemplates\Models\PaymentScheduleTemplate;

class EditPaymentScheduleTemplate extends PaymentScheduleTemplateView
{
    public function paymentScheduleTemplate(): PaymentScheduleTemplate
    {
        return $this->translator->translate($this->request->paymentScheduleTemplate);
    }

    public function mappedPaymentScheduleTemplate(): array
    {
        $paymentScheduleTemplate = $this->paymentScheduleTemplate;

        $translated = collect($this->supportedLanguages())
            ->mapWithKeys(fn($language) => [$language => ['name' => lang($paymentScheduleTemplate, 'name', $language)]])
            ->toArray();

        return [
            'id' => $paymentScheduleTemplate->id,
            'translated' => VueFormatter::formatTranslated($translated),
            'payment_method_id' => $paymentScheduleTemplate->payment_method_id,
            'payments' => $paymentScheduleTemplate->payments,
        ];
    }
}
