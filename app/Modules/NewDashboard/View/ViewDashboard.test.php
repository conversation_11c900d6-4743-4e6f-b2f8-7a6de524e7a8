<?php

namespace AwardForce\Modules\NewDashboard\View;

use AwardForce\Modules\NewDashboard\DataObjects\Dashboards;
use AwardForce\Modules\NewDashboard\Enums\DashboardProvider;
use <PERSON>\Uuid\Uuid;
use <PERSON>\Uuid\UuidInterface;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Factory\MuffinFactory;

class ViewDashboardTest extends BaseTestCase
{
    use Database,
        Laravel,
        MuffinFactory;

    private ChildViewDashboard $view;

    protected function init(): void
    {
        $this->view = app(ChildViewDashboard::class);
    }

    public function testComponentNameReturnsCorrectName(): void
    {
        $this->assertEquals('dashboards.embeddable', app(ChildViewDashboard::class)->component());
    }

    public function testDashboardLinksConcatenatesDefaultAndCustomLinks(): void
    {
        $this->assertEquals([
            [
                'link' => 'dashboard-new/default-dashboard',
                'name' => 'Default',
            ],
            [
                'link' => 'dashboard-new/custom-dashboard',
                'name' => 'Custom Dashboard',
            ],
        ], $this->view->dashboardLinks());
    }
}

class ChildViewDashboard extends ViewDashboard
{
    public function activeDashboardLink(): string
    {
        return 'test';
    }

    protected function provider(): DashboardProvider
    {
        return DashboardProvider::Embeddable;
    }

    public function dashboardId(): UuidInterface
    {
        return Uuid::fromString(fake()->uuid());
    }

    protected function customDashboardLinks(): Dashboards
    {
        return Dashboards::make([
            [
                'link' => 'dashboard-new/custom-dashboard',
                'name' => 'Custom Dashboard',
            ],
        ]);
    }

    protected function defaultDashboardLink(): array
    {
        return [
            'link' => 'dashboard-new/default-dashboard',
            'name' => 'Default',
        ];
    }
}
