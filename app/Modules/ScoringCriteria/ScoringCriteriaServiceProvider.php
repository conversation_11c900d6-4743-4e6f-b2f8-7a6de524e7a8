<?php

namespace AwardForce\Modules\ScoringCriteria;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\ScoringCriteria\Repositories\EloquentScoringCriterionRepository;
use AwardForce\Modules\ScoringCriteria\Repositories\ScoringCriterionRepository;

class ScoringCriteriaServiceProvider extends ServiceProvider
{
    /**
     * @var bool
     */
    public $defer = true;

    /**
     * An array of the listeners that need to be registered with the system. The key should
     * refer to the event that is fired, and the value should be the class name and method
     * that will handle that event.
     *
     * @var array
     */
    protected $listeners = [
    ];

    /**
     * The repository bindings for the Entries module.
     *
     * @var array
     */
    protected $repositories = [
        ScoringCriterionRepository::class => EloquentScoringCriterionRepository::class,
    ];

    protected $files = [
        __DIR__.'/macros.php',
        __DIR__.'/validators.php',
    ];
}
