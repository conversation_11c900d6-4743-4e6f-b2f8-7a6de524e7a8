<?php

namespace AwardForce\Modules\PaymentMethods\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\PaymentMethods\Models\PaymentMethod;
use Platform\Database\Eloquent\Collection;

class EloquentPaymentMethodsRepository extends Repository implements PaymentMethodsRepository
{
    public function __construct(PaymentMethod $paymentMethod)
    {
        $this->model = $paymentMethod;
    }

    public function getWithTrashed(): Collection
    {
        return $this->getQuery()->withTrashed()->get();
    }
}
