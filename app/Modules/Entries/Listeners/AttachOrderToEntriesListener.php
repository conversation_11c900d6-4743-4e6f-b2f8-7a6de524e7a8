<?php

namespace AwardForce\Modules\Entries\Listeners;

use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasCreated;
use AwardForce\Modules\Entries\Commands\AttachOrderToEntryCommand;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use Illuminate\Foundation\Bus\DispatchesJobs;

class AttachOrderToEntriesListener
{
    use DispatchesJobs;

    /**
     * @var EntryRepository
     */
    protected $entries;

    public function __construct(EntryRepository $entries)
    {
        $this->entries = $entries;
    }

    public function handle(OrderWasCreated $event)
    {
        foreach ($event->order->orderItems as $orderItem) {
            if (($orderItem->type() == 'entry') && ($orderItem->entry_id)) {
                $entry = $this->entries->getById($orderItem->entry_id);
                $this->dispatch(new AttachOrderToEntryCommand($entry, $event->order));
            }
        }
    }
}
