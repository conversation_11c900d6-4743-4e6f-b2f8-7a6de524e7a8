<?php

namespace AwardForce\Modules\Entries\Search\Columns;

use AwardForce\Library\Search\Columns\TranslatedColumnWithFallback;
use AwardForce\Library\Search\Traits\StrictDefaultLanguage;
use AwardForce\Modules\Api\V2\Services\ApiFieldTransformer;
use AwardForce\Modules\Grants\Search\Filters\GrantStatusFilter;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Columns\HasEagerLoadedRelations;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilterCollection;

class GrantStatus extends TranslatedColumnWithFallback implements ApiColumn, HasEagerLoadedRelations
{
    use ApiFieldTransformer;
    use StrictDefaultLanguage;

    /** @var bool */
    private $grantsMode;

    public function __construct($grantsMode = true)
    {
        $this->grantsMode = $grantsMode;
    }

    /**
     * {@inheritDoc}
     */
    public function fieldName(): string
    {
        return 'name';
    }

    /**
     * {@inheritDoc}
     */
    public function title()
    {
        return trans('grants.titles.status');
    }

    /**
     * {@inheritDoc}
     */
    public function name(): string
    {
        return 'grant_status';
    }

    /**
     * {@inheritDoc}
     */
    public function dependencies(): Collection
    {
        return collect([
            GrantStatusFilter::class,
        ]);
    }

    /**
     * {@inheritDoc}
     */
    public function html($record)
    {
        return new HtmlString(view('partials.list-actions.grant-status-selector', [
            'selected' => $record->id,
            'selectedStatus' => $record->grantStatus ? $record->grantStatus->slug : '',
            'disabled' => ! is_null($record->deletedAt),
            'grantStatusSelectorMode' => 'single',
        ])->render());
    }

    /**
     * {@inheritDoc}
     */
    public function default(): Defaults
    {
        return new Defaults($this->grantsMode ? 'all' : 'search');
    }

    /**
     * {@inheritDoc}
     */
    public function visible(): bool
    {
        return feature_enabled('grants') && \Consumer::can('view', 'Grants');
    }

    /**
     * {@inheritDoc}
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * {@inheritDoc}
     */
    public function priority(): int
    {
        return 10;
    }

    /**
     * {@inheritDoc}
     */
    public function sortable(): bool
    {
        return true;
    }

    public function apiName()
    {
        return 'grant_status';
    }

    public function apiValue($record)
    {
        return $this->fetchApiRelatedResource($record, 'grantStatus', 'grant-status');
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility(feature_enabled('grants') ? 'all' : 'none');
    }

    public function relations(): IncludeFilterCollection
    {
        return (new IncludeFilterCollection)
            ->addIncludeFilter('grantStatus');
    }
}
