<?php

namespace AwardForce\Modules\Entries\Search\Filters;

use AwardForce\Library\Search\Filters\FormFilter;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class ContributorEntryFilter extends FormFilter implements ColumnatorFilter, SearchFilter
{
    public function __construct(protected array $input)
    {
        parent::__construct($input);
    }

    /**
     * Determines if this dependency applies to the search in any way.
     */
    public function applies(): bool
    {
        return true;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        return $query->join('entries', 'contributors.submittable_id', '=', 'entries.id')
            ->join('tabs', 'tabs.id', 'contributors.tab_id')
            ->join('forms', 'forms.id', 'tabs.form_id')
            ->whereNull('forms.deleted_at')
            ->when(! $this->viewingAll(), function ($query) {
                $query->where('forms.id', $this->formId());
            });
    }
}
