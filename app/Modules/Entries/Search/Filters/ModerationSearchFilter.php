<?php

namespace AwardForce\Modules\Entries\Search\Filters;

use AwardForce\Modules\Entries\Models\Entry;
use Illuminate\Contracts\Support\Htmlable;
use Platform\Search\ApiVisibility;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\Services\SearchFilterValidator;

class ModerationSearchFilter implements ColumnatorFilter, Htmlable, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    /** @var array */
    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! empty($this->input['moderation'])) {
            if ($this->input['moderation'] == 'not-rejected') {
                $query = $query->where('moderation_status', '!=', Entry::MODERATION_STATUS_REJECTED);
            } else {
                $query = $query->where('moderation_status', '=', $this->input['moderation']);
            }
        }

        return $query;
    }

    /**
     * Determines if this dependency applies to the search in any way.
     */
    public function applies(): bool
    {
        return $this->input['moderation'] ?? false;
    }

    /**
     * Get content as a string of HTML.
     *
     * @return string
     */
    public function toHtml()
    {
        $options = [
            '' => '',
            'approved' => trans('entries.moderation.status.approved'),
            'undecided' => trans('entries.moderation.status.undecided'),
            'rejected' => trans('entries.moderation.status.rejected'),
        ];

        return view('entry.manager.search.filters.moderation-status', ['options' => $options]);
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $values = ['approved', 'undecided', 'rejected'];
        $this->validateValueInArray($filterName, $filterValue, $values);
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['moderation']);
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
