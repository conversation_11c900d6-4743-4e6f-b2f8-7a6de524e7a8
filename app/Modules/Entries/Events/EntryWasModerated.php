<?php

namespace AwardForce\Modules\Entries\Events;

use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Notifications\Services\TaggableEvent;
use AwardForce\Modules\Webhooks\Contracts\TriggersWebhooks;

class EntryWasModerated extends EntryWasUpdated implements TaggableEvent, TriggersWebhooks
{
    public function models(): array
    {
        return [$this->entry];
    }

    public function actions(): array
    {
        return [config('taggable.entry_moderated.moderation_'.$this->entry->moderationStatus)];
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('entry'),
            'updated',
            'audit.entry.moderated',
            $this->entry,
            $this->entry->id,
            (string) $this->entry->slug,
        );
    }
}
