<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Support\Arr;

class ApiEntryFieldValueService
{
    public function __construct(
        protected Field $field,
        protected array $existingValues,
        protected $requestValue
    ) {
    }

    public function getValue()
    {
        return $this->field->isTable()
            ? $this->getTableValues()
            : $this->requestValue;
    }

    private function getTableValues()
    {
        $tempTableFieldValues = Arr::get($this->existingValues, (string) $this->field->slug, []);

        $tempTableFieldValues = is_array($tempTableFieldValues) ? $tempTableFieldValues : [];

        $this->requestValue = json_decode($this->requestValue, true) ?? [];

        $mergedValues = array_merge_recursive_distinct($tempTableFieldValues, $this->requestValue);

        Arr::set($tempTableFieldValues, 'values', $this->getCleanTableValues($mergedValues));
        Arr::set($tempTableFieldValues, 'dynamicRows', Arr::get($mergedValues, 'dynamicRows', []));

        return $tempTableFieldValues;
    }

    private function getCleanTableValues(array $mergedValues): array
    {
        return collect(Arr::get($mergedValues, 'values', []))
            ->mapWithKeys(function (array $columnRowValues, string $columnRow) {
                $requestTableFieldColumns = Arr::get($this->requestValue, "values.{$columnRow}", []);

                // We add the new cell values
                collect($requestTableFieldColumns)
                    ->each(fn($cellValue, $cellRow) => Arr::set($columnRowValues, $cellRow, $cellValue));

                // we remove the null-ish cell values
                collect($columnRowValues)
                    ->each(function ($cellValue, $cellRow) use ($requestTableFieldColumns, &$columnRowValues) {
                        $this->removeNullCellValues($cellRow, $requestTableFieldColumns, $columnRowValues);
                    });

                return [
                    $columnRow => $columnRowValues,
                ];
            })
            ->toArray();
    }

    private function removeNullCellValues($cellRow, $requestTableFieldColumns, &$columnRowValues): void
    {
        if (in_array($cellRow, array_keys($requestTableFieldColumns))) {
            $updatedValue = Arr::get($requestTableFieldColumns, $cellRow);
            if (empty($updatedValue)) {
                unset($columnRowValues[$cellRow]);
            } else {
                Arr::set($columnRowValues, $cellRow, $updatedValue);
            }
        }
    }
}
