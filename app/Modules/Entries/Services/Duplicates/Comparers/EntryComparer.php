<?php

namespace AwardForce\Modules\Entries\Services\Duplicates\Comparers;

use AwardForce\Modules\Entries\Models\Entry;

interface EntryComparer
{
    /**
     * Calculates the similarity percentage of the given entries.
     *
     * @return mixed
     */
    public function compare(Entry $entry1, Entry $entry2): float;

    /**
     * Determine if changes in the given entry make it out of date using this comparison rule.
     */
    public function outOfDate(Entry $entry): bool;
}
