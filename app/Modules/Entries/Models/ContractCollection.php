<?php

namespace AwardForce\Modules\Entries\Models;

use Consumer;
use Platform\Database\Eloquent\Collection;
use Spatie\Html\Facades\Html;

class ContractCollection extends Collection
{
    public function contracts(): array
    {
        return $this->validContracts()->map(function (Contract $contract) {
            return [
                'id' => $contract->id,
                'slug' => (string) $contract->slug,
                'title' => $contract->signedAt ? $contract->title :
                    lang($contract->contentBlock, 'title'),
                'contentBlockId' => $contract->contentBlock ? $contract->contentBlock->id : null,
                'signedAt' => (string) HTML::dateTimezone(
                    $contract->signedAt,
                    setting('timezone'),
                    Consumer::dateLocale()
                ),
            ];
        })
            ->sortBy('title')
            ->values()
            ->all();
    }

    public function validContracts(): ContractCollection
    {
        return $this->filter(function (Contract $contract) {
            return $contract->isSigned() || $contract->contentBlock;
        });
    }
}
