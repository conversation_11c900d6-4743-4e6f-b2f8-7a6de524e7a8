<?php

namespace AwardForce\Modules\Authentication\Redirection\Redirectors;

use AwardForce\Library\Authorization\Manager;

class ProfileRedirector implements Redirector
{
    /**
     * Determines if the conditions for using this redirector have been met.
     */
    public function canRedirect(Manager $consumer): bool
    {
        return true;
    }

    /**
     * Returns the route where the user should be redirected to.
     */
    public function route(): string
    {
        return route('profile.show');
    }
}
