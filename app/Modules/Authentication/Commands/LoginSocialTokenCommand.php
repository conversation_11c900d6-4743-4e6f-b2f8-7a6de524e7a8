<?php

namespace AwardForce\Modules\Authentication\Commands;

class LoginSocialTokenCommand
{
    /*** @var string */
    public $token;

    /*** @var bool */
    public $allowRegistration;

    /*** @var bool */
    public $assignRoleOnRegister;

    /**
     * @param  string  $token
     * @param  bool  $allowRegistration
     * @param  bool  $assignRoleOnRegister
     */
    public function __construct($token, $allowRegistration = true, $assignRoleOnRegister = true)
    {
        $this->token = $token;
        $this->allowRegistration = $allowRegistration;
        $this->assignRoleOnRegister = $assignRoleOnRegister;
    }
}
