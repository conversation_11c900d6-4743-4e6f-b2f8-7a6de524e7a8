<?php

namespace AwardForce\Modules\Authentication\Commands;

use AwardForce\Library\Authentication\Authenticator;
use AwardForce\Modules\Authentication\Tokens\SocialAuthToken;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\UserFactory;
use AwardForce\Modules\Identity\Users\Services\GlobalCommunicationChannelService;
use Crypt;
use Illuminate\Support\Facades\Session;
use Platform\Events\EventDispatcher;
use Platform\Tokens\TokenManager;

class LoginSocialTokenCommandHandler
{
    use EventDispatcher;

    public function __construct(
        private UserFactory $factory,
        private UserRepository $users,
        private Authenticator $authenticator,
        private TokenManager $tokens,
        private GlobalCommunicationChannelService $channels)
    {
    }

    public function handle(LoginSocialTokenCommand $command)
    {
        /** @var SocialAuthToken $token */
        $token = $this->tokens->pull($command->token, SocialAuthToken::class);

        $user = $this->getUser($token->firstName, $token->lastName, $token->email, $token->globalUserId);

        $this->authenticator->loginSocial($user, $command->allowRegistration, $command->assignRoleOnRegister);

        Session::put('authentication.freshLogin', true);
        Session::put('authentication.social_login', [
            'token' => Crypt::encrypt($token),
            'last_checked_at' => now()->timestamp,
        ]);

        $this->dispatch($user->releaseEvents());

        return $user;
    }

    private function getUser($firstName, $lastName, $email, $globalUserId = null)
    {
        if (! $globalUserId) {
            return $this->createUser($firstName, $lastName, $email);
        }

        return $this->factory->requireUserByGlobalId($globalUserId);
    }

    private function createUser(string $firstName, string $lastName, string $email)
    {
        $user = $this->factory->addFromSocial($firstName, $lastName, $email);

        $this->users->save($user);

        $this->channels->createChannelsAndConfirm($user, false);

        $this->dispatch($user->releaseEvents());

        return $user;
    }
}
