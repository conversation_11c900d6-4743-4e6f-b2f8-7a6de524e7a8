<?php

namespace AwardForce\Modules\Authentication\Views;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Authentication\Authenticators\Authenticator;
use Illuminate\Http\Request;
use Platform\View\View;

class AuthenticatorsChallenge extends View
{
    /**
     * @var Request
     */
    private $request;

    /**
     * @var \AwardForce\Modules\Authentication\Authenticators\Authenticator
     */
    private $authenticator;

    public function __construct(Request $request, Manager $manager)
    {
        $this->request = $request;
        $this->authenticator = new Authenticator($manager->user());
        VueData::registerTranslations([
            'buttons.continue',
            'buttons.cancel',
            'auth.authenticator.challenge.use.backup',
            'auth.authenticator.challenge.use.code',
            'auth.authenticator.challenge.send-message',
        ]);
    }

    public function hasMobile(): bool
    {
        return $this->authenticator->registered('mobile');
    }

    public function message()
    {
        $sent = $this->request->get('sent');

        return $sent ? trans('auth.authenticator.'.$sent.'.start') : null;
    }

    public function verificationRoute()
    {
        return route('authenticator.verify');
    }
}
