<?php

namespace AwardForce\Provisioning\Data;

use Platform\Database\Eloquent\Repository;

class EloquentDomainRepository extends Repository implements DomainRepository
{
    public function __construct(Domain $model)
    {
        $this->setModel($model);
    }

    /**
     * Returns a new query object that can be used.
     *
     * @return mixed
     */
    protected function getQuery()
    {
        return $this->getModel()->newQuery();
    }
}
