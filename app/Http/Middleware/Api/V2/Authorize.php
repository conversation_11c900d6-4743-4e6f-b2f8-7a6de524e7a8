<?php

namespace AwardForce\Http\Middleware\Api\V2;

use AwardForce\Library\Authorization\ApiConsumer;
use AwardForce\Library\Authorization\Consumer as AuthConsumer;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Api\V2\Contracts\ApiKeyRepository;
use AwardForce\Modules\Api\V2\Exceptions\ApiUnsupportedLanguage;
use AwardForce\Modules\Api\V2\Services\ApiRequestUtils;
use Closure;
use Dingo\Api\Routing\Helpers;
use Illuminate\Support\Facades\Log;

class Authorize
{
    use ApiRequestUtils;
    use Helpers;

    /** @var AccountRepository */
    private $accounts;

    /** @var ApiKeyRepository */
    private $apiKeys;

    public function __construct(AccountRepository $accounts, ApiKeyRepository $apiKeys)
    {
        $this->accounts = $accounts;
        $this->apiKeys = $apiKeys;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     *
     * @throws ApiUnsupportedLanguage
     */
    public function handle($request, Closure $next)
    {
        $headerApiKey = $this->apiKeyFromHeader($request);
        $apiKey = $this->apiKeys->getApiKeyBySlug($this->apiKeySlugFromHeader($headerApiKey));

        if ($apiKey->readable() && ! $request->isMethod('get')) {
            Log::info('Api authorization error: Insufficient scope');

            $this->response()->errorForbidden('API key provided does not have sufficient scope to access this resource.');
        }

        $account = $this->accounts->getById($apiKey->accountId);

        if ($account->suspended) {
            Log::info('Api authorization error: Account suspended');

            $this->response()->errorForbidden('Account is suspended.');
        }

        CurrentAccount::set(translate($apiKey->account));

        if (! feature_enabled('api')) {
            Log::info('Api authorization error: Feature not enabled');

            $this->response()->errorForbidden('API feature not enabled for this account.');
        }

        AuthConsumer::set(new ApiConsumer($this->apiLanguagesFromHeader($request), $apiKey));

        Log::info('API Authorization success: API Key - '.$headerApiKey.', HTTP Method: '.$request->method());

        return $next($request);
    }
}
