<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Modules\Menu\Events\SelectedContextWasUpdated;
use AwardForce\Modules\Menu\Services\ContextService;
use Illuminate\Http\Request;
use Platform\Events\EventDispatcher;
use Platform\Http\Controller;

class ContextMenuController extends Controller
{
    use EventDispatcher;

    /**
     * Store the user selected context in the session
     *
     * @param  Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateSelectedContext(string $context, ContextService $contextService)
    {
        if ($contextService->selectedContext() !== $context) {
            $this->dispatch(new SelectedContextWasUpdated);
        }

        $contextService->setSelectedContext($context);

        return response()->noContent();
    }
}
