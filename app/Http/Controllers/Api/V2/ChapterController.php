<?php

namespace AwardForce\Http\Controllers\Api\V2;

use AwardForce\Http\Requests\Api\V2\Chapter\CreateChapterApiRequest;
use AwardForce\Http\Requests\Api\V2\Chapter\UpdateChapterApiRequest;
use AwardForce\Http\Requests\Api\V2\File\FileUploadApiRequest;
use AwardForce\Modules\Chapters\Commands\AddChapter;
use AwardForce\Modules\Chapters\Commands\DeleteChapters;
use AwardForce\Modules\Chapters\Commands\EditChapter;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Exceptions\ChapterQuantityLimitReachedException;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Http\Request;
use Platform\Search\ApiVisibility;

class ChapterController extends ApiController
{
    public function create(CreateChapterApiRequest $request)
    {
        try {
            $chapter = $this->dispatchSync(AddChapter::fromApi(
                $request->get('translated'),
                id_from_slug($request->get('season'), app(SeasonRepository::class)),
                ids_from_slugs($request->get('managers', []), app(UserRepository::class)),
                strtolower($request->get('status', 'active')) == 'active',
                $request->get('max_image_width')
            ));
        } catch (ChapterQuantityLimitReachedException $e) {
            $this->response()->error('The chapter quantity limit has been reached in this account.', 422);
        }

        return $this->resourceResponse((string) $chapter->slug, 'chapter');
    }

    public function update(UpdateChapterApiRequest $request)
    {
        $this->dispatch(
            new EditChapter(
                ($chapter = $this->resource($request->slug)),
                $request->get('translated', []),
                ids_from_slugs($request->get('managers', null), app(UserRepository::class)),
                $request->get('status') ? $request->get('status') == 'active' : null,
                $request->get('max_image_width')
            )
        );

        return $this->resourceResponse($request->slug);
    }

    public function delete(Request $request)
    {
        $this->dispatch(new DeleteChapters([$this->resourceId($request->slug)]));

        return $this->response()->noContent();
    }

    public function upload(FileUploadApiRequest $request)
    {
        $file = $this->processFile($request, $this->resourceId($request->slug), 'Chapters');

        return $this->newFileResponse($file->token);
    }

    public function columnator(array $parameters, ApiVisibility $apiVisibility)
    {
        return $this->columnatorFactory->forApi('chapter.search', $apiVisibility, $parameters);
    }

    public function repository()
    {
        $this->repository = app(ChapterRepository::class);
    }
}
