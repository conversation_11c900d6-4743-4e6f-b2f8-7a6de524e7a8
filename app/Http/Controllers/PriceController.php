<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Payment\AddPriceRequest;
use AwardForce\Http\Requests\Payment\UpdatePriceRequest;
use AwardForce\Modules\Payments\Commands\AddPriceCommand;
use AwardForce\Modules\Payments\Commands\DeletePriceCommand;
use AwardForce\Modules\Payments\Commands\UpdatePriceCommand;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Repositories\PriceRepository;
use AwardForce\Modules\Payments\Services\PriceAmountInputTransformer;
use AwardForce\Modules\Payments\View\Prices;
use AwardForce\Modules\Payments\View\PriceView;
use AwardForce\Modules\Tags\Commands\TagModelCommand;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Platform\Http\Controller;

class PriceController extends Controller
{
    use DispatchesJobs;

    public static $resource = 'Settings';

    /**
     * @var PriceRepository
     */
    private $prices;

    public function __construct(PriceRepository $prices)
    {
        $this->prices = $prices;
    }

    /**
     * Render the list view with prices.
     *
     * @return mixed
     */
    public function index(Prices $view)
    {
        return $this->respond('price.index', $view);
    }

    /**
     * Render the form for a new price.
     *
     * @return mixed
     */
    public function getNew(PriceView $view)
    {
        return $this->respond('price.new', $view);
    }

    /**
     * @return mixed
     */
    public function copy(PriceView $view)
    {
        return $this->respond('price.copy', $view);
    }

    /**
     * Handle storing of a new price
     *
     * @return mixed
     */
    public function create(AddPriceRequest $request, PriceAmountInputTransformer $transformer)
    {
        $transformedPriceInput = $transformer->transform(
            $request->get('amount', []),
            $request->get('defaultAmount', [])
        );

        $price = $this->dispatchSync(new AddPriceCommand(
            $request->get('seasonId'),
            $request->get('translated'),
            $request->get('type'),
            $request->get('default', false),
            $request->get('selectable', false),
            $request->get('memberNumberRequired', false),
            $transformedPriceInput
        ));

        $this->dispatch(new TagModelCommand($price, $request->get('tags')));

        return redirect()->route('price');
    }

    /**
     * Retrieve a single price.
     *
     * @return mixed
     */
    public function show(PriceView $view)
    {
        return $this->respond('price.edit', $view);
    }

    /**
     * Update a single price.
     *
     * @return mixed
     */
    public function update(UpdatePriceRequest $request, PriceAmountInputTransformer $transformer)
    {
        $transformedPriceInput = $transformer->transform(
            $request->get('amount', []),
            $request->get('defaultAmount', [])
        );

        $this->dispatch(new UpdatePriceCommand(
            $request->price->id,
            $request->get('translated'),
            $request->get('type'),
            $request->get('default', false),
            $request->get('selectable', false),
            $request->get('memberNumberRequired', false),
            $request->price->seasonId,
            $transformedPriceInput
        ));

        $this->dispatch(new TagModelCommand($request->price, $request->get('tags')));

        return redirect()->route('price');
    }

    /**
     * Delete price.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function delete(Request $request)
    {
        $this->dispatch(new DeletePriceCommand($request->price));

        return redirect()->route('price');
    }
}
