<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Content\UpdateContentRequest;
use AwardForce\Http\Requests\Content\ViewContentRequest;
use AwardForce\Http\Requests\Term\EditTermRequest;
use AwardForce\Modules\Content\Terms\Commands\EditTermCommand;
use AwardForce\Modules\Content\Terms\Facades\Term;
use AwardForce\Modules\Content\Terms\Services\TermsService;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Platform\Http\Controller;

class TermController extends Controller
{
    use DispatchesJobs;

    /**
     * @var TermsService
     */
    protected $contentTerms;

    public static $resource = 'Content';

    /**
     * Constructor
     */
    public function __construct(TermsService $contentTerms)
    {
        $this->contentTerms = $contentTerms;
    }

    /**
     * Retrieve the list of content terms.
     *
     * @return mixed
     */
    public function index(ViewContentRequest $request)
    {
        $terms = $this->contentTerms->getAll()->sortBy('key');

        return $this->respond('term.index', compact('terms'));
    }

    /**
     * Retrieve the list of content terms.
     *
     * @return mixed
     */
    public function edit(EditTermRequest $request, $term)
    {
        $term = $this->contentTerms->get($term->key);

        return $this->respond('term.edit', compact('term'));
    }

    /**
     * Saves the changes to the Term.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(UpdateContentRequest $request, $term)
    {
        $this->dispatch(new EditTermCommand($term, $request->get('translated')));

        return redirect()->route('term.index');
    }
}
