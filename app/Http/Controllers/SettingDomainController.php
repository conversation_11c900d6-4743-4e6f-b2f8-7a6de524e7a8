<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Setting\UpdateDomainRequest;
use AwardForce\Modules\Accounts\Commands\AccountVanityDomainPingCommand;
use AwardForce\Modules\Accounts\Commands\CheckCustomDomainCertificateCommand;
use AwardForce\Modules\Accounts\Commands\CheckCustomDomainDnsCommand;
use AwardForce\Modules\Settings\Commands\UpdateDomainsCommand;
use AwardForce\Modules\Settings\View\EditDomain;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Platform\Http\Controller;

class SettingDomainController extends Controller
{
    use DispatchesJobs;

    public static $resource = 'Settings';

    public function edit(EditDomain $view)
    {
        return $this->respond('setting.domain', $view);
    }

    public function status(EditDomain $view): array
    {
        return ['status' => $view->status()];
    }

    public function recheck()
    {
        $custom = current_account()->customDomain();

        if (! $custom) {
            return [];
        } elseif ($custom->waitingforDns() || $custom->customFailed()) {
            $this->dispatch(new CheckCustomDomainDnsCommand($custom));
        } elseif ($custom->waitingforCertificate()) {
            $this->dispatch(new CheckCustomDomainCertificateCommand($custom));
        }

        return [];
    }

    public function update(UpdateDomainRequest $request)
    {
        $account = $request->account();
        $vanity = $request->vanity();
        if (feature_disabled('custom_domain')) {
            $this->dispatch(new UpdateDomainsCommand($account, $vanity));
            $this->dispatch(new AccountVanityDomainPingCommand($account));

            return $request->getHost() == $vanity ? []
                : ['redirect' => '//'.vanity_domain_setup_url($vanity)];
        }

        $this->dispatch(new UpdateDomainsCommand(
            $account,
            $vanity,
            $custom = $request->custom(),
            $redirect = (bool) $request->get('redirect')
        ));

        $default = $account->defaultDomain()->domain;
        $domains = $redirect ? [$default] : [$vanity, $custom];
        $this->dispatch(new AccountVanityDomainPingCommand($account));

        return in_array($request->getHost(), $domains)
            ? ['status' => app(EditDomain::class)->status()]
            : ['redirect' => '//'.vanity_domain_setup_url($vanity)];
    }
}
