<?php

namespace AwardForce\Http\Requests\ScoreSet;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\ScoreSets\Enums\Layout;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Illuminate\Validation\Rules\Enum;

class UpdateScoreSetRequest extends FormRequest
{
    public function rules()
    {
        $modes = array_intersect(config('awardforce.judging.modes'), current_account()->judgingModes());

        if ($this->isGalleryMode()) {
            $galleryBlocks = app(ContentBlockRepository::class)->getAllByKey('gallery-info')->implode('id', ',');
        } elseif ($this->isVotingMode()) {
            $votingBlocks = app(ContentBlockRepository::class)->getAllByKey('voting-home')->implode('id', ',');
        } elseif ($this->isVipJudgingMode()) {
            $vipJudgingBlocks = app(ContentBlockRepository::class)->getAllByKey('judges-home')->implode('id', ',');
        } elseif ($this->isTopPickMode()) {
            $topPickBlocks = app(ContentBlockRepository::class)->getAllByKey('top-pick-home')->implode('id', ',');
        } elseif ($this->isQualifyMode()) {
            $qualifyBlocks = app(ContentBlockRepository::class)->getAllByKey('qualifying-home')->implode('id', ',');
        }

        return [
            'translated' => 'required|translation_required:name',
            'maxVotesPerEntry' => 'sometimes|numeric|min:1',
            'maxVotesPerUser' => 'sometimes|numeric|min:1',
            'maxVotesPerCategory' => 'sometimes|numeric|min:1',
            'minResponsesForConsensus' => 'required_if:mode,qualifying|numeric|min:1',
            'minPercentageToQualify' => 'required_if:mode,qualifying|numeric|min:0|max:100',
            'mode' => 'required|in:'.implode(',', $modes),
            'topPickWinners' => 'required_if:mode,top_pick|numeric|min:1',
            'topPickPreferences' => 'required_if:mode,top_pick|numeric|min:1',
            'topPickMode' => 'required_if:mode,top_pick|in:categories,all',
            'order' => 'numeric|min:0',
            'galleryContentBlockId' => 'numeric|in:'.($galleryBlocks ?? '0'),
            'votingContentBlockId' => 'numeric|in:'.($votingBlocks ?? '0'),
            'vipJudgingContentBlockId' => 'numeric|in:'.($vipJudgingBlocks ?? '0'),
            'topPickContentBlockId' => 'numeric|in:'.($topPickBlocks ?? '0'),
            'qualifyContentBlockId' => 'numeric|in:'.($qualifyBlocks ?? '0'),
            'searchFields' => 'array|max:'.ScoreSet::MAX_SEARCH_FIELDS,
            'layout' => ['required', new Enum(Layout::class)],
        ];
    }

    /**
     * @return bool
     */
    protected function isGalleryMode()
    {
        return $this->get('mode') === ScoreSet::MODE_GALLERY;
    }

    /**
     * @return bool
     */
    protected function isVotingMode()
    {
        return $this->get('mode') === ScoreSet::MODE_VOTING;
    }

    /**
     * @return bool
     */
    protected function isVipJudgingMode()
    {
        return $this->get('mode') === ScoreSet::MODE_VIP;
    }

    /**
     * @return bool
     */
    protected function isTopPickMode()
    {
        return $this->get('mode') === ScoreSet::MODE_TOP_PICK;
    }

    /**
     * @return bool
     */
    protected function isQualifyMode()
    {
        return $this->get('mode') === ScoreSet::MODE_QUALIFYING;
    }
}
