<?php

namespace AwardForce\Http\Requests\Api\V2\Contributor;

use AwardForce\Http\Requests\Api\V2\ApiRequest;
use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Entries\Contracts\ContributorRepository;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\ContributorField;
use AwardForce\Modules\Forms\Collaboration\Validation\CurrentlyLocked;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Http\Validation\FieldValidator;
use Illuminate\Validation\Rule;

class UpdateContributorField extends ApiRequest
{
    public function rules(): array
    {
        $this->merge(['fieldSlug' => $this->fieldSlug]);

        $contributor = app(ContributorRepository::class)->requireBySlug($this->slug);
        $field = app(FieldRepository::class)->requireBySlug($this->fieldSlug);

        return [
            'bail',
            'value' => array_merge(['required'], app(FieldValidator::class)->getValidation($field)['rules']['values.'.$field->slug]),
            'fieldSlug' => Rule::when($field->form->supportsRealTimeUpdates(), [
                new CurrentlyLocked(
                    app(Database::class),
                    new ContributorField($contributor, $field)
                ),
            ]),
        ];
    }
}
