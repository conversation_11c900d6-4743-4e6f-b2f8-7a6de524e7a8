<?php

namespace AwardForce\Http\Requests\Api\V2\Fund;

use AwardForce\Http\Requests\Api\V2\ApiRequest;
use AwardForce\Modules\Api\V2\Rules\AttributeNames;

class CreateFundApiRequest extends ApiRequest
{
    protected $attributeNames = [
        'translated',
        'currency',
        'budget',
    ];

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $currencies = implode(',', array_keys(config('currencies')));

        return [
            '*' => new AttributeNames($this->attributeNames),
            'translated' => [
                'required',
                'array',
                function ($attribute, $value, $fail) {
                    $this->validateRequestBodyLanguageCodes($value, $fail);
                },
            ],
            'translated.name' => 'sometimes|array',
            'translated.description' => 'sometimes|array',
            'currency' => 'required|in:'.$currencies,
            'budget' => 'required|numeric',
        ];
    }
}
