<?php

namespace AwardForce\Http\Requests\Cart;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Http\FormRequest;

class PayTagFee extends FormRequest
{
    public function authorize(): bool
    {
        if (! Consumer::isUser()) {
            return false;
        }

        $user = Consumer::get()->user();
        $entry = $this->route('entry');
        $price = $this->route('price');

        return $user->started($entry) && $price->hasAnyMatchingTags(...$entry->tags->just('id'));
    }

    public function rules(): array
    {
        return [];
    }
}
