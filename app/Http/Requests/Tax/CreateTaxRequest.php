<?php

namespace AwardForce\Http\Requests\Tax;

use AwardForce\Library\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Unique;

class CreateTaxRequest extends FormRequest
{
    /**
     * @return array
     */
    public function messages()
    {
        return [
            'country.unique' => trans('payments.taxes.validation.country.unique'),
            'name.unique' => trans('payments.taxes.validation.name.unique'),
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $accountId = current_account_id();

        return [
            'country' => [
                'required',
                Rule::unique('taxes', 'country')
                    ->where('account_id', $accountId)
                    ->when(
                        $this->region,
                        fn(Unique $rule) => $rule->where('region', $this->region)
                    ),
            ],
            'rate' => 'required|numeric|min:0|max:100',
            'term' => 'required',
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('taxes', 'name')
                    ->where('account_id', $accountId),
            ],
            'vat' => 'nullable|boolean',
            'exemptFromVat' => 'nullable|boolean',
        ];
    }
}
