<?php

namespace AwardForce\Http\Requests\Entry\Feedback;

use AwardForce\Library\Authorization\LogsAuthRequests;
use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Consumer;
use Illuminate\Support\Collection;

class ViewRequest extends FormRequest
{
    use LogsAuthRequests;

    /**
     * @return bool
     */
    public function authorize()
    {
        $entry = $this->entry;
        $rounds = app(RoundRepository::class)->getActiveFeedbackRounds($entry->chapterId, $entry->categoryId);

        // Entrants
        if ($this->entrantAllowed($entry, $rounds)) {
            return true;
        }

        // Judges
        if ($this->judgeAllowed($entry, $rounds)) {
            return true;
        }

        // All permissions
        return Consumer::can('view', 'EntriesAll') && Consumer::can('view', 'ScoresAll');
    }

    /**
     * @return array
     */
    public function rules()
    {
        return [];
    }

    /**
     * Returns true if the current consumer is the entrant of the requested entry,
     * and has access to view entry feedback.
     *
     * @return bool
     */
    private function entrantAllowed(Entry $entry, Collection $rounds)
    {
        if (! $rounds->max('entrantVisibility')) {
            return false;
        }

        if (! Consumer::can('view', 'EntriesOwner')) {
            return false;
        }

        return Consumer::get()->id() == $entry->userId;
    }

    /**
     * Returns true if the judge is allowed to view feedback on the entry.
     *
     * @return bool
     */
    private function judgeAllowed(Entry $entry, Collection $rounds)
    {
        if (! $rounds->max('judgeVisibility')) {
            return false;
        }

        return app(CurrentAssignments::class)->judgeHasEntry(
            ScoreSet::MODE_VIP,
            new AssignmentUser(Consumer::get()),
            $entry->id
        );
    }

    /**
     * Get the response for a forbidden operation.
     *
     * @return \Illuminate\Http\Response
     */
    public function forbiddenResponse()
    {
        $this->log('forbidden', 'view', 'feedback');
        abort(404);
    }

    /**
     * The layer simply defines where exactly the log was executed from.
     *
     * @return string
     */
    protected function layer()
    {
        return 'feedback view request';
    }
}
