<?php

namespace AwardForce\Http\Requests\Theme;

use AwardForce\Library\Http\FormRequest;

class UpdateThemeRequest extends FormRequest
{
    public function rules()
    {
        return [
            'theme' => ['required', 'array'],
            'theme.home-header-height' => ['numeric', 'min:0', 'max:1000'],
            'theme.header-height' => ['numeric', 'min:0', 'max:1000'],
            'theme.header-link' => 'url',
            'theme.footer-height' => ['numeric', 'min:0', 'max:1000'],
            'theme.footer-height-mobile' => ['numeric', 'min:0', 'max:1000'],
            'theme.footer-link' => 'url',
        ];
    }

    public function attributes()
    {
        return [
            'theme.home-header-height' => trans('theme.sizes.home-header-height'),
            'theme.header-height' => trans('theme.sizes.header-height'),
            'theme.header-link' => trans('theme.links.header-link'),
            'theme.footer-height' => trans('theme.sizes.footer-height'),
            'theme.footer-height-mobile' => trans('theme.sizes.footer-height-mobile'),
            'theme.footer-link' => trans('theme.links.footer-link'),
        ];
    }
}
