<?php

namespace AwardForce\Http\Requests\Chapter;

use AwardForce\Http\Requests\SelectedModelsRequest;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;

class UndeleteChapterRequest extends SelectedModelsRequest
{
    protected $repository = ChapterRepository::class;

    public function rules(array $rules = [])
    {
        $rules = parent::rules($rules);
        $rules['selected'] = ['required', 'array', 'max:'.$this->allowedCount()];

        return $rules;
    }

    public function selected(): array
    {
        return $this->get('selected', []);
    }

    private function allowedCount(): int
    {
        return current_account()->chapterQuantityLimit - $this->getRepository()->getForSeason($this->seasonId())->count();
    }

    protected function getIds()
    {
        return $this->getRepository()->getTrashedForSeason($this->seasonId())->pluck('id')->toArray();
    }

    private function seasonId()
    {
        return SeasonFilter::viewingAll() ? null : SeasonFilter::getId();
    }

    public function messages()
    {
        return array_merge(parent::messages(), ['selected.max' => trans('chapters.messages.limit.message')]);
    }
}
