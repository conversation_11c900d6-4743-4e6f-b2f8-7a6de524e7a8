<?php

namespace AwardForce\Http\Requests\Kessel\FieldPairing;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Entries\Validation\FieldPairingsValidator;
use AwardForce\Modules\Entries\Validation\TablePairingsValidator;

class CreateFieldPairingRequest extends FormRequest
{
    public function rules()
    {
        return [
            'userId' => ['required', 'exists:users,global_id'],
            'sourceAccount' => ['required', 'exists:accounts,global_id'],
            'sourceSeason' => ['required', 'exists:seasons,slug'],
            'sourceCategory' => ['required', 'exists:categories,slug'],
            'destinationAccount' => ['required', 'exists:accounts,global_id'],
            'destinationSeason' => ['required', 'exists:seasons,slug'],
            'destinationCategory' => ['required', 'exists:categories,slug'],
            'pairings' => [
                'required',
                'json',
                new FieldPairingsValidator($this->pairings),
                app(TablePairingsValidator::class),
            ],
        ];

        // TODO: validate everything we did not validate in MyAF :'(
    }

    public function authorize()
    {
        return true;
    }
}
