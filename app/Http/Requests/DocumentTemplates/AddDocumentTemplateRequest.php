<?php

namespace AwardForce\Http\Requests\DocumentTemplates;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Files\Models\File;
use Illuminate\Validation\Rule;

class AddDocumentTemplateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'translated' => 'required|translation_required:name',
            'fileToken' => 'required|array|min:1',
            'fileToken.*' => [
                'required',
                'string',
                'max:255',
                Rule::exists('files', 'token')
                    ->where('account_id', current_account_id())
                    ->where('resource', File::RESOURCE_DOCUMENT_TEMPLATES),
            ],
        ];
    }

    public function attributes(): array
    {
        return [
            'fileToken' => strtolower(trans('document-templates.form.file.label')),
        ];
    }
}
