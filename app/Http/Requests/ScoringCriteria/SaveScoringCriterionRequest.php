<?php

namespace AwardForce\Http\Requests\ScoringCriteria;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use AwardForce\Rules\JsonTranslationRequired;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

abstract class SaveScoringCriterionRequest extends FormRequest
{
    protected function baseRules(?ScoreSet $scoreSet): array
    {
        return [
            'translated' => 'required|translation_required:title,shortcode',
            'weight' => ['required', 'numeric', Rule::when($scoreSet?->calculationIsMean(), 'greater_than:0')],
            'order' => ['numeric'],
            'commentsAllowed' => ['boolean'],
            'commentsRequired' => ['boolean'],
            'scoringControl' => [
                'required',
                Rule::in(
                    ScoringCriterion::SCORING_CONTROL_SELECT,
                    ScoringCriterion::SCORING_CONTROL_SLIDER,
                    ScoringCriterion::SCORING_CONTROL_INPUT,
                    ScoringCriterion::SCORING_CONTROL_RECOMMENDATION
                ),
            ],
            'recommendations' => [
                'sometimes',
                'required_if:scoringControl,'.ScoringCriterion::SCORING_CONTROL_RECOMMENDATION,
            ],
            'translated.recommendationLabel' => [
                'array',
                'sometimes',
                'required_if:scoringControl,'.ScoringCriterion::SCORING_CONTROL_RECOMMENDATION,
                Rule::when(
                    Str::is(ScoringCriterion::SCORING_CONTROL_RECOMMENDATION, $this->get('scoringControl')),
                    new JsonTranslationRequired
                ),
            ],
            'translated.recommendationLabel.*' => [
                'sometimes',
                'json',
                'required_if:scoringControl,'.ScoringCriterion::SCORING_CONTROL_RECOMMENDATION,
            ],
            'scores' => [
                'sometimes',
                'required_if:scoringControl,'.ScoringCriterion::SCORING_CONTROL_RECOMMENDATION,
                'array',
                'min:1',
            ],
            'scores.*' => [
                'sometimes',
                'required_if:scoringControl,'.ScoringCriterion::SCORING_CONTROL_RECOMMENDATION,
                'numeric',
                'distinct',
            ],
        ];
    }

    public function prepareForValidation(): void
    {
        if ($this->input('scoringControl') !== ScoringCriterion::SCORING_CONTROL_RECOMMENDATION) {
            return;
        }

        $this->merge([
            'maximumScore' => max($this->input('scores')),
            'minimumScore' => min($this->input('scores')),
        ]);
    }

    /**
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'translated.recommendationLabel' => trans('validation.attributes.recommendationLabel'),
            'translated.recommendationLabel.*' => trans('validation.attributes.recommendationLabel'),
        ];
    }
}
