<?php

namespace AwardForce\Http\Requests\Comment;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Comments\Services\UserTags;

class GetCommentsRequest extends FormRequest
{
    public function authorize()
    {
        return app(UserTags::class)->isValidToken($this->get('token'));
    }

    public function rules()
    {
        return [
            'tags' => 'required|array',
        ];
    }
}
