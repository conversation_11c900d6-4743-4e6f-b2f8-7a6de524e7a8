<?php

namespace AwardForce\Console\Commands\Migrations;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class LinkAttachmentsToTabs extends Command
{
    /**
     * @var string
     */
    protected $signature = 'migrate:link-attachments-to-tabs';

    /**
     * @var string
     */
    protected $description = 'Link attachments without a tab_id to the appropriate tab in their season.';

    public function handle()
    {
        $count = DB::update(
            "UPDATE attachments
                JOIN entries ON entries.id = attachments.entry_id
                LEFT JOIN (
                     SELECT tabs.season_id, MIN(tabs.id) AS tab_id
                     FROM tabs
                     WHERE tabs.type = 'Attachments' AND tabs.deleted_at IS NULL
                     GROUP BY tabs.season_id
                ) AS attachments_tab ON attachments_tab.season_id = entries.season_id
                LEFT JOIN (
                     SELECT tabs.season_id, MIN(tabs.id) AS tab_id
                     FROM tabs
                     WHERE tabs.type = 'Attachments' AND tabs.deleted_at IS NOT NULL
                     GROUP BY tabs.season_id
                ) AS deleted_attachments_tab ON deleted_attachments_tab.season_id = entries.season_id
                SET attachments.tab_id = IF(attachments_tab.tab_id IS NOT NULL, attachments_tab.tab_id, deleted_attachments_tab.tab_id)
                WHERE attachments.tab_id IS NULL"
        );

        $this->info("{$count} attachments updated");
    }
}
