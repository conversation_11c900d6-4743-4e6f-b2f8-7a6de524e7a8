<?php

namespace AwardForce\Console\Commands\Migrations;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Chapters\Models\Chapter;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

class SeedDefaultChapterNameTranslations extends Command
{
    /** @var string */
    protected $signature = 'migrate:seed-default-chapter-name-translations';

    /** @var string */
    protected $description = 'Seed default chapter name translations.';

    public function handle()
    {
        $accounts = Account::all();

        $this->output->progressStart($accounts->count());

        $accounts->each(function (Account $account) {
            if (! $account->languages->count()) {
                $this->output->progressAdvance();

                return;
            }

            $defaultLanguage = $account->defaultLanguage();

            Chapter::where('account_id', $account->id)
                ->each(function (Chapter $chapter) use ($defaultLanguage, $account) {
                    $translations = DB::table('translations')
                        ->where('account_id', $chapter->accountId)
                        ->where('foreign_id', $chapter->id)
                        ->where('resource', 'Chapter')
                        ->where('field', 'name');

                    $defaultTranslation = $translations
                        ->where('language', $defaultLanguage->code)
                        ->where('value', 'Default')
                        ->first();

                    // If a default translation exists and it's the only translation, translations can be safely seeded
                    if ($defaultTranslation && $translations->count() === 1) {
                        $this->seedTranslations($account, $chapter);
                    }
                });

            $this->output->progressAdvance();
        });

        $this->output->progressFinish();
    }

    /**
     * Seed the 'Default' chapter name translations.
     */
    private function seedTranslations(Account $account, Chapter $chapter)
    {
        foreach ($account->languages as $language) {
            if (! Lang::has('chapters.default', $language->code)) {
                continue;
            }

            DB::transaction(function () use ($language, $chapter) {
                DB::table('translations')
                    ->where('account_id', $chapter->accountId)
                    ->where('foreign_id', $chapter->id)
                    ->where('language', $language->code)
                    ->where('resource', 'Chapter')
                    ->where('field', 'name')
                    ->delete();

                DB::table('translations')->insert([
                    'account_id' => $chapter->accountId,
                    'foreign_id' => $chapter->id,
                    'language' => $language->code,
                    'resource' => 'Chapter',
                    'field' => 'name',
                    'value' => Lang::get('chapters.default', [], $language->code),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);
            });
        }
    }
}
