<?php

namespace AwardForce\Console\Commands;

use AwardForce\Modules\Billing\Commands\SyncUsageLogs as SyncUsageLogsCommand;
use Illuminate\Console\Command;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Throwable;

class SyncUsageLogs extends Command
{
    use DispatchesJobs;
    use RunsOnAllDatabases;

    protected $signature = 'billing:sync-usage-logs';
    protected $description = 'Trigger SyncUsageLogs commands for each subscription provider';

    public function handle(): int
    {
        $this->runOnAllDatabases(function () {
            $providers = array_values(config('payment-subscriptions.gateways.chargebee.providers'));

            foreach ($providers as $provider) {
                try {
                    $this->dispatch(new SyncUsageLogsCommand($provider));
                } catch (Throwable $e) {
                    $this->error("Failed to dispatch for provider {$provider}: ".$e->getMessage());
                    throw $e;
                }
            }
        });

        $this->info('Dispatched SyncUsageLogs commands.');

        return self::SUCCESS;
    }
}
