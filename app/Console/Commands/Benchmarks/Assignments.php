<?php

namespace AwardForce\Console\Commands\Benchmarks;

use AwardForce\Console\Commands\Benchmarks\Assignments\SyncScoreSet;
use AwardForce\Console\Commands\Benchmarks\Assignments\UpdateEntry;
use AwardForce\Console\Commands\Benchmarks\Assignments\UpdateJudge;
use AwardForce\Console\Commands\Benchmarks\Assignments\UpdatePanel;
use Illuminate\Console\Command;

class Assignments extends Command
{
    /**
     * @var string
     */
    protected $signature = 'benchmark:assignments {name? : Options: SyncScoreSet, UpdateEntry, UpdateJudge, UpdatePanel} {--force} {--multiplier=1}';

    /**
     * @var string
     */
    protected $description = 'Runs the assignments benchmark tests.';

    /**
     * Available benchmarks
     *
     * @var array
     */
    protected $benchmarks = [SyncScoreSet::class, UpdateEntry::class, UpdateJudge::class, UpdatePanel::class];

    public function handle()
    {
        if (! $this->option('force')
            && ! $this->confirm('WARNING: This script will reset your database. Do you wish to continue?')) {
            return;
        }

        foreach ($this->benchmarks as $benchmark) {
            $this->benchmark($benchmark);
        }
    }

    private function benchmark(string $benchmark)
    {
        $name = class_basename($benchmark);

        if ($this->argument('name') && $this->argument('name') != $name) {
            return;
        }

        $multiplier = $this->option('multiplier');
        $this->line(PHP_EOL."Starting <info>{$name}</info> with multiplier <comment>x{$multiplier}</comment> ".PHP_EOL);

        (new $benchmark($this, $multiplier))->run();

        $this->line(PHP_EOL."Finished <info>{$name}</info>".PHP_EOL);
    }
}
