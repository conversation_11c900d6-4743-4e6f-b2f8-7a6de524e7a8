<?php

namespace AwardForce\Console\Commands\Benchmarks\Assignments;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Panels\Models\Panel;
use Illuminate\Support\Collection;

class UpdatePanel extends Benchmark
{
    /**
     * @var int
     */
    private $judgeCount;

    /**
     * @var int
     */
    private $entryCount;

    public function run()
    {
        $this->judgeCount = $this->multiplier * 10;
        $this->entryCount = $this->multiplier * 1000;

        $this->output("Creating {$this->judgeCount} judges...");
        $judges = $this->createJudges();
        [$judgesA, $judgesB, $judgesC] = $judges->chunk(ceil($judges->count() / 3));

        $this->output("Creating {$this->entryCount} entries...");
        $entries = $this->createEntries();
        [$entriesA, $entriesB, $entriesC] = $entries->chunk(ceil($entries->count() / 3));
        $this->moderate($entriesC, Entry::MODERATION_STATUS_REJECTED);

        $this->output('New panel, invited judges...');
        $invitePanel = $this->createPanelInvitedJudges($judgesA->merge($judgesB));

        $this->output('New panel, role based...');
        $rolePanel = $this->createPanelRoleBased($judgesA->merge($judgesB));

        $this->output('Refresh panel, invited judges...');
        $this->updatePanel($invitePanel);

        $this->output('Refresh panel, role based...');
        $this->updatePanel($rolePanel);

        $this->output('Edit panel, swap out half of the entries...');
        $this->moderate($entriesB, Entry::MODERATION_STATUS_REJECTED);
        $this->moderate($entriesC, Entry::MODERATION_STATUS_APPROVED);
        $this->updatePanel($invitePanel);

        $this->output('Edit panel, swap out half of the judges...');
        $this->swapJudges($invitePanel, $judgesA->merge($judgesC));

        $this->output('Edit panel, swap out half of the roles...');
        $this->swapRoles($rolePanel, $judgesA->merge($judgesC));

        $this->output('Edit panel, drop all judges...');
        $this->dropJudges($invitePanel);

        $this->output('Edit panel, drop all roles...');
        $this->dropRoles($rolePanel);

        $this->countAssignments();
    }

    /**
     * Set up X judges
     */
    private function createJudges(): Collection
    {
        $judges = new Collection;

        for ($i = 0; $i < $this->judgeCount; $i++) {
            $judges->push($this->setupUserWithRole('Judge'));
        }

        return $judges;
    }

    /**
     * Set up X entries
     */
    private function createEntries(): Collection
    {
        $entries = new Collection;

        for ($i = 0; $i < $this->entryCount; $i++) {
            $entries->push($this->makeEntry());
        }

        return $entries;
    }

    /**
     * Set up an invited panel and assign all of the specified judges, then update the panel assignments.
     *
     * Measures the time it takes to create assignments for a newly created panel.
     */
    private function createPanelInvitedJudges(Collection $judges): Panel
    {
        $panel = $this->makePanel(Panel::JUDGES_INVITED);
        $panel->judges()->sync($judges->just('id'));

        $this->updatePanel($panel);

        return $panel;
    }

    /**
     * Set up a role based panel, assign the roles from the judges, and setup the assignments for the panel.
     *
     * Measures the time it takes to set up assignments for a newly created role based panel.
     */
    private function createPanelRoleBased(Collection $judges): Panel
    {
        $panel = $this->makePanel(Panel::JUDGES_ROLES);

        $panel->roles()->sync($judges->pluck('roles')->collapse()->just('id'));

        $this->updatePanel($panel);

        return $panel;
    }

    /**
     * Update panel assigments with the timer to measure execution time.
     */
    private function updatePanel($panel)
    {
        $this->timer(function () use ($panel) {
            $this->updater->updatePanel($panel);
        });

        $this->persistAssignments();
    }

    /**
     * Apply the specified moderation status to each entry.
     */
    private function moderate(Collection $entries, string $status)
    {
        $entries->each(function (Entry $entry) use ($status) {
            $entry->moderate($status)->save();
        });
    }

    /**
     * Change the judges that are assigned to the panel to the given set of judges,
     * and update the panel assignments.
     *
     * Measures the time it takes to change the assigned judges on a panel,
     * essentially removing the existing assignments and setting up new ones.
     */
    private function swapJudges(Panel $panel, Collection $judges)
    {
        $panel->judges()->sync($judges->just('id'));

        $this->updatePanel($panel);
    }

    /**
     * Swap the roles on the panel to a new set of roles, and update assignments.
     *
     * Measures the time it takes to change the assigned roles on a panel,
     * essentially removing the existing assignments and setting up new ones.
     */
    private function swapRoles(Panel $panel, Collection $judges)
    {
        $panel->roles()->sync($judges->pluck('roles')->collapse()->just('id'));

        $this->updatePanel($panel);
    }

    /**
     * Forget all judges on a panel and update assignments.
     *
     * Measures how long it takes to remove all judge assignments from a panel.
     */
    private function dropJudges(Panel $panel)
    {
        $panel->judges()->sync([]);

        $this->updatePanel($panel);
    }

    /**
     * Forget all roles on a panel and update assignments.
     *
     * Measures how long it takes to remove all role assignments from a panel.
     */
    private function dropRoles(Panel $panel)
    {
        $panel->roles()->sync([]);

        $this->updatePanel($panel);
    }
}
