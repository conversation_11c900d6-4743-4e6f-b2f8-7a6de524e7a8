<?php

namespace AwardForce\Console\Commands;

use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use Illuminate\Console\Command;

class FixContentBlocksVisibility extends Command
{
    use RunsOnAllDatabases;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:fix-content-blocks-visibility';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command useful fix visibility of \'standard\' type content blocks';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $contentBlocksKeys = collect(config('awardforce.content-blocks'))->where('type', 'standard')->keys();

        $this->runOnAllDatabases(function () use ($contentBlocksKeys) {
            $wrongContentBlocks = ContentBlock::whereIn('key', $contentBlocksKeys)->where('visibility', '!=', 'open');
            $wrongContentBlocks->each(function (ContentBlock $contentBlock) {
                $contentBlock->visibility = 'open';
                $contentBlock->save();
            });
        });
    }
}
