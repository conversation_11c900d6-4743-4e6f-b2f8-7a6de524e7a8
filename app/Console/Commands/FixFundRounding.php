<?php

namespace AwardForce\Console\Commands;

use AwardForce\Library\Values\Amount;
use AwardForce\Modules\Funding\Data\Fund;
use Illuminate\Console\Command;

class FixFundRounding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'funds:fix-rounding {slug}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fix rounding on a fund identified by its slug.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $slug = $this->argument('slug');

        $fund = Fund::whereSlug($slug)->first();

        $allocated = $fund->allocations->pluck('amount')->map(
            function (Amount $amount): float {
                return $amount->value();
            }
        )->sum();

        $fund->allocated = new Amount($allocated, $fund->allocated->currency());
        $fund->available = $fund->budget->subtract($fund->allocated);

        $fund->save();
    }
}
