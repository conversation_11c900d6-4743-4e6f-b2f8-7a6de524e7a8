<?php

namespace AwardForce\Auth\Integrations;

use AwardForce\Auth\Exceptions\UnknownIntegrationProviderException;
use AwardForce\Auth\Integrations\Providers\Provider;
use AwardForce\Auth\Integrations\Providers\Salesforce;

class ProviderManager
{
    /**
     * @var array
     */
    private $drivers = [
        'salesforce' => Salesforce::class,
    ];

    /**
     * @return Provider
     *
     * @throws UnknownIntegrationProviderException
     */
    public function fromDriver(string $driver)
    {
        if (! isset($this->drivers[$driver])) {
            throw new UnknownIntegrationProviderException($driver);
        }

        return app($this->drivers[$driver]);
    }
}
