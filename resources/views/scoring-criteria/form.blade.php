<scoring-criterion-form-wrapper
    id="scoring-criterion-form-wrapper"
    :criterion="@js($translatedCriterion)"
    :categories="@js($categories)"
    :score-sets="@js(for_vue($scoreSets))"
    :form="@js($form)"
    :fields="@js($fields)"
    :supported-languages="@js(current_account()->supportedLanguageCodes())"
    :translations="@js($translations)"
    cancel-url="{{ route('scoring-criteria.index') }}"
    form-url="{{ $formUrl }}"
    :can-save="@js($canSave)"
></scoring-criterion-form-wrapper>
