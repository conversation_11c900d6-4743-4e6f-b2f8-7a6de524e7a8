<h3>
    @lang('broadcasts.recipients-table.user-count', ['count' => $consolidatedRecipients->count()])
    @lang('broadcasts.recipients-table.email-count', ['count' => $recipients->count()])
</h3>
<table class="table recipients-table">
    <thead>
    <tr>
        <th>@lang('broadcasts.recipients-table.columns.name')</th>
        <th>@lang('broadcasts.recipients-table.columns.email')</th>
    </tr>
    </thead>
    <tbody>

    @foreach ($consolidatedRecipients->take(10) as $recipient)
        <tr>
            <td>{{ $recipient->fullName() }}</td>
            <td>{{ $recipient->email() }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
@if ($consolidatedRecipients->count() > 10)
    <p><b>@lang('broadcasts.recipients-table.others', ['count' => $consolidatedRecipients->count() - 10])</b></p>
@endif
