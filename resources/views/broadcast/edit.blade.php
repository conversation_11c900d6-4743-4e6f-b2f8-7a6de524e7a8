@section('title')
    {!! HTML::pageTitle([trans('broadcasts.titles.main'), trans('broadcasts.titles.edit')]) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('broadcasts.titles.main'), route('broadcast.index')],
                    [trans('broadcasts.titles.edit')],
                ]])
            </div>
        </div>
    </div>

    @include('partials.errors.display')

    <h3>{{ trans('broadcasts.form.heading.recipients') }}</h3>
    <p>
        <b>
            @if (Lang::has('broadcasts.recipients.'.$broadcast->type))
                {{ trans_choice('broadcasts.recipients.'.$broadcast->type, $recipients->count()) }}
            @else
                {{ trans('broadcasts.descriptions.'.$broadcast->type) }}
            @endif
        </b>
    </p>
    @include('broadcast.active-filters', ['filters' => $broadcast->filters])

    {!! html()->modelForm($broadcast, 'put', route('broadcast.update', $broadcast->slug))->attributes(['class' => 'vertical', 'data-pjax' => ''])->open() !!}
        @include('broadcast.form')
    {!! html()->closeModelForm() !!}
@endsection
