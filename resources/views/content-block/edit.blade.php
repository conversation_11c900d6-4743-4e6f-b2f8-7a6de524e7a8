@section('title')
    {!! HTML::pageTitle([trans('content-block.titles.main'), trans('content-block.keys.'.$contentBlock->key)]) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('content-block.titles.main'), route('content-block.index')],
                    [trans('content-block.keys.'.$contentBlock->key)],
                ]])
            </div>
        </div>
    </div>

    @include('partials.errors.display')

    <div>
        {!! html()->modelForm($contentBlock, 'put', route('content-block.update', $contentBlock->slug))->class('vertical')->open() !!}
        {!! HTML::updatedAt() !!}
            @include('content-block.form')
            <input type="hidden" name="redirectAfterSubmit" value="{{$redirectAfterSubmit}}"/>
        {!! html()->closeModelForm() !!}
    </div>
@stop
