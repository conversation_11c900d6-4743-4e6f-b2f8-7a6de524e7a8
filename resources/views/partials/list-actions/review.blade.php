<reviewer
    name="reviewer"
    :ids="{{ isset($selected) ? "[$selected]" : 'selected' }}"
    :labels="@js(array_merge(
        [
            'button' => trans_elliptic('buttons.initiate_review_stage'),
            'title' => trans('buttons.initiate_review_stage'),
            'label' => trans('review-flow.table.columns.review_stage'),
            'cancel' => trans('buttons.cancel')
        ],
        $labels ?? []
    ))"
    route="{{ route($resource . '.initiate-review-stage', $params ?? []) }}"
    method="POST"
    :stages="@js($reviewStages)"
    button-class="{{ isset($buttonClass) ? "$buttonClass" : '' }}"
    @if(!isset($selected))
        @reveal="onReveal"
    @else
        @reveal="$emit('reveal', $event)"
    @endif
>
</reviewer>
