@if ($elevioEnabled)
    <link rel="preconnect" href="https://cdn.elev.io" crossorigin/>
    <script>
        !function(e,l,v,i,o,n){e[i]||(e[i]={}),e[i].account_id=n;var g,h;g=l.createElement(v),g.type="text/javascript",g.defer=1,g.src=o+n,h=l.getElementsByTagName(v)[0],h.parentNode.insertBefore(g,h);e[i].q=[];e[i].on=function(z,y){e[i].q.push([z,y])}}(window,document,"script","_elev","https://cdn.elev.io/sdk/bootloader/v4/elevio-bootloader.js?cid=","{{ is_awardforce() ? $accountId : '5f7707557a711' }}");

        window._elev.on('load', function(_elev) {
            @if (is_goodgrants() && $user)
                _elev.setUser({
                    first_name: "{{ $user->firstName }}",
                    last_name: "{{ $user->lastName }}",
                    email: "{{ $user->email }}",
                    user_hash: "{{ $hash }}",
                    registered_at: "{{ strtotime($user->currentMembership->createdAt) }}",
                });
            @endif

            $(window).on('darkmode:enabled', function () {
                _elev.setSettings({ enabled: false });
                _elev.setSettings({ embeddable_css_file: '{{ $cssFileDarkmode }}' });
                _elev.setSettings({ enabled: true });
            });

            $(window).on('darkmode:disabled', function () {
                _elev.setSettings({ enabled: false });
                _elev.setSettings({ embeddable_css_file: '{{ $cssFile }}' });
                _elev.setSettings({ enabled: true });
            });
        });
    </script>
@endif
