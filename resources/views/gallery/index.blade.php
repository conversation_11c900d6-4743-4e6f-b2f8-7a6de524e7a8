@section('title')
    {!! HTML::pageTitle(trans('gallery.titles.main')) !!}
@stop

@section('main')
    <div class="vote-page">
        <header id="header">
            <a href="{{ $headerLink ?: '/' }}" class="logo" name="{{ lang(current_account(), 'name') . ' ' . strtolower(trans('menu.home')) }}">
                {{ lang(current_account(), 'name') . ' ' . strtolower(trans('menu.home')) }}
            </a>
        </header>

        @include('partials.holocron.feature-intro')

        {!! isset($contentBlock) ? HTML::contentBlock($contentBlock) : HTML::contentBlockCreate('gallery-info', 'ScoreSet', $scoreSet->id) !!}

        <div class="vote-content">
            <div class="vote-results">
                <div class="filtertron">
                    {!! html()->form('GET', url()->current())->open() !!}
                    <div class="search-container">
                        <div class="keyword-search">
                            <i class="af-icons af-icons-search"></i>
                            <i class="arrow-down" id="expander"></i>
                            {!! html()->label(trans('miscellaneous.search.placeholder'), 'keywords')->attributes(['class' => 'accessible-hidden']) !!}
                            {!! html()->text('keywords', Request::get('keywords'))->attributes(['class' => 'form-control keywords', 'placeholder' => trans('miscellaneous.search.placeholder')]) !!}
                        </div>
                    </div>
                    <div class="extended-container{{ query_parameters(['chapter']) ? ' visible' : '' }}">
                        <div class="fields">
                            @include('assignment.search.filters.badge', ['badges' => $badges])

                            @if (has_multiple_chapters())
                                <div class="form-group">
                                    {!! html()->label(trans('judging.table.columns.chapter'), 'chapter') !!}
                                    {!! html()->select('chapter', for_select($chapters, ['id', 'name'], $empty = true, true), Request::get('chapter'))->attributes(['class' => 'form-control']) !!}
                                </div>
                            @endif

                            <div class="form-group">
                                {!! html()->label(trans('judging.table.columns.category'), 'category') !!}
                                @include('category.select', [
                                    'overrideCategories' => $categories, 'categoriesSeason' => $scoreSet->season->slug
                                ])
                            </div>

                            @if (count((array) $scoreSet->searchFields))
                                @include('field.searchable', ['resource' => 'Entries', 'fieldIds' => $scoreSet->searchFields])
                            @endif

                            @include('html.buttons.search')
                        </div>
                    </div>
                    {!! html()->form()->close() !!}
                </div>

                <div id="searchResults"{!! query_parameters(['chapter']) ? ' class="courteous"' : '' !!}>
                    <div class="row island">
                        <div class="col-xs-12">
                            <div class="title">
                                @include('partials.header.breadcrumbs', ['crumbs' => [
                                     [trans('gallery.titles.dashboard'), route('gallery-dashboard.index', ['context' => $selectedContextRouteName])],
                                     [$scoreSet->name],
                                 ]])
                                @include('partials.holocron.feature-intro-revealer')
                            </div>
                        </div>
                    </div>

                    @if ($selectorCategories->count() > 1 && $scoreSet->categoryQuickFilter)
                        <div class="quick-selector quick-selector-voting" id="quick-selector-category">
                            <div class="row">
                                <div class="col-md-12">
                                    @include('partials.page.quick-selector', ['field' => 'category', 'options' => $selectorCategories])
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="row island">
                        <div class="col-xs-12">
                            @if ($scoreSet->slideshow && $assignments->count())
                                <div class="pull-left">
                                    <a href="{{ route('gallery.slideshow', ['scoreSet' => $scoreSet->slug, 'entry' => $assignments->first()->entrySlug, 'search' => next_previous_token($assignments, 0, $searchToken)]) }}" target="slideshow" class="btn btn-tertiary ignore">
                                        <i class="af-icons af-icons-slideshow"></i>
                                        @lang('slideshow.buttons.slideshow')
                                    </a>
                                </div>
                            @endif

                            <div class="search-info pull-right">
                                @include('partials.page.active-filters', ['filters' => Request::all(), 'context' => ['fieldIds' => $scoreSet->searchFields]])
                                @include('partials.page.pagination-info', ['paginator' => $assignments])
                            </div>
                        </div>
                    </div>

                    @include('partials.errors.display')
                    @include('partials.errors.message')
                    @if (!$scoreSet->galleryIsOpen())
                        <div class="alert-warning sticky island" role="alert">
                            <div class="icon">
                                <i class="af-icons-md af-icons-md-alert-warning"></i>
                            </div>
                            <div class="message">
                                @lang('gallery.messages.closed-round')
                            </div>
                        </div>
                    @endif

                    @if ($assignments->count())
                        <div class="row island">
                            <div class="col-xs-12">
                                <ul class="cards">
                                    @foreach ($assignments as $key => $assignment)
                                        {!! HTML::galleryCard($scoreSet, $assignment, $thumbnails, next_previous_token($assignments, $key, $searchToken)) !!}
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-xs-12">
                                @include('partials.page.pagination', ['paginator' => $assignments])
                            </div>
                        </div>
                        @include('judging.partials.bulk-download', ['assignments' => $assignmentIds])
                    @else
                        <div>
                            <p>@lang('voting.cards.empty')</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@stop
