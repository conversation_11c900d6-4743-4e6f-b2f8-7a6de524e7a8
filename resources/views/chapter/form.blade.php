<?php $managerIds = isset($managerIds) ? $managerIds : null; ?>

{!! HTML::updatedAt() !!}

<div class="row">
    <div class="col-xs-12 col-md-6 chapters-left-column">
        {!! html()->seasonSelector('chapters.form.season.label') !!}

        <div class="form-group">
            {!! html()->label(sentence_case(trans('chapters.form.name.label')), 'name') !!}
            {!! Multilingual::text('name', $chapter, ['class' => 'form-control', 'disabled' => !empty($readOnly)]) !!}
        </div>

        <div class="form-group">
            {!! html()->label(sentence_case(trans('chapters.form.managers.label')), 'managerIds') !!}
            {!! html()->multi_select('managerIds', for_select($managers), $managerIds, ['placeholder' => trans('chapters.form.managers.placeholder'), 'disabled' => !empty($readOnly)]) !!}
        </div>

        @if (empty($readOnly))
            <div class="form-actions">
                @include('html.buttons.save')
                @include('html.buttons.cancel', ['route' => 'chapter.index'])
            </div>
        @endif

    </div>
</div>
