@if (! $entry->inProgress() && Consumer::can('update', 'EntriesAll'))
    <div class="action-overflow-wrapper">
        <div class="dropdown action-overflow">
            <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" role="button" tabindex="0">
                <i class="af-icons af-icons-action-overflow"></i>
                <span class="sr-only">{{ trans('buttons.action_overflow', ['resource' => $entry->resourceLabel()]) }}</span>
            </a>
            <ul class="dropdown-menu dropdown-menu-left">
                @include('entry.manager.resubmission-actions')
            </ul>
        </div>
        <span>{{ $status }}</span>
    </div>
@else
    {{ $status }}
@endif
