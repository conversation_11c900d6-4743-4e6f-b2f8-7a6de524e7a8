<quick-manager id="quickManager" class="quick-manager" inline-template v-cloak>
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="row">
                @if ($canModerate)
                    <div class="col-xs-12 col-md-4">
                        @include('entry.common.quick-approver', ['entry' => $model ?? $entry, 'moderationStatuses' => $moderationStatuses, 'name' => 'manager-quick-approver'])
                    </div>
                @endif
                @if (Consumer::can('create', 'Tags'))
                    <div class="col-xs-12 col-md-4">
                        @include('entry.common.quick-tagger', ['entry' => $model ?? $entry])
                    </div>
                @endif
                <div class="col-xs-12 col-md-4">
                    @include('entry.common.quick-comments', ['entry' => $model ?? $entry, 'tags' => ['entry' => ($model ?? $entry)->id, 'manager']])
                </div>
            </div>
        </div>
    </div>
</quick-manager>
