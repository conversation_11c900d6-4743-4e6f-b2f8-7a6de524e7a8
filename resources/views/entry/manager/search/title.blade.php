@if($entry)
    @include('entry.common.archived-icon', ['entry' => $entry])

    <div class="submittable-title">
        {!! HTML::resourceLink($entry->title, route('entry.manager.view', [$entry->slug]), $entry) !!}
    </div>

    @include('entry.common.deadline', ['entry' => $entry])

    @if (Consumer::can('view', 'Tags'))
        <div class="labels">
            {!! HTML::tagLabels($entry->tagsWithRelations()) !!}
        </div>
    @endif

    @if ($showEntryLinks || $showFeedback)
        <ul class="entry-title-links">
            @endif

            @if ($showEntryLinks)
                @include('award.partials.entry-links', ['entry' => $entry])
            @endif

            @if ($showFeedback)
                <li>
                    @include('entry.manager.search.feedback', ['entry' => $entry])
                </li>
            @endif

            @if ($showEntryLinks || $showFeedback)
        </ul>
    @endif
@endif
