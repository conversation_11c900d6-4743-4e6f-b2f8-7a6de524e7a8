@section('title')
    {!! HTML::pageTitle([trans('exports.titles.main'), trans('exports.titles.new')]) !!}
@stop

@section('main')
    <new-custom-export-layout id="custom-export-layout" :columns="@js($columns)" area="{{ Request::get('area') }}" inline-template v-cloak>
        <div>
            <div class="row island">
                <div class="col-xs-12">
                    <div class="title">
                        @include('partials.header.breadcrumbs', ['crumbs' => [
                            [trans('exports.titles.main'), route('export.index')],
                            [trans('exports.titles.new')],
                        ]])
                    </div>
                </div>
            </div>

            @include('partials.errors.message')
            @include('partials.errors.display')

            {!! html()->modelForm($exportLayout, 'post', route('export.create', ['redirect' => Request::get('redirect')]))->attributes(['class' => 'vertical'])->open() !!}
                @include('export.form')

                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-actions">
                            @include('html.buttons.save')
                            @include('html.buttons.cancel', ['route' => 'export.index'])
                        </div>
                    </div>
                </div>
            {!! html()->closeModelForm() !!}
        </div>
    </new-custom-export-layout>
@stop
