<tr>
    <td>@include('api-key.partials.action-overflow', compact('apiKey'))</td>
    <td><a href="{{ route('api-key.edit', [$apiKey->slug]) }}">{{ $apiKey->name }}</a></td>
    <td>
        <input class="api-key-slug hidden" value="{{ $apiKey->slug }}">
        <span class="api-key-masked">{{ $apiKey->valueMasked() }}</span>
        <span class="btn-api-key-show" @click.prevent="show($event.target, '{{ $apiKey->slug }}')"><a>{{ trans('api-keys.table.show') }}</a></span>
        <span class="api-key-full hidden">{{ $apiKey->value }}</span>
        <span class="btn-api-key-hide hidden" @click.prevent="hide($event.target)"><a>{{ trans('api-keys.table.hide') }}</a></span>
        <span class="btn-api-key-copy" @click.prevent="copy('{{ $apiKey->value }}')"><a>{{ trans('api-keys.table.copy') }}</a></span>
        <input class="api-key-copy-message-text hidden" value="{{ trans('api-keys.messages.copy') }}">
    </td>
    <td>{{ $apiKey->scope == 'read' ? trans('api-keys.table.values.scope.read') : trans('api-keys.table.values.scope.write') }}</td>
    <td>{{ HTML::relativeTime($apiKey->createdAt) }}</td>
</tr>
