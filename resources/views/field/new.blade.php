@section('title')
    {!! HTML::pageTitle([trans('fields.titles.main'), trans('fields.titles.new')]) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('fields.titles.main'), route('field.index')],
                    [trans('fields.titles.new')],
                ]])
                {{ lang($season, 'name') }} {{ trans('fields.names.'.strtolower($field->resource).'.label') }}
            </div>
        </div>
    </div>

    @include('partials.errors.display')

    {!! html()->modelForm($field, action:route('field.add'))->attributes(['class' => 'vertical field-form field-form-new', 'data-pjax' => '', 'id' => $formSelector])->open() !!}
        @include('html.tabular')

        <div class="row">
            <div class="col-xs-12">
                <div class="form-actions">
                    @include('html.buttons.save')
                    <a href="{{ Request::get('redirect', route('field.index')) }}" class="btn btn-tertiary btn-lg">{{ trans('buttons.cancel') }}</a>
                </div>
            </div>
        </div>
    {!! html()->closeModelForm() !!}
 @endsection
