<div class="dropdown action-overflow">
    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" role="button" tabindex="0">
        <i class="af-icons af-icons-action-overflow"></i>
        <span class="sr-only">{{ trans('buttons.action_overflow') }}</span>
    </a>
    <ul class="dropdown-menu dropdown-menu-left">
        <li>
            @include('partials.list-actions.delete', ['resource' => 'review-flow.task.manage', 'params' => ['redirect' => route('review-flow.task.manage')], 'selected' => $reviewTask->id])
        </li>
        @if(! $reviewTask->reviewStage->isRefereeReview())
            <li>
                @include('partials.list-actions.reassign-reviewer', [
                    'resource' => 'review-flow.task.manage',
                    'params' => ['redirect' => route('review-flow.task.manage'),
                    'reviewTask' => $reviewTask->token],
                    'selected' => $reviewTask->id,
                    'notificationId' => $reviewTask->reviewStage->startNotificationId,
                ])
            </li>
            @if($reviewTask->actionTaken !== null)
                <li>
                    @include('partials.list-actions.review-task-reset-action', ['resource' => 'review-flow.task.manage', 'params' => ['redirect' => route('review-flow.task.manage')], 'selected' => $reviewTask->id])
                </li>
            @endif
        @endif
    </ul>
</div>
