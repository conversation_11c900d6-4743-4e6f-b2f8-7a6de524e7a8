<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
</head>
<body>
<table>
    <thead>
        <tr>
            <th width="6">@lang('review-flow.table.columns.entry-id')</th>
            <th width="8">@lang('review-flow.table.columns.category-code')</th>
            <th width="10">@lang('review-flow.table.columns.slug')</th>
            <th width="30">@lang('review-flow.table.columns.entry')</th>
            <th width="30">@lang('review-flow.table.columns.name')</th>
            <th width="10">@lang('review-flow.table.columns.season')</th>
            <th width="20">@lang('review-flow.table.columns.started')</th>
            <th width="30">@lang('review-flow.table.columns.reviewer_name')</th>
            <th width="30">@lang('review-flow.table.columns.reviewer_email')</th>
            <th width="30">@lang('review-flow.table.columns.decision')</th>
        </tr>
    </thead>
    <tbody>
        @foreach ($tasks as $chunk)
            @foreach ($chunk as $task)
                <tr>
                    <td>{{ $task->entry->localId }}</td>
                    <td>{{ $task->entry->category->shortcode ?? '' }}</td>
                    <td>{{ $task->entry->slug }}</td>
                    <td>{{ $task->entry->title }}</td>
                    <td>{{ $task->reviewStage->name }}</td>
                    <td>{{ $task->reviewStage->season->name }}</td>
                    <td>{{ HTML::localisedDateTime($task->createdAt, Consumer::dateLocale()) }}</td>
                    <td>{{ $task->assignees->pluck('name')->merge([$task->assigneeName ?: $task->assigneeEmail])->filter()->implode(', ') }}</td>
                    <td>{{ $task->assignees->pluck('email')->merge([$task->assigneeEmail])->filter()->implode(', ') }}</td>
                    <td>{{ $task->isProceed() ? $task->reviewStage->proceedStatus : ($task->isStop() ? $task->reviewStage->stopStatus : '') }}</td>
                </tr>
            @endforeach
        @endforeach
    </tbody>
</table>
</body>
</html>
