@section('title')
    {!! HTML::pageTitle([trans('review-flow.titles.main'), trans('review-flow.titles.new')]) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('review-flow.titles.main'), route('review-flow.index')],
                    [trans('review-flow.titles.new')],
                ]])
            </div>
        </div>
    </div>

    @include('partials.errors.summary')
    @include('partials.errors.message')

    <div id="review-flow-form">
        {!! html()->modelForm($stage, action:route('review-flow.create'))->class('vertical')->open() !!}

        @include('html.tabular')

        <div class="row">
            <div class="col-xs-12">
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-lg">
                        {!! trans('buttons.save') !!}
                    </button>

                    <a href="{{ route('review-flow.index') }}" class="btn btn-tertiary btn-lg">{{ trans('buttons.cancel') }}</a>
                </div>
            </div>
        </div>

        {!! html()->closeModelForm() !!}
    </div>
@stop
