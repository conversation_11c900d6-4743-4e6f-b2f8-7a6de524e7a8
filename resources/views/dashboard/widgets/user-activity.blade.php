<div class="col-md-6 col-lg-4">
    <div class="widget">
        <header>
            <h2>{{ trans('dashboard.widgets.user_activity.title') }}</h2>
        </header>
        <table class="table table-condensed stats">
            <thead>
                <tr>
                    <th></th>
                    <th class="stat">@lang('dashboard.widgets.user_activity.labels.total')</th>
                    <th class="stat">@lang('dashboard.widgets.user_activity.labels.change')</th>
                </tr>
            </thead>
            <tbody>
            @foreach ($stats as $stat)
                <tr>
                    <td>{{ $stat['label'] }}</td>
                    <td class="stat">{{ $stat['total'] }}</td>
                    <td class="stat"
                    @if (isset($stat['invertColours']) && $stat['invertColours'])
                        style="color: {{ stat_change_colour($stat['difference'], $colours['negative'], $colours['positive']) }}"
                    @else
                        style="color: {{ stat_change_colour($stat['difference'], $colours['positive'], $colours['negative']) }}"
                    @endif
                    >
                        {!! HTML::caretStat($stat['difference']) !!} {{ abs($stat['difference']) ?: '-' }}
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
</div>
