<div class="col-md-6 col-lg-4">
    <div class="widget">
        <header>
            <h2>{{ trans('dashboard.widgets.new_users.title') }}</h2>
        </header>
        <table class="table table-condensed stats">
            <thead>
                <tr>
                    <th></th>
                    <th class="stat">@lang('dashboard.widgets.new_users.labels.total')</th>
                    <th class="stat">@lang('dashboard.widgets.new_users.labels.change')</th>
                </tr>
            </thead>
            <tbody>
            @foreach ($stats as $stat)
                <tr>
                    <td>{{ $stat['label'] }}</td>
                    <td class="stat">{{ $stat['total'] }}</td>
                    <td class="stat" style="color: {{ stat_change_colour($stat['difference'], $colours['positive'], $colours['negative']) }}">
                        {!! HTML::caretStat($stat['difference']) !!} {{ abs($stat['difference']) ?: '-' }}
                    </td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
</div>

