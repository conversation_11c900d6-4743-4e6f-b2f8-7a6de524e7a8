<template>
  <date-time-timezone
    :id="id"
    mode="datetime"
    :field="field"
		:field-service="fieldService"
    :value="value"
    :disabled="disabled"
    :element-id="elementId"
    :aria-required="field.required ? 'true' : 'false'"
    :has-error="hasError"
    :aria-describedby="ariaDescribedby"
    @update:value="onInput"
    @changed="validate"
  />
</template>

<script>
import DateTimeTimezone from './DateTimeTimezone';
import Field from './Field';

export default {
  components: { DateTimeTimezone },
  extends: Field,
};
</script>
