import { User } from '@/domain/models/User';

enum ProfilePhotoSize {
	Large = 'large',
	Medium = 'medium',
	Small = 'small',
}

const profilePhotoSizes = {
	[ProfilePhotoSize.Large]: { width: 50, height: 50 },
	[ProfilePhotoSize.Medium]: { width: 35, height: 35 },
	[ProfilePhotoSize.Small]: { width: 18, height: 18 },
};

const fontSizes = (size: ProfilePhotoSize) =>
	({
		[ProfilePhotoSize.Small]: 10,
		[ProfilePhotoSize.Medium]: 17,
		[ProfilePhotoSize.Large]: 24,
	}[size]);

type ProfilePhoto = Pick<User, 'fullName' | 'image' | 'color' | 'initials'>;

export { fontSizes, ProfilePhotoSize, profilePhotoSizes, type ProfilePhoto };
