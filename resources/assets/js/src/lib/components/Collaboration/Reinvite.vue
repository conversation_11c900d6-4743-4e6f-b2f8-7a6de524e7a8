<template>
	<span v-if="visible">
		<i v-if="loading" class="local-loader af-icons af-icons-repeat af-icons-animate-rotate" />
		<a v-else :disabled="loading" @click.prevent="reinvite">
			({{ lang.get('collaboration.form.actions.invitation.resend') }})
		</a>
	</span>
</template>

<script lang="ts">
import { Collaborator } from '@/domain/models/Collaborator';
import { defineComponent } from 'vue';
import { useController } from '@/domain/services/Composer';
import { reinviteController, Props, View } from '@/lib/components/Collaboration/Reinvite.controller';

export default defineComponent<Props, View>({
	props: {
		collaborator: {
			type: Object as Collaborator,
			required: true,
		},
	},

	setup: useController(reinviteController, 'reinviteController') as View,
});
</script>

<style lang="scss" scoped>
.local-loader {
	font-size: 12px;
	width: 12px;
	height: 12px;
	margin-left: 2px;
}
</style>
