import { EventEmitters } from '@/domain/utils/Events';
import { FormSettings } from '@/domain/models/Form';

enum FormSettingsEvents {
	SettingsUpdated = 'update:settings',
	ContentBlockSelected = 'update:selectedContentBlock',
}

type FormSettingsEmitters = EventEmitters<{
	[FormSettingsEvents.SettingsUpdated]: (settings: FormSettings) => void;
	[FormSettingsEvents.ContentBlockSelected]: (selectedContentBlock: string) => void;
}>;

export { FormSettingsEvents, FormSettingsEmitters };
