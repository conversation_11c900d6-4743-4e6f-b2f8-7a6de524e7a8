import { Composer } from '@/domain/services/Composer';
import ContactsBillingContactsRow from '@/modules/billing/components/contacts/ContactsBillingContactsRow.vue';
import { expect } from 'chai';
import PortalVue from 'portal-vue';
import { createLocalVue, mount, shallowMount } from '@vue/test-utils';

const localVue = createLocalVue();
localVue.use(PortalVue);

const bilingContact = {
	firstName: 'First',
	lastName: 'Contact',
	email: '<EMAIL>',
};

localVue.use(PortalVue);

describe('ContactsBillingContactsRow snapshot', () => {
	let viewStub;
	let defaultOptions;

	beforeEach(() => {
		viewStub = {
			lang: { get: (t) => t },
			inViewMode: true,
			editable: true,
			viewContact: bilingContact,
			editContact: bilingContact,
			errors: {},
			showConfirmDelete: false,
			toggleEditContactForm: () => {},
			saveContact: () => {},
			resetForm: () => {},
			hideDeleteConfirmationDialog: () => {},
			deleteContact: () => {},
			showDeleteConfirmationDialog: () => {},
			errorExists: () => false,
		};
		defaultOptions = {
			propsData: {
				bilingContact,
				canEdit: true,
				viewMode: true,
				customerId: 'recoaGCNp7SrP4VXA',
			},
			localVue,
		};

		Composer.mockView('ContactsBillingContactsRowController', viewStub);
	});

	it('should properly initialized with a billing contact', () => {
		let contactsBillingContactsRow = shallowMount(ContactsBillingContactsRow, defaultOptions);

		expect(contactsBillingContactsRow.vm.viewContact['firstName']).to.equal('First');
		expect(contactsBillingContactsRow.vm.viewContact['lastName']).to.equal('Contact');
		expect(contactsBillingContactsRow.vm.viewContact['email']).to.equal('<EMAIL>');
		expect(contactsBillingContactsRow.vm.editContact['firstName']).to.equal('First');
		expect(contactsBillingContactsRow.vm.editContact['lastName']).to.equal('Contact');
		expect(contactsBillingContactsRow.vm.editContact['email']).to.equal('<EMAIL>');
	});

	it('should show the overflow action menu if can be edited', () => {
		viewStub.editable = true;
		let contactsBillingContactsRow = mount(ContactsBillingContactsRow, defaultOptions);

		expect(contactsBillingContactsRow.find('.overflow-cell').exists()).to.be.true;
	});
});
