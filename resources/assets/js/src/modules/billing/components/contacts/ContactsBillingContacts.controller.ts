import { BillingContact } from '@/modules/billing/Billing.types';
import { Ref, ref, SetupFunction } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';

type Props = {
	billingContacts: BillingContact[];
	isOwner: boolean;
	customerId: string;
};

type View = {
	lang: Trans;
	localBillingContacts: Ref<BillingContact[]>;
	showNewContactForm: Ref<boolean>;
	toggleAddNewContactForm: () => void;
	addNewContact: (contact: BillingContact) => void;
	removeDeletedContact: (contact: BillingContact) => void;
};

const contactsBillingContactsController: SetupFunction<Props, View> = (props): View => {
	const localBillingContacts = ref(props.billingContacts);
	const showNewContactForm = ref(false);

	const toggleAddNewContactForm = () => {
		showNewContactForm.value = !showNewContactForm.value;
	};

	return {
		lang: trans(),
		localBillingContacts,
		showNewContactForm,
		toggleAddNewContactForm,
		addNewContact: (contact: BillingContact) => {
			localBillingContacts.value.push(contact);
			toggleAddNewContactForm();
		},
		removeDeletedContact: (contact: BillingContact) => {
			localBillingContacts.value = localBillingContacts.value.filter(
				(localBillingContact: BillingContact) => localBillingContact.id !== contact.id
			);
		},
	};
};

export { Props, View, contactsBillingContactsController };
