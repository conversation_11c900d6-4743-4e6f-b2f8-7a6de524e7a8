<template>
	<div>
		<div class="panel-title">
			<h4>{{ lang.get('billing.invoices.list.title') }}</h4>
		</div>
		<table class="table">
			<thead>
				<tr>
					<th>{{ lang.get('billing.invoices.list.table.id') }}</th>
					<th>{{ lang.get('billing.invoices.list.table.date') }}</th>
					<th class="amount-column">
						{{ lang.get('billing.invoices.list.table.amount') }}
					</th>
					<th>{{ lang.get('billing.invoices.list.table.status') }}</th>
					<th>{{ lang.get('billing.invoices.list.table.download') }}</th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="(invoice, index) in invoices" :key="index">
					<td>
						{{ invoice.invoiceId }}
					</td>
					<td>{{ invoice.invoiceDate }}</td>
					<td class="amount-column">{{ invoice.value }}</td>
					<td>
						<span :class="overDueClass(invoice)">{{ invoice.status }}</span>
					</td>
					<td>
						<a :href="invoice.invoiceDownloadLink" target="_blank"><span class="af-icons af-icons-pdf"></span></a>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
</template>

<script lang="ts">
import { Invoice } from '@/modules/billing/Billing.types';
import { defineComponent, type PropType } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';
export default defineComponent({
	props: {
		invoices: {
			type: Array as PropType<Invoice[]>,
			required: true,
		},
	},

	setup() {
		const lang: Trans = trans();

		return {
			lang,
			overDueClass: (invoice: Invoice) =>
				invoice.status === lang.get('billing.invoices.list.status.overdue') ? 'overdue' : '',
		};
	},
});
</script>

<style scoped>
.amount-column {
	text-align: right;
}
</style>
