<script>
import { computed, onMounted, ref } from 'vue';
import MergeFields from '@/lib/components/Shared/MergeFields/MergeFields';
import Multilingual from '@/lib/components/Translations/Multilingual.vue';
import TextEditor from '@/lib/components/Shared/editor/TextEditor.vue';
import { Multiselect } from 'vue-bootstrap';

export default {
	components: {
		MergeFields,
		Multilingual,
		Multiselect,
		TextEditor,
	},

	props: {
		mergeFieldsArray: {
			type: Object,
			required: true,
		},
	},

	setup(props) {
		const contentBlockSelect = ref(null);
		const contentBlock = ref(null);
		const mergeFields = computed(() => {
			if (!contentBlock.value) {
				return [];
			}

			return props.mergeFieldsArray[contentBlock.value];
		});

		onMounted(() => {
			contentBlock.value = contentBlockSelect.value.value;
		});

		const onContentBlockChange = (e) => {
			contentBlock.value = e.target.value;
		};

		return {
			contentBlockSelect,
			contentBlock,
			mergeFields,

			onContentBlockChange,
		};
	},
};
</script>
