import { computed, ref } from 'vue';
import type { WebhookFormProps, WebhookFormView } from './WebhookForm.types';

const webhookFormControllerFactory =
	() =>
	(props: WebhookFormProps): WebhookFormView => {
		const subscriptionEvents = ref(props.webhook?.subscriptionEvents ?? []);
		const selectedForm = ref(form(props.webhook?.formId)?.slug);

		const forms = computed(() =>
			props.formsWithFields.map((form) => ({
				slug: form.slug,
				name: form.name,
			}))
		);

		const formFields = computed(() => {
			const form = props.formsWithFields.find((form) => form.slug === selectedForm.value);
			return form?.fields ?? [];
		});

		const selectedFieldSlugs = computed(() => {
			const fieldIds = new Set(props.fieldIds);
			return formFields.value.filter((field) => fieldIds.has(field.id)).map((field) => field.slug);
		});

		const showResourceSelector = computed(() => subscriptionEvents.value?.includes('FieldValueUpdated') || false);

		function onFormChange(event: Event, formSlug: string) {
			selectedForm.value = formSlug;
		}

		function form(id?: number) {
			return props.formsWithFields.find((form) => form.id === id);
		}

		function onSubscriptionEventsChange(events: string[]) {
			subscriptionEvents.value = events ?? [];
		}

		return {
			subscriptionEvents,
			selectedForm,
			forms,
			formFields,
			selectedFieldSlugs,
			showResourceSelector,
			onFormChange,
			form,
			onSubscriptionEventsChange,
		};
	};

const webhookFormController = (props: WebhookFormProps) => webhookFormControllerFactory()(props);

export { webhookFormController, webhookFormControllerFactory, WebhookFormProps };
