<template>
  <dropdown
    v-if="!contract.signedAt"
    :id="'contract-' + contract.slug"
    button-class="dropdown-toggle"
    container-class="action-overflow"
    menu-class="dropdown-menu-right"
  >
    <span slot="label">
      <i class="af-icons af-icons-action-overflow"></i>
    </span>
    <li>
      <a href @click.prevent="$emit('deleted', contract)">
        {{ labels.delete }}
      </a>
    </li>
  </dropdown>
</template>

<script>
import { Dropdown } from 'vue-bootstrap';

export default {
  components: {
    Dropdown
  },
  props: {
    contract: {
      type: Object,
      required: true
    },
    labels: {
      type: Object,
      required: true
    }
  }
};
</script>
