import AvatarField from '@/modules/users/components/AvatarField';
import DarkModeToggle from '@/lib/components/DarkModeToggle';
import FileUploadWrapper from '@/modules/fields/components/FileUploadWrapper';
import FormPanel from '@/lib/components/Shared/FormPanel';
import PasswordField from '@/lib/components/Fields/PasswordField';
import PhoneField from '@/lib/components/Fields/PhoneField';
import UserColourField from '@/modules/users/components/UserColourField';

export default {
	PhoneField,
	PasswordField,
	'file-upload-field-wrapper': FileUploadWrapper,
	DarkModeToggle,
	AvatarField,
	UserColourField,
	FormPanel,
};
