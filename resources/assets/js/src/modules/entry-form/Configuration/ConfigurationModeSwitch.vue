<template>
	<div class="toggle-container mtl">
		<div v-output="lang.get('entries.form.configuration.label')" class="toggle-content-label" />
		<div class="toggle-content-switch">
			<popover v-if="isGrantReportStart" :content="lang.get('entries.form.configuration.deactivated')" trigger="hover">
				<toggle-switch
					id="configuration-mode-toggle-switch"
					name="toggle-switch"
					:on-label="lang.get('buttons.on')"
					:off-label="lang.get('buttons.off')"
					:checked="true"
					:aria-label="lang.get('entries.form.configuration.label')"
					:disabled="true"
				></toggle-switch>
			</popover>
			<toggle-switch
				v-else
				id="configuration-mode-toggle-switch"
				name="toggle-switch"
				:on-label="lang.get('buttons.on')"
				:off-label="lang.get('buttons.off')"
				:checked="checkedState"
				:aria-label="lang.get('entries.form.configuration.label')"
				@change="onToggle"
			></toggle-switch>
		</div>
		<confirmation-modal
			modal-id="modal-target-toggle-configuration-mode"
			:show-modal="confirmToggle"
			:confirmation="lang.get('miscellaneous.unsaved_changes')"
			:confirm-button-label="lang.get('buttons.continue')"
			:cancel-button-label="lang.get('buttons.cancel')"
			@closed="confirmToggle = false"
			@confirmed="toggle"
		/>
	</div>
</template>

<script>
import { mapActions, mapMutations, mapState, mapGetters } from 'vuex';
import { Popover } from 'vue-bootstrap';
import ConfirmationModal from '@/lib/components/Shared/ConfirmationModal';
import ToggleSwitch from '@/lib/components/Shared/ToggleSwitch';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import url from '@/lib/url.js';

export default {
	components: {
		ToggleSwitch,
		ConfirmationModal,
		Popover,
	},
	mixins: [langMixin],
	data() {
		return {
			confirmToggle: false,
		};
	},
	computed: {
		...mapGetters('entryForm', ['userId', 'selectedTab', 'visibleTabs', 'currentForm']),
		...mapState('entryForm', { disabled: 'uploadInProgress' }),
		...mapState('entryFormConfiguration', {
			checkedState: 'configurationMode',
			configurationInProgress: 'configurationInProgress',
		}),
		isGrantReportStart() {
			return window.location.href.indexOf('grant-report/manager/start') !== -1;
		},
		isEntryForm() {
			return window.location.href.indexOf('entry-form/manager/start') !== -1;
		},
	},
	watch: {
		checkedState(flag) {
			const url = new URL(window.location.href);
			const params = url.searchParams;

			params.set('configuration', flag ? 'true' : 'false');

			// Update the URL without reloading the page
			window.history.replaceState({}, '', `${url.pathname}?${params.toString()}${url.hash}`);
		},
	},
	created() {
		const currentUrl = window.location.href;
		const urlParameter = url.getUrlParameter(currentUrl, 'configuration');
		const isInConfigurationMode = urlParameter === 'true';

		if (this.isGrantReportStart || (this.isEntryForm && isInConfigurationMode)) {
			this.enableConfigurationMode(true);
		}
	},
	methods: {
		...mapActions('entryFormConfiguration', ['restoreConfiguration']),
		...mapMutations('entryForm', ['selectTab']),
		...mapMutations('entryFormConfiguration', ['setConfigurationInProgressFlag', 'enableConfigurationMode']),
		onToggle() {
			if (this.configurationInProgress) {
				this.confirmToggle = true;
			} else {
				this.toggle();
			}
		},
		toggle() {
			this.restoreConfiguration().then(() => {
				this.setConfigurationInProgressFlag(false);
				this.enableConfigurationMode(!this.checkedState);
				this.switchToTheFirstTab();
			});
		},
		switchToTheFirstTab() {
			// If configuration mode is on keep the current tab open
			if (this.checkedState) {
				return;
			}

			// If configuration mode is off and the selected tab is disabled
			// switch to the first tab
			if (this.selectedTab && this.selectedTab.disabled) {
				this.selectTab(this.visibleTabs[0].id);
			}
		},
	},
};
</script>
