<template>
	<filtertron-tray-section id="chapter-configurator-general" :title="lang.get('chapters.configuration.general.label')">
		<div class="fields island">
			<div class="form-group">
				<label>
					{{ lang.get('chapters.form.name.label') }}
				</label>
				<multilingual
					:supported-languages="supportedLanguages"
					:resource="chapter"
					property="name"
					@input="(translated) => handleTranslatedInput('name', translated)"
				/>
			</div>
			<div class="form-group">
				<label style="text-transform: capitalize">
					{{ lang.get('shared.description') }}
				</label>
				<multilingual
					mode="markdown"
					:supported-languages="supportedLanguages"
					:resource="chapter"
					property="description"
					@input="(translated) => handleTranslatedInput('description', translated, true)"
				/>
			</div>
			<div class="form-group">
				<div class="checkbox styled">
					<input
						id="active"
						name="active"
						type="checkbox"
						:checked="chapter.active"
						:disabled="chapter.locked"
						@change="(e) => handleInput('active', e.target.checked)"
					/>
					<label for="active">
						{{ lang.get('chapters.form.active.label') }}
					</label>
				</div>
			</div>
		</div>
	</filtertron-tray-section>
</template>

<script>
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection';
import Multilingual from '@/lib/components/Translations/Multilingual';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';

export default {
	inject: ['lang'],
	components: {
		FiltertronTraySection,
		Multilingual,
	},
	mixins: [handleInputMixin, handleTranslatedInputMixin],
	props: {
		chapter: {
			type: Object,
			required: true,
		},
	},
};
</script>
