import { Collections } from '@/domain/services/Rt/DataSource';
import { emptyApi } from '@/modules/entry-form/Collaboration/services/Api';
import { Tab } from '@/modules/entry-form/RefereeTypes';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { collectionServiceFactory, nullDocumentService } from '@/domain/services/Collaboration/Document';

const emptyCollaborationRefereesService = () => ({
	...nullDocumentService(),
});

const useCollaborationRefereesService = (tabId?: Tab['id']) => {
	const { submittableSlug, formSlug, isCollaborative, api } = useCollaborativeSubmittable();

	if (!isCollaborative) {
		return {
			service: emptyCollaborationRefereesService(),
			api: emptyApi,
		};
	}

	return {
		service: collectionServiceFactory(`${submittableSlug}-${tabId}`, formSlug)(Collections.Referees),
		api: {
			createReferee: api.createReferee,
			updateReferee: api.updateReferee,
			deleteReferee: api.deleteReferee,
		},
	};
};

export { useCollaborationRefereesService };
