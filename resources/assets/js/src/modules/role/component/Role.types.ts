type ActionType = 'create' | 'view' | 'update' | 'delete';

type ResourceType =
	| 'ApiKeys'
	| 'EntriesAll'
	| 'EntriesOwner'
	| 'Broadcasts'
	| 'Categories'
	| 'Chapters'
	| 'Content'
	| 'ContractAll'
	| 'Fields'
	| 'Forms'
	| 'Funding'
	| 'Grants'
	| 'Integrations'
	| 'Notifications'
	| 'Orders'
	| 'Panels'
	| 'Payments'
	| 'Roles'
	| 'Rounds'
	| 'ScoreSets'
	| 'ScoresAll'
	| 'ScoresOwner'
	| 'ScoringCriteria'
	| 'Seasons'
	| 'Settings'
	| 'Tags'
	| 'Users'
	| 'Webhooks';

type Mode = 'deny' | 'inherit' | 'allow';

type Permission = {
	action: ActionType;
	actionDisabled: boolean;
	chapterLimited: boolean;
	description: string;
	guest: boolean;
	mode: string;
	name: string;
	registration: boolean;
	resource: ResourceType;
};

type RoleSettings = {
	slug: string;
	name: string;
	default: number;
	guest: number;
	registration: number;
	requireAuthenticator: number;
	formContentId: number;
	completedContentId: number;
	completedScoreSetId: number;
	chapterLimited: number;
};

type RoleRegistrationForm = {
	[key: string]: string;
};

type RoleRegistrationCompleted = {
	[key: string]: string;
};

type ScoreSets = {
	[key: string]: {
		[key: string]: string;
	};
};

type RoleProperties = {
	id?: number;
	permissions: Permission[];
	roleRegistrationForm: RoleRegistrationForm;
	roleRegistrationCompleted: RoleRegistrationCompleted;
	completedScoreSets: ScoreSets;
};

type ShowSimpleFormRef = {
	value: boolean;
};

export {
	ActionType,
	ResourceType,
	Mode,
	Permission,
	RoleSettings,
	RoleProperties,
	RoleRegistrationForm,
	RoleRegistrationCompleted,
	ScoreSets,
	ShowSimpleFormRef,
};
