<template>
	<div>
		<div class="row">
			<div class="col-xs-12">
				<h2 class="island">
					{{ lang.get('judging.fast_start.entries.title') }}
				</h2>
			</div>
			<div class="col-xs-12 col-md-4">
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="panel-title">
							<h4>{{ lang.get('panels.form.categories.label') }}</h4>
						</div>
						<multiselect
							id="category-selector"
							name="categoryIds[]"
							:options="categories"
							:selected-options="panel.categoryIds"
							:placeholder="lang.get('panels.form.categories.placeholder')"
							:select-all-label="lang.get('multiselect.select_all')"
							@selected="(categoryIds) => onInput({ categoryIds })"
						>
						</multiselect>
					</div>
				</div>
			</div>
			<div class="col-xs-12 col-md-4">
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="panel-title">
							<h4>{{ lang.get('panels.form.chapters.label') }}</h4>
						</div>
						<multiselect
							id="chapter-selector"
							name="chapterIds[]"
							:options="chapters"
							:selected-options="panel.chapterIds"
							:placeholder="lang.get('panels.form.chapters.placeholder')"
							:select-all-label="lang.get('multiselect.select_all')"
							@selected="(chapterIds) => onInput({ chapterIds })"
						>
						</multiselect>
					</div>
				</div>
			</div>
			<div class="col-xs-12 col-md-4">
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="panel-title">
							<h4>{{ lang.get('panels.form.tags.label') }}</h4>
						</div>
						<multiselect
							id="tag-selector"
							name="tagIds[]"
							:options="tags"
							:selected-options="panel.tagIds"
							:placeholder="lang.get('panels.form.tags.placeholder')"
							:select-all-label="lang.get('multiselect.select_all')"
							value-property="tag"
							@selected="(tagIds) => onInput({ tagIds })"
						>
						</multiselect>
					</div>
				</div>
			</div>
		</div>
		<navigation v-on="$listeners" />
	</div>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import { Multiselect } from 'vue-bootstrap';
import Navigation from '../Navigation';

export default {
	inject: ['lang'],
	components: {
		Multiselect,
		Navigation,
	},
	props: {
		categories: {
			type: Array,
			default: () => [],
		},
		chapters: {
			type: Array,
			default: () => [],
		},
		tags: {
			type: Array,
			default: () => [],
		},
	},
	computed: {
		...mapGetters('judgingFastStart', ['panel']),
	},
	created() {
		if (!this.panel) {
			this.setPanel({
				chapterIds: this.chapterIds(),
				categoryIds: this.categoryIds(),
			});
		}
	},
	methods: {
		...mapMutations('judgingFastStart', ['setPanel']),
		onInput(input) {
			this.setPanel({ ...this.panel, ...input });
		},
		chapterIds() {
			return this.chapters.filter((chapter) => !chapter.disabled).map((chapter) => chapter.id);
		},
		categoryIds() {
			const categoryIds = [];
			const callback = (category) => {
				if (category.children && category.children.length) {
					category.children.forEach(callback);
				} else {
					categoryIds.push(category.id);
				}
			};

			this.categories.forEach(callback);
			return categoryIds;
		},
	},
};
</script>
