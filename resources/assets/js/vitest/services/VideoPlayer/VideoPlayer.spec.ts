import { getPlayer } from '@/domain/services/VideoPlayer/VideoPlayerWrapper';
import GlobalData from '../../_common/GlobalData';
import init from '@/domain/services/VideoPlayer/VideoPlayerInit';
import VideoPlayer from '@/domain/services/VideoPlayer/VideoPlayer';
import { afterEach, beforeEach, describe, expect, it, Mock, vi } from 'vitest';

vi.mock('@/domain/services/VideoPlayer/VideoPlayerWrapper', () => ({
	getPlayer: vi.fn(),
}));

vi.mock('@/domain/services/VideoPlayer/VideoPlayerInit');

describe('Video Player Setup', () => {
	const globalData = GlobalData();

	beforeEach(() => {
		vi.resetAllMocks();
		globalData.document = {
			getElementsByClassName: () => [{ id: 'video1' }, { id: 'video2' }],
			getElementById: () => ({
				getAttribute: (attr: string) => {
					if (attr === 'data-source') {
						return 'source';
					} else if (attr === 'data-aspect-ratio') {
						return '16:9';
					}
				},
			}),
		};
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('should initialize video player for each element with custom options', () => {
		const mockAspectRatio = vi.fn();
		const mockOn = vi.fn();
		const mockSrc = vi.fn();
		(init as Mock).mockImplementation(() => ({
			aspectRatio: mockAspectRatio,
			on: mockOn,
			src: mockSrc,
		}));

		VideoPlayer.setup('video-element', {});

		expect(getPlayer).toHaveBeenCalledTimes(2);
		expect(getPlayer).toHaveBeenCalledWith('video1');
		expect(getPlayer).toHaveBeenCalledWith('video2');
		expect(init).toHaveBeenCalledTimes(2);
		expect(init).toHaveBeenCalledWith('video1', expect.any(Object));
		expect(init).toHaveBeenCalledWith('video2', expect.any(Object));
		expect(mockAspectRatio).toHaveBeenCalledTimes(2);
		expect(mockAspectRatio).toHaveBeenCalledWith('16:9');
		expect(mockOn).toHaveBeenCalledTimes(2);
		expect(mockOn).toHaveBeenCalledWith('xhr-hooks-ready', expect.any(Function));
		expect(mockSrc).toHaveBeenCalledTimes(2);
		expect(mockSrc).toHaveBeenCalledWith({ src: 'source', type: 'application/x-mpegURL' });
	});
});
