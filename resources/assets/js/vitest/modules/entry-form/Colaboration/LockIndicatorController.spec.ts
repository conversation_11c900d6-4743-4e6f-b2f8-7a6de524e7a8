import { collaborationUIBus } from '@/domain/signals/Collaboration';
import { Collaborator } from '@/domain/models/Collaborator';
import { HexColor } from '@/domain/models/Color';
import { describe, expect, it, test, vi } from 'vitest';
import { lockIndicatorController, View } from '@/modules/entry-form/Collaboration/LockIndicator.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
		language: { locale: 'en_GB' },
	},
}));

vi.mock('@/modules/entry-form/EntryFormProvider', () => ({
	useEntryFormContainer: () => ({
		onBeforeUnmount: () => {},
	}),
}));

describe('LockIndicator  Controller', () => {
	const props = { lockable: 'lockable' };

	test('user default value', () => {
		const view = lockIndicatorController(props) as View;
		expect(view.user.value).toBeNull();
	});

	test('text default value', () => {
		const view = lockIndicatorController(props) as View;
		expect(view.text.value).toBeNull();
	});

	it('should updates the user and text values after a lock event emitted', () => {
		const view = lockIndicatorController(props) as View;
		const collaborator: Collaborator = { user: 'myself' } as Collaborator;
		collaborationUIBus.emit('collaboration.lock.on-lockable', { locker: collaborator });

		expect(view.user.value).to.equal(collaborator);
		expect(view.text.value).to.equal('collaboration.form.fields.is_editing');
	});

	it('should not updates for own events', () => {
		const view = lockIndicatorController(props) as View;
		const collaborator: Collaborator = { user: 'myself' } as Collaborator;
		collaborationUIBus.emit('collaboration.lock.on-lockable', { });

		expect(view.text.value).to.be.null;
	});

	it('should reset the user and text values to null after a unlock event emitted', () => {
		const view = lockIndicatorController(props) as View;
		const collaborator: Collaborator = { user: 'myself' } as Collaborator;

		collaborationUIBus.emit('collaboration.lock.on-lockable', { locker: collaborator });
		expect(view.user.value).to.equal(collaborator);

		collaborationUIBus.emit('collaboration.lock.off-lockable', undefined);
		expect(view.user.value).toBeNull();
		expect(view.text.value).toBeNull();
	});

	it('should have styling for lock indicator element', () => {
		const view = lockIndicatorController(props) as View;
		expect(view.lockIndicatorStyle.value).toBeNull();

		const collaborator: Collaborator = { profilePhoto: { color: '#000fff' as HexColor } } as Collaborator;
		collaborationUIBus.emit('collaboration.lock.on-lockable', { locker: collaborator });
		expect(view.lockIndicatorStyle.value).toStrictEqual({ backgroundColor: '#000fff', color: '#ffffff' });
	});

	it('should have styling for profile photo outline', () => {
		const view = lockIndicatorController(props) as View;
		expect(view.profilePhotoOutline.value).toBe('none');

		const collaborator: Collaborator = { profilePhoto: { color: '#000fff' as HexColor } } as Collaborator;
		collaborationUIBus.emit('collaboration.lock.on-lockable', { locker: collaborator });
		expect(view.profilePhotoOutline.value).toBe('1px solid #ffffff');
	});
});
