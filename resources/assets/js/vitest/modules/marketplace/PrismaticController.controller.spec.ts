import { flushPromises } from '../../_common/ControllerHelpers';
import GlobalData from '../../_common/GlobalData';
import { SetupContext } from 'vue';
import { useContainer } from '@/domain/services/Container';
import { beforeEach, describe, expect, Mock, vi } from 'vitest';
import prismatic, { getMessageIframe, PrismaticMessageEvent } from '@prismatic-io/embedded';
import {
	prismaticMarketplaceController,
	Props,
} from '@/modules/integrations/component/PrismaticMarketplace.controller';

vi.mock('@/domain/services/Container', () => ({ useContainer: vi.fn() }));

const postMarketplaceEvent = vi.fn();
vi.mock('@/modules/integrations/component/PrismaticMarketplace.api', () => ({
	useMarketplaceDao: () => ({ postMarketplaceEvent }),
}));

vi.mock('@prismatic-io/embedded', () => ({
	default: {
		init: vi.fn(),
		authenticate: vi.fn(),
		showMarketplace: vi.fn(),
		setConfigVars: vi.fn(),
	},
	getMessageIframe: vi.fn(),
	PrismaticMessageEvent: {
		INSTANCE_DEPLOYED: 'INSTANCE_DEPLOYED',
		INSTANCE_DELETED: 'INSTANCE_DELETED',
	},
}));

const props: Props = {
	jwt: 'dummy-jwt',
	brand: 'brand-name',
	prismaticUrl: 'https://example.com',
	apiKey: 'dummy-api-key',
	langCode: 'en',
	baseUrl: 'https://base-url.com',
	connectionKey: 'connection-key',
};

const onMounted = vi.fn();
const addEventListener = vi.fn();

describe('PrismaticMarketplaceController', () => {
	const globalData = GlobalData();

	beforeEach(() => {
		vi.resetAllMocks();
		(useContainer as Mock).mockReturnValue({ onMounted });
		(onMounted as Mock).mockImplementation((cb) => cb());

		globalData.window = { addEventListener };
	});

	it('should init Prismatic on mount', async () => {
		prismaticMarketplaceController(props, {} as SetupContext);

		expect(prismatic.init).toHaveBeenCalledWith({
			prismaticUrl: props.prismaticUrl,
			fontConfiguration: {
				google: {
					families: ['Open Sans'],
				},
			},
		});
	});

	it('should add a message event listener on mount', async () => {
		prismaticMarketplaceController(props, {} as SetupContext);

		expect(addEventListener).toHaveBeenCalledWith('message', expect.any(Function));
	});

	it('should authenticate Prismatic on mount', async () => {
		prismaticMarketplaceController(props, {} as SetupContext);

		expect(prismatic.authenticate).toHaveBeenCalledWith({ token: props.jwt });
	});

	it('should show the Marketplace on mount', async () => {
		prismaticMarketplaceController(props, {} as SetupContext);

		await flushPromises();
		expect(prismatic.showMarketplace).toHaveBeenCalledWith({
			selector: '#prismatic-marketplace',
			filters: {
				label: props.brand,
			},
		});
	});

	// Simulate EventListener
	const dispatchEventListener = (message: PrismaticMessageEvent) => {
		globalData.MessageEvent = class MessageEvent {
			constructor(public type: string, public eventInitDict: Record<string, unknown>) {
				Object.assign(this, eventInitDict);
			}
		};

		addEventListener.mockImplementation((event, listener) => {
			if (event === 'message')
				listener({
					data: {
						event: message,
					},
				});
		});
	};

	it('should set config vars when INSTANCE_CONFIGURATION_LOADED event is received', async () => {
		(getMessageIframe as Mock).mockReturnValueOnce({});

		dispatchEventListener(PrismaticMessageEvent.INSTANCE_CONFIGURATION_LOADED);
		prismaticMarketplaceController(props, {} as SetupContext);
		await flushPromises();

		expect(getMessageIframe).toHaveBeenCalledWith({
			data: { event: PrismaticMessageEvent.INSTANCE_CONFIGURATION_LOADED },
		});

		expect(prismatic.setConfigVars).toHaveBeenCalledWith({
			iframe: {},
			configVars: {
				[props.connectionKey]: {
					inputs: {
						language: { value: props.langCode },
						apiKey: { value: props.apiKey },
						baseUrl: { value: props.baseUrl },
					},
				},
			},
		});
	});

	const eventTypes = [PrismaticMessageEvent.INSTANCE_DEPLOYED, PrismaticMessageEvent.INSTANCE_DELETED];

	it.each(eventTypes)('should call postMarketplaceEvent for %s event', async (message) => {
		dispatchEventListener(message);
		prismaticMarketplaceController(props, {} as SetupContext);
		await flushPromises();

		expect(postMarketplaceEvent).toHaveBeenCalledWith({ payload: { event: message } });
	});
});
