import { createLocalVue, shallowMount } from '@vue/test-utils';
import { expect } from 'chai';
import Multilingual from '@/lib/components/Translations/Multilingual';
import Uploader from '@/lib/components/Uploader/Uploader';
import FormConfigCover from '@/lib/components/Forms/FormConfigCover';

const localVue = createLocalVue();

const lang = { get: () => '' };

const newFormConfigCover = propsData =>
  shallowMount(FormConfigCover, {
    provide: { lang },
    propsData: {
      ...propsData
    },
    localVue
  });

describe('FormConfigCover', () => {
  it('contains Uploader component', () => {
    const formConfigCover = newFormConfigCover({
      form: {
        slug: 'XsdlDOpW',
        translated: {}
      }
    });

    expect(formConfigCover.findComponent(Uploader).exists()).to.be.true;
    expect(formConfigCover.findAllComponents(Multilingual).length).to.equal(1);
  });
});
