import PortalVue from 'portal-vue';
import { shallowMount, createLocalVue } from '@vue/test-utils';
import { expect } from  'chai';
import sinon from 'sinon';
import QuickGrantManager from '../../../../src/lib/components/QuickManager/QuickGrantManager.vue';

const localVue = createLocalVue();
localVue.use(PortalVue);

const response = Promise.resolve();
const urlSpy = sinon.spy();
const selectedSpy = sinon.spy();
const endDateSpy = sinon.spy();

describe('QuickGrantManager', () => {
  const quickGrantManager = shallowMount(QuickGrantManager, {
    propsData: {
      entryId: '1',
      selectedStatus: 'Slug1',
      grantStatuses: [
        {
          slug: 'Slug1',
          name: 'Status 1'
        },
        {
          slug: 'Slug2',
          name: 'Status 2'
        },
      ],
      timezone: 'Europe/Athens',
      endDate: '2022-02-02 02:02',
      routes: {'grant.status.add-to-entries': 'status/add/{grantStatus?}'}
    },
    mocks: {
      $http: {
        post: (url, params) => {
          urlSpy(url);
          selectedSpy(params.selected);
          endDateSpy(params.endDate);

          return response;
        }
      }
    },
    localVue
  });

  it('sets current values', () => {
    expect(quickGrantManager.vm.currentStatus).to.equal('Slug1');
    expect(quickGrantManager.vm.currentEndDate.timezone).to.equal('Europe/Athens');
    expect(quickGrantManager.vm.currentEndDate.datetime).to.equal('2022-02-02 02:02');
  });

  it('saves grant status and end date', () => {
    quickGrantManager.vm.currentStatusUpdated('component', 'Slug2');
    quickGrantManager.vm.endDateUpdated('2023-03-03 03:03');
    quickGrantManager.vm.save();

    return response.then(() => {
      expect(urlSpy.withArgs('/status/add/Slug2').calledOnce).to.be.true;
      expect(selectedSpy.withArgs(['1']).calledOnce).to.be.true;
      expect(endDateSpy.withArgs('2023-03-03 03:03').calledOnce).to.be.true;
    });
  });
});
