import { expect } from 'chai';
import emailValidator from '../../../../../src/lib/components/Fields/validator/email.js';

describe('email validator', () => {
  it('should validate emails', () => {
    expect(emailValidator('john')).to.be.false;
    expect(emailValidator('john@')).to.be.false;
    expect(emailValidator('john@awardforce')).to.be.false;
    expect(emailValidator('<EMAIL>')).to.be.true;
  });
});
