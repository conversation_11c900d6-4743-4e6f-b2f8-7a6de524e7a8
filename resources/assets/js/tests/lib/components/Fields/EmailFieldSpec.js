import EmailField from '@/lib/components/Fields/EmailField.vue';
import { expect } from 'chai';
import { shallowMount } from '@vue/test-utils';
import { useValidationStore } from '@/../tests/utils/test-helper';

describe('EmailField', () => {
	it('should disable if locked is true', () => {
		const emailField = shallowMount(EmailField, {
			propsData: {
				field: {
					id: 1,
					type: 'email',
				},
				value: '<EMAIL>',
			},
			data: () => ({
				disabledOrCollaborationLocked: true,
			}),
			...useValidationStore,
		});
		const input = emailField.find('input[type="email"]');

		expect(input.html()).to.contain('disabled="disabled"');
	});

	it('should not disable if locked is false', () => {
		const emailField = shallowMount(EmailField, {
			propsData: {
				field: {
					id: 1,
					type: 'email',
				},
				value: '<EMAIL>',
			},
			data: () => ({
				disabledOrCollaborationLocked: false,
			}),
			...useValidationStore,
		});
		const input = emailField.find('input[type="email"]');

		expect(input.html()).to.not.contain('disabled="disabled"');
	});
});
