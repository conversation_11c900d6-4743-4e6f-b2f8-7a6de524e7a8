import { shallowMount } from '@vue/test-utils';
import { useValidationStore } from '../../../utils/test-helper';
import { expect } from 'chai';
import ContentField from '../../../../src/lib/components/Fields/ContentField.vue';

describe('ContentField', () => {
  it('renders content', () => {
    const contentField = shallowMount(ContentField, {
      propsData: {
        field: {
          id: 1,
          type: 'content',
          title: 'Hello world'
        }
      },
      ...useValidationStore
    });

    const div = contentField.find('div');

    expect(div.exists()).to.be.true;
    expect(div.text()).to.equal('Hello world');
  });
});
