import { expect } from 'chai';
import { shallowMount } from '@vue/test-utils';
import sinon from 'sinon';
import VideoModal from '@/lib/components/Video/VideoModal.vue';

const pauseSpy = sinon.spy();

const videoModalFactory = (settings) => {
	const defaultSettings = {
		modalVisible: true,
		videoPlayerHeight: 1080,
		...settings,
	};

	return shallowMount(VideoModal, {
		propsData: {
			source: 'playlist.m3u8',
			poster: defaultSettings.poster ?? '',
			modalVisible: defaultSettings.modalVisible,
			caption: defaultSettings.caption ?? null,
			videoPlayerHeight: defaultSettings.videoPlayerHeight,
		},
		stubs: {
			VideoPlayer: {
				template: '<div ref="videoPlayer" />',
				props: ['source', 'caption'],
				data: () => ({
					player: {
						pause: pauseSpy,
					},
				}),
			},
		},
	});
};

describe('VideoModal', () => {
	it('pauses video when modal is closed', () => {
		const videoModal = videoModalFactory({ modalVisible: true });

		videoModal.vm.onClose();

		expect(pauseSpy.calledOnce).to.be.true;
		expect(videoModal.vm.isModalVisible).to.be.false;
	});

	it('respects the video player height style', () => {
		const videoModal = videoModalFactory({ videoPlayerHeight: 720 });

		const styleObject = videoModal.vm.styleObject;

		expect(styleObject.minHeight).to.equal('min(720px, 95vh)');
		expect(styleObject.width).to.equal('auto');
		expect(styleObject.maxHeight).to.equal('720px');
	});

	it('displays the video player with provided source', () => {
		const videoModal = videoModalFactory({});

		const videoPlayer = videoModal.findComponent({ ref: 'videoPlayer' });

		expect(videoPlayer.exists()).to.be.true;
		expect(videoPlayer.props('source')).to.equal('playlist.m3u8');
	});

	it('passes caption to video player if provided', () => {
		const caption = 'captions.vtt';
		const videoModal = videoModalFactory({ caption });

		const videoPlayer = videoModal.findComponent({ ref: 'videoPlayer' });

		expect(videoPlayer.props('caption')).to.equal(caption);
	});

	it('hides modal when modalVisible is false', async () => {
		const videoModal = videoModalFactory({ modalVisible: true });

		await videoModal.setProps({ modalVisible: false });

		expect(videoModal.vm.isModalVisible).to.be.false;
	});
});
