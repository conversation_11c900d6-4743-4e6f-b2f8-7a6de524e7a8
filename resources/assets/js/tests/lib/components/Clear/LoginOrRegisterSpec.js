import { expect } from 'chai';
import LoginOrRegister from '@/lib/components/Clear/LoginOrRegister';
import { overrideObfuscatedData } from '@/../tests/utils/test-helper';
import { shallowMount } from '@vue/test-utils';
import sinon from 'sinon';

const urlSpy = sinon.spy();
const dataSpy = sinon.spy();
const lang = { get: (v) => v };

const wrapperFactory = (propsData) =>
	shallowMount(LoginOrRegister, {
		propsData: {
			login: '',
			formAction: '/initiate',
			...propsData,
		},
		provide: { lang },
		mocks: {
			$http: {
				post: (url, params) => {
					urlSpy(url);
					dataSpy(params);
				},
			},
		},
	});

describe('LoginOrRegister', () => {
	it('handles input', () => {
		const wrapper = wrapperFactory();

		expect(wrapper.vm.loginValue).to.equal('');
		wrapper.vm.handleInput('foo');
		expect(wrapper.vm.loginValue).to.equal('foo');
	});

	it('handles submit', () => {
		const wrapper = wrapperFactory();
		wrapper.vm.handleInput('test');
		wrapper.vm.submit();
		expect(urlSpy.calledWith(wrapper.vm.formAction)).to.equal(true);
		expect(
			dataSpy.calledWith({ login: wrapper.vm.loginValue, role: wrapper.vm.roleSlug, consent: wrapper.vm.consent })
		).to.equal(true);
	});

	it('login is enabled by default (no setting)', () => {
		const wrapper = wrapperFactory();

		expect(wrapper.vm.showLoginForm).to.equal(true);
	});

	it('login is enabled when setting is enabled', () => {
		overrideObfuscatedData({
			settings: {
				'disable-login-form': false,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.showLoginForm).to.equal(true);
	});

	it('login is disabled when setting is disabled', () => {
		overrideObfuscatedData({
			settings: {
				'disable-login-form': true,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.showLoginForm).to.equal(false);
	});

	it('register is disabled', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': false,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.registerEnabled).to.equal(false);
	});

	it('register is enabled', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': true,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.registerEnabled).to.equal(true);
	});

	it('shows the login form', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': true,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.showLoginForm).to.equal(true);
	});

	it('hides the login form', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': false,
				'disable-login-form': true,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.showLoginForm).to.equal(false);
	});

	it('hides social login buttons when setting is disabled', () => {
		overrideObfuscatedData({
			settings: {
				'enable-3rd-party-authentication': false,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.showSocialLogin).to.equal(false);
	});

	it('hides social login buttons when there are no social providers set', () => {
		overrideObfuscatedData({
			settings: {
				'enable-3rd-party-authentication': true,
			},
		});

		const wrapper = wrapperFactory();
		expect(wrapper.vm.showSocialLogin).to.equal(false);
	});

	it('shows social login buttons when setting is enabled and there are social providers set', () => {
		overrideObfuscatedData({
			settings: {
				'enable-3rd-party-authentication': true,
			},
		});

		const wrapper = wrapperFactory({
			socialProviders: [
				{ name: 'google', content: 'google button' },
				{ name: 'facebook', content: 'facebook button' },
			],
		});
		expect(wrapper.vm.showSocialLogin).to.equal(true);
	});

	it('shows correct title', () => {
		let wrapper = wrapperFactory();

		// default title
		expect(wrapper.vm.title).to.equal('home.login.heading');

		// Login and register are enabled
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': true,
			},
		});
		wrapper = wrapperFactory();
		expect(wrapper.vm.title).to.equal('home.login.login_or_register');

		// Login is disabled
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': true,
				'disable-login-form': true,
			},
		});
		wrapper = wrapperFactory();
		expect(wrapper.vm.title).to.equal('home.register.heading');
	});

	it('Shows outer div and title when login form is enabled', () => {
		// Mock settings to make sure showLoginForm is true
		overrideObfuscatedData({
			settings: {
				'disable-login-form': false,
				'app-site-registration-open': true,
			},
		});

		const wrapper = wrapperFactory({ showTitle: true });
		expect(wrapper.find('div').exists()).to.be.true;
		expect(wrapper.find('h2#login-or-register').exists()).to.be.true;
	});

	it('Shows outer div and hides title when login form is disabled and social buttons enabled', () => {
		overrideObfuscatedData({
			settings: {
				'disable-login-form': true,
				'app-site-registration-open': false,
				'enable-3rd-party-authentication': true,
			},
		});

		const wrapper = wrapperFactory({
			showTitle: true,
			socialProviders: [
				{ name: 'google', content: 'google button' },
				{ name: 'facebook', content: 'facebook button' },
			],
		});
		expect(wrapper.find('div').exists()).to.be.true;
		expect(wrapper.find('h2#login-or-register').exists()).to.be.false;
	});

	it('Hides outer div and title when login form is disabled', () => {
		overrideObfuscatedData({
			settings: {
				'disable-login-form': true,
				'app-site-registration-open': false,
			},
		});

		const wrapper = wrapperFactory({ showTitle: true });
		expect(wrapper.find('div').exists()).to.be.false;
		expect(wrapper.find('h2#login-or-register').exists()).to.be.false;
	});
});
