import { createLocalVue } from '@vue/test-utils';
import { expect } from 'chai';
import Vuex from 'vuex';
import getters from '@/lib/store/modules/menu/getters';
import actions from '@/lib/store/modules/menu/actions';
import mutations from '@/lib/store/modules/menu/mutations';
import baseMenu from '../../components/Navigation/baseMenu';

const localVue = createLocalVue();
localVue.use(Vuex);
const generateContexts = contexts =>
  contexts.map(context => ({
    name: context,
    text: context,
    route: 'https://test.awardforce.dev/' + context
  }));

describe('Menu Store', () => {
  it('should store baseMenu and context data', () => {
    let contexts = generateContexts(['manage', 'enter']);
    let store = new Vuex.Store({
      state: {
        baseMenu: {},
        contexts: [],
        mainMenu: {},
        selectedContext: {}
      },
      getters,
      actions,
      mutations
    });

    store.dispatch('setContextMenuData', { baseMenu: baseMenu, contexts: contexts });

    expect(store.state.baseMenu['name']).to.eq('main-menu');
    expect(store.state.contexts[0]['name']).to.eq('manage');
    expect(store.state.contexts[1]['name']).to.eq('enter');
  });

  it('should generate and store context main menu', () => {
    let contexts = generateContexts(['manage', 'enter', 'judge', 'guest']);
    let store = new Vuex.Store({
      state: {
        baseMenu: baseMenu,
        contexts: contexts,
        mainMenu: {},
        selectedContext: {}
      },
      getters,
      actions,
      mutations
    });

    store.dispatch('generateContextMainMenu');
    expect(store.state.mainMenu['manage']['children'][0]['name']).to.eq('dashboard');
    expect(store.state.mainMenu['manage']['children'][0]['type']).to.eq('link');
    expect(store.state.mainMenu['manage']['children'][1]['name']).to.eq('guides-and-tours');
    expect(store.state.mainMenu['manage']['children'][5]['name']).to.eq('settings');
    expect(store.state.mainMenu['manage']['children'][5]['type']).to.eq('menu');
    expect(store.state.mainMenu['manage']['children'][5]['children'][0]['name']).to.eq('general-settings');
    expect(store.state.mainMenu['manage']['children'][5]['children'][0]['children'][0]['name']).to.eq(
      'settings-account'
    );
    expect(store.state.mainMenu['manage']['children'][5]['children'][0]['children'][0]['type']).to.eq('link');
    expect(store.state.mainMenu['manage']['children'][5]['children'][0]['children'][1]['name']).to.eq(
      'settings-languages'
    );
    expect(store.state.mainMenu['manage']['children'][5]['children'][0]['children'][1]['type']).to.eq('link');
    expect(store.state.mainMenu['enter']['children'][0]['name']).to.eq('entries');
    expect(store.state.mainMenu['enter']['children'][0]['type']).to.eq('link');
    expect(store.state.mainMenu['enter']['children'][1]['name']).to.eq('gallery-enter');
    expect(store.state.mainMenu['judge']['children'][0]['name']).to.eq('judging');
    expect(store.state.mainMenu['judge']['type']).to.eq('menu');
    expect(store.state.mainMenu['judge']['children'][1]['name']).to.eq('gallery-judge');
    expect(store.state.mainMenu['guest']['children'][0]['name']).to.eq('gallery-guest');
  });

  it('should generate selected context menu', () => {
    let contexts = generateContexts(['manage', 'enter', 'judge']);
    let store = new Vuex.Store({
      state: {
        baseMenu: baseMenu,
        contexts: contexts,
        mainMenu: {},
        selectedContext: {}
      },
      getters,
      actions,
      mutations
    });

    store.dispatch('generateContextMainMenu');

    // select the enter context menu
    store.dispatch('setSelectedContext', contexts[1]);
    let enterSelectedContextMainMenu = store.getters.selectedContextMainMenu;

    expect(enterSelectedContextMainMenu[0]['name']).to.eq('entries');
    expect(enterSelectedContextMainMenu[0]['type']).to.eq('link');
    expect(enterSelectedContextMainMenu[0]['link']).to.eq('https://test.awardforce.dev/entry/entrant');
    expect(enterSelectedContextMainMenu[1]['name']).to.eq('gallery-enter');
    expect(enterSelectedContextMainMenu[1]['type']).to.eq('link');
    expect(enterSelectedContextMainMenu[1]['link']).to.eq('https://test.awardforce.dev/gallery/entrant');

    // select the manage context menu
    store.dispatch('setSelectedContext', contexts[0]);
    let manageSelectedContextMainMenu = store.getters.selectedContextMainMenu;

    expect(manageSelectedContextMainMenu[0]['name']).to.eq('dashboard');
    expect(manageSelectedContextMainMenu[1]['name']).to.eq('guides-and-tours');
    expect(manageSelectedContextMainMenu[1]['contexts']).to.contain('manage');
    expect(manageSelectedContextMainMenu[2]['name']).to.eq('entries');
    expect(manageSelectedContextMainMenu[2]['link']).to.contain('https://test.awardforce.dev/entry/manager');
    expect(manageSelectedContextMainMenu[4]['name']).to.eq('judging');
    expect(manageSelectedContextMainMenu[4]['type']).to.eq('menu');
    expect(manageSelectedContextMainMenu[4]['children'][0]['name']).to.eq('panels');
    expect(manageSelectedContextMainMenu[4]['children'][1]['name']).to.eq('shortcut-settings');
    expect(manageSelectedContextMainMenu[4]['children'][1]['type']).to.eq('shortcut_link');
    expect(manageSelectedContextMainMenu[5]['name']).to.eq('settings');
    expect(manageSelectedContextMainMenu[5]['type']).to.eq('menu');
    expect(manageSelectedContextMainMenu[5]['children'][0]['name']).to.eq('general-settings');
    expect(manageSelectedContextMainMenu[5]['children'][0]['type']).to.eq('menu');
    expect(manageSelectedContextMainMenu[5]['children'][0]['children'][0]['name']).to.eq('settings-account');
    expect(manageSelectedContextMainMenu[5]['children'][0]['children'][1]['name']).to.eq('settings-languages');

    // select the manage context menu
    store.dispatch('setSelectedContext', contexts[2]);
    let judgeSelectedContextMainMenu = store.getters.selectedContextMainMenu;

    expect(judgeSelectedContextMainMenu[0]['name']).to.eq('judging');
    expect(judgeSelectedContextMainMenu[1]['name']).to.eq('gallery-judge');
    expect(judgeSelectedContextMainMenu[1]['contexts']).to.contains('judge');
  });
});
