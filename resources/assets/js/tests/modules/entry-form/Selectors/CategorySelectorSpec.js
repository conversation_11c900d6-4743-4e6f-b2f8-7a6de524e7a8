import CategorySelector from '@/modules/entry-form/Selectors/CategorySelector.vue';
import { expect } from 'chai';
import getters from '@/lib/store/modules/entry-form/getters';
import LockedCategoryIcon from '@/modules/entry-form/Selectors/LockedCategoryIcon.vue';
import { SelectField } from 'vue-bootstrap';
import sinon from 'sinon';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const localVue = createLocalVue();
localVue.use(Vuex);

const lang = { get: () => '' };

const wrapper = () => ({
	state: {
		chapterId: null,
		chaptersLoaded: true,
		loadedChapters: [
			{
				chapterId: 1,
				categories: [
					{ id: 1, name: '1', parentId: null, hasChildren: true },
					{ id: 11, name: '1-1', parentId: 1, hasChildren: true },
					{ id: 111, name: '1-1-1', parentId: 11, hasChildren: false },
					{ id: 112, name: '1-1-2', parentId: 11, hasChildren: false },
					{ id: 12, name: '1-2', parentId: 1, hasChildren: false },
					{ id: 2, name: '2', parentId: null, hasChildren: false },
				],
			},
			{
				chapterId: 2,
				categories: [{ id: 3, name: '3', parentId: null, hasChildren: false }],
			},
			{
				chapterId: 3,
				categories: [
					{ id: 4, name: '4', parentId: null, hasChildren: false },
					{ id: 5, name: '5', parentId: null, hasChildren: false },
				],
			},
		],
		selectedCategoryId: null,
		locks: {
			readOnly: false,
			lockedCategory: false,
		},
	},

	withChapterSelected: function (id) {
		this.state.chapterId = id;
		return this;
	},

	locked: function () {
		this.state.locks.lockedCategory = true;
		return this;
	},

	readOnly: function () {
		this.state.locks.readOnly = true;
		return this;
	},

	withCategorySelected: function (id) {
		this.state.selectedCategoryId = id;
		return this;
	},

	setChapterLoaded: function (status) {
		this.state.chaptersLoaded = status;
		return this;
	},

	entryFormApiMutations: {
		removeFromErrorBag: () => {},
	},

	setEntryFormApiMutation: function (mutation, value) {
		this.entryFormApiMutations[mutation] = value;
		return this;
	},

	mount: function () {
		let store = new Vuex.Store({
			modules: {
				entryForm: {
					namespaced: true,
					state: this.state,
					getters,
					mutations: {
						setFormEdited: () => {},
						selectCategory: () => {},
					},
					actions: {
						selectCategory: () => {},
					},
				},
				entryFormApi: {
					namespaced: true,
					state: {
						errorBag: [],
					},
					mutations: this.entryFormApiMutations,
				},
				global: {
					namespaced: true,
					mutations: {
						removeFromErrorBag: () => {},
						setFormEdited: () => {},
						selectCategory: () => {},
					},
				},
				entryFormConfiguration: {
					namespaced: true,
					state: {
						configuredCategory: null,
					},
				},
			},
		});

		return shallowMount(CategorySelector, {
			provide: { lang },
			store,
			localVue,
			stubs: {
				translation: { template: '<div class="stubbed-translation" />' },
				SelectField,
				LockedCategoryIcon,
			},
		});
	},
});

describe('CategorySelector', () => {
	const updateMyCategorySpy = sinon.spy(CategorySelector.methods, 'updateMyCategory');

	it('it renders placeholder <select> without chapter selected', () => {
		const categorySelector = wrapper().mount();
		const selects = categorySelector.findAll('select');
		expect(selects.length).to.equal(1);

		const options = selects.at(0).findAll('option');
		expect(options.length).to.equal(1);
		expect(options.at(0).element.textContent).to.match(/^\s*-\s*$/);
	});

	it('it renders <select> when chapter selected', () => {
		const categorySelector = wrapper().withChapterSelected(1).mount();
		const selects = categorySelector.findAll('select');
		expect(selects.length).to.equal(1);

		const options = selects.at(0).findAll('option');
		expect(options.length).to.equal(2);
		expect(options.at(0).element.textContent).to.match(/^\s*1\s*$/);
		expect(options.at(1).element.textContent).to.match(/^\s*2\s*$/);
	});

	it('it renders <select> relevant to chapter selected', () => {
		const categorySelector = wrapper().withChapterSelected(2).mount();
		const selects = categorySelector.findAll('select');
		expect(selects.length).to.equal(1);

		const options = selects.at(0).findAll('option');
		expect(options.length).to.equal(1);
		expect(options.at(0).element.textContent).to.match(/^\s*3\s*$/);
	});

	it("it renders multiple <select>'s relevant to chapter and category selected", () => {
		const categorySelector = wrapper().withChapterSelected(1).withCategorySelected(11).mount();
		const selects = categorySelector.findAll('select');
		expect(selects.length).to.equal(3);

		const options0 = selects.at(0).findAll('option');
		const options1 = selects.at(1).findAll('option');
		const options2 = selects.at(2).findAll('option');

		expect(options0.length).to.equal(2);
		expect(options1.length).to.equal(2);
		expect(options2.length).to.equal(2);

		expect(options0.at(0).element.textContent).to.match(/^\s*1\s*$/);
		expect(options0.at(1).element.textContent).to.match(/^\s*2\s*$/);

		expect(options1.at(0).element.textContent).to.match(/^\s*1-1\s*$/);
		expect(options1.at(1).element.textContent).to.match(/^\s*1-2\s*$/);

		expect(options2.at(0).element.textContent).to.match(/^\s*1-1-1\s*$/);
		expect(options2.at(1).element.textContent).to.match(/^\s*1-1-2\s*$/);

		expect(selects.at(0).element.value).to.equal('1');
		expect(selects.at(1).element.value).to.equal('11');
		expect(selects.at(2).element.value).to.be.empty;
	});

	it('it renders disabled <select> element when readOnly', () => {
		const categorySelector = wrapper().readOnly().mount();
		const select = categorySelector.find('select');

		expect(select.exists()).to.be.true;
		expect(select.element.disabled).to.be.true;

		const icon = categorySelector.findAll('span.af-icons-lock');
		expect(icon.length).to.equal(0);
	});

	it('it renders disabled <select> element and an icon when locked', () => {
		const categorySelector = wrapper().locked().mount();
		const select = categorySelector.findComponent('select');

		expect(select.exists()).to.be.true;
		expect(select.element.disabled).to.be.true;

		const icon = categorySelector.findAllComponents(LockedCategoryIcon);
		expect(icon.length).to.equal(1);
	});

	// TODO: Temporarily disabled for collaboration work changes.
	// it('should commit removeFromErrorBag when item is selected', async () => {
	// 	const removeFromErrorBagSpy = sinon.spy();
	//
	// 	const categorySelector = wrapper()
	// 		.withChapterSelected(3)
	// 		.setEntryFormApiMutation('removeFromErrorBag', removeFromErrorBagSpy)
	// 		.mount();
	//
	// 	const select = categorySelector.findComponent('select');
	// 	select.element.value = 5;
	// 	await select.trigger('change');
	//
	// 	sinon.assert.calledWithExactly(removeFromErrorBagSpy, sinon.match.any, {
	// 		type: 'Tab',
	// 		tab: 'Details',
	// 		baseField: 'categoryId',
	// 	});
	// });

	it('should not commit removeFromErrorBag when it is auto selected', () => {
		const removeFromErrorBagSpy = sinon.spy();

		wrapper().withChapterSelected(2).setEntryFormApiMutation('removeFromErrorBag', removeFromErrorBagSpy).mount();

		sinon.assert.notCalled(removeFromErrorBagSpy);
	});

	it('should not commit removeFromErrorBag when it is auto selected', () => {
		const selectorComponent = wrapper().mount();

		updateMyCategorySpy.resetHistory();
		selectorComponent.vm.onSelect('event-name', 7);

		sinon.assert.notCalled(updateMyCategorySpy);
	});

	it('should not commit removeFromErrorBag when it is auto selected', () => {
		const selectorComponent = wrapper().mount();

		updateMyCategorySpy.resetHistory();
		selectorComponent.vm.onSelect('auto-select', 7);

		sinon.assert.called(updateMyCategorySpy);
	});
});
