import { expect } from 'chai';
import FormBox from '@/modules/forms/list/FormBox.vue';
import FormsList from '@/modules/forms/list/FormsList.vue';
import NewFormBox from '@/modules/forms/list/NewFormBox.vue';
import { shallowMount } from '@vue/test-utils';

describe('FormsList', () => {
	it('contains all widgets', () => {
		const list = shallowMount(FormsList, {
			propsData: {
				forms: [
					{ slug: '1111', name: 'form1', type: 'entry', count: 11, cover: 'url1', round: null },
					{ slug: '1112', name: 'form2', type: 'report', count: 12, cover: 'url2', round: null },
					{ slug: '1113', name: 'form3', type: 'entry', count: 13, cover: 'url3', round: null },
				],
				canUpdate: true,
				canCreate: true,
				canDelete: true,
				language: 'en_GB',
				defaultLanguage: 'en_GB',
			},
		});

		expect(list.findAllComponents(FormBox).length).to.equal(3);
		expect(list.findAllComponents(NewFormBox).length).to.equal(0);
	});
});
