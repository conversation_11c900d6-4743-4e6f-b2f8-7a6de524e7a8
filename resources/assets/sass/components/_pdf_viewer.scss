
.pdf {
  border: 1px solid $table-border-color;
  margin: 0;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  width: 100%;
  max-width: 100%;

  .pdf__header {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid $table-border-color;
    border-radius: 4px 4px 0 0;
    padding: 2px;
    background-color: #FFF;
  }

  .pdf__container {
    overflow: auto;
    min-width: 100px;
    width: 100%;
    height: 100%;
    max-height: 65vh;
  }

  .pdf__loading {
    height: 10px;
    background-color: $colour-secondary;
    width: 30%;
  }

  .pdf__toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 4px;
    flex-wrap: wrap;
    padding: 2px;
    position: sticky;
    top: 0;
    z-index: 1;
    border-radius: 4px;

    & label {
      margin: 0;
    }

    & input {
      text-align: center;
      padding: 2px;
      width: 50px;
      height: 30px;
      border-radius: 4px;

      &:focus {
        outline-offset: 0;
        border-color: #000;
        outline: 2px solid #000;
      }
    }

    & .pdf__toolbar-button {
      background-color: transparent;
      color: $theme-var-text;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 0;
      padding: 0;
      width: 30px;
      height: 30px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;

      &:focus {
        color: $colour-link-active;
        background-color: transparent;
        outline: 2px solid $colour-link-active;
        outline-offset: 0;
      }

      &:active {
        color: $theme-var-text;
        background-color: rgba($theme-var-secondary-button, 0.2);
      }

      &:hover {
        color: #FFF;
        background-color: $colour-link-hover;
      }
    }

    .-m--t--6 {
      margin-top: -6px;
    }
  }
}
