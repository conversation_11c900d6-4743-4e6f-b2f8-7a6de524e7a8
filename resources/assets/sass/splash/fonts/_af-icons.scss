@font-face {
  font-family: 'af-icons';
  src: url('../fonts/af-icons.eot');
  src: url('../fonts/af-icons.eot?#iefix') format('eot'),
  url('../fonts/af-icons.woff') format('woff'),
  url('../fonts/af-icons.ttf') format('truetype'),
  url('../fonts/af-icons.svg#af-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}

@mixin af-icons() {
  display: inline-block;
  font-smooth: never;
  font-family: 'af-icons';
  font-variant: normal;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  user-select: none;
}

.af-icons {
  @include af-icons();
}

.af-icons-help:before {
  content: "\E021"
}

.af-icons-preview:before {
  content: "\E033"
}

.af-icons-read-off:before {
  content: "\E057"
}

.af-icons-chevron-down:before {
  content: "\E00D";
}

.af-icons-chevron-up:before {
  content: "\E00E";
}

.af-icons-info:before {
  content: "\E049";
}

