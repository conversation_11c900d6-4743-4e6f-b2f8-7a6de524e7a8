<?php

use AwardForce\Modules\Accounts\Models\Account;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCookieClassificationToAccount extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            $values = ['necessary', 'analytics', 'marketing'];
            $table->enum('footer_uses_cookies', $values)->nullable()->after('tracking_codes');
            $table->enum('header_uses_cookies', $values)->nullable()->after('tracking_codes');
        });

        Account::whereNotNull('tracking_codes')
            ->where('tracking_codes', '<>', '')
            ->where('tracking_codes', '<>', '[]')
            ->each(function (Account $account) {
                $account->update([
                    'headerUsesCookies' => 'marketing',
                    'footerUsesCookies' => 'marketing',
                ]);
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            $table->dropColumn('footer_uses_cookies');
            $table->dropColumn('header_uses_cookies');
        });
    }
}
