<?php

use Illuminate\Database\Migrations\Migration;

class FixTypoInUiTranslations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        \DB::update("UPDATE `translations` set `field`='judging.index.judging_progress.picks_per_category.label' WHERE `field`='judging.index.jugding_progress.picks_per_category.label' and account_id > 0 and resource = 'ui' and foreign_id = 0");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        \DB::update("UPDATE `translations` set `field`='judging.index.jugding_progress.picks_per_category.label' WHERE `field`='judging.index.judging_progress.picks_per_category.label' and account_id > 0 and resource = 'ui' and foreign_id = 0");
    }
}
