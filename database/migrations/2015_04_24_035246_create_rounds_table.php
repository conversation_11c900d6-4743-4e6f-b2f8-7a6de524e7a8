<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateRoundsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('rounds', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->bigInteger('account_id')->unsigned()->index();
            $table->string('round_type')->index();
            $table->boolean('enabled')->index()->default(true);
            $table->datetime('starts_at')->nullable()->index();
            $table->string('starts_tz');
            $table->datetime('ends_at')->nullable()->index();
            $table->string('ends_tz');
            $table->string('judging_view')->index();
            $table->string('feedback_role')->index();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::drop('rounds');
    }
}
