<?php

use AwardForce\Modules\Features\Data\Feature;
use Illuminate\Database\Migrations\Migration;
use Platform\Features\Feature as PlatformFeature;

class AddGrantsFeature extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $exists = Feature::where('feature', 'grants')->first();

        if (! $exists) {
            Feature::add(null, new PlatformFeature('grants', Feature::ENABLED));
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Feature::where('feature', 'grants')->delete();
    }
}
