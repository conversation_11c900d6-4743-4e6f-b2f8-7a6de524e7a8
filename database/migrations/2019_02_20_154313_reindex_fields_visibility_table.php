<?php

use Illuminate\Database\Migrations\Migration;

class ReindexFieldsVisibilityTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::table('field_score_set')->truncate();

        DB::statement("
            INSERT INTO field_score_set (field_id, score_set_id)
            SELECT DISTINCT fields.id AS 'field_id', score_sets.id AS 'score_set_id'
            FROM score_sets
            INNER JOIN `fields` ON fields.season_id = score_sets.season_id AND fields.visibility LIKE CONCAT('%',score_sets.mode, '%')
            ON DUPLICATE KEY UPDATE field_id = field_id;
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        DB::table('field_score_set')->truncate();
    }
}
