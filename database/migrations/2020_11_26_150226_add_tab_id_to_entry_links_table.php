<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTabIdToEntryLinksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Implicit commit, cannot be part of another transaction. See https://dev.mysql.com/doc/refman/8.0/en/implicit-commit.html
        Schema::table('entry_links', function (Blueprint $table) {
            $table->unsignedInteger('tab_id')->nullable()->after('entry_id');
        });

        DB::transaction(function () {
            DB::update(
                "UPDATE entry_links
                JOIN entries ON entries.id = entry_links.entry_id
                LEFT JOIN (
                     SELECT tabs.season_id, MIN(tabs.id) AS tab_id
                     FROM tabs
                     WHERE tabs.type = 'Attachments' AND tabs.deleted_at IS NULL
                     GROUP BY tabs.season_id
                ) AS attachments_tab ON attachments_tab.season_id = entries.season_id
                LEFT JOIN (
                     SELECT tabs.season_id, MIN(tabs.id) AS tab_id
                     FROM tabs
                     WHERE tabs.type = 'Attachments' AND tabs.deleted_at IS NOT NULL
                     GROUP BY tabs.season_id
                ) AS deleted_attachments_tab ON deleted_attachments_tab.season_id = entries.season_id
                SET entry_links.tab_id = IF(attachments_tab.tab_id IS NOT NULL, attachments_tab.tab_id, deleted_attachments_tab.tab_id)"
            );
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('entry_links', function (Blueprint $table) {
            $table->dropColumn('tab_id');
        });
    }
}
