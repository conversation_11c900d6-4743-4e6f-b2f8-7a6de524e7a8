<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class DropPddIdColumnFromAccounts extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('accounts', function (Blueprint $table) {
            DB::statement('ALTER TABLE accounts CHANGE hsd_id deal_id varchar(255) NULL DEFAULT NULL;');
            $table->dropColumn('pdd_id');
            $table->dropColumn('pdd_deal');
            $table->dropColumn('pdd_deal_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('accounts', function (Blueprint $table) {
            DB::statement('ALTER TABLE accounts CHANGE deal_id hsd_id varchar(255) NULL DEFAULT NULL;');
            $table->string('pdd_id')->nullable()->after('subscription_customer_id');
            $table->datetime('pdd_deal_updated_at')->nullable()->after('pdd_id');
            $table->json('pdd_deal')->nullable()->after('pdd_id');
        });
    }
}
