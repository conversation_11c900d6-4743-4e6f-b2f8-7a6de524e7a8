<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AddUniqueTokenConstraintToFiles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::beginTransaction();

        $this->resolveDuplicateTokens();
        $this->generateMissingTokens();

        DB::commit();

        // Implicit commit, cannot be part of another transaction. See https://dev.mysql.com/doc/refman/8.0/en/implicit-commit.html
        Schema::table('files', function (Blueprint $table) {
            $table->unique('token', 'files_token_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('files', function (Blueprint $table) {
            $table->dropUnique('files_token_unique');
        });
    }

    private function resolveDuplicateTokens()
    {
        $this->getDuplicateTokens(['Entries', 'Categories', 'Chapters'])
            ->each(function ($token) {
                $files = $this->getFilesByToken($token->token);
                $files->shift();

                $tokens = $this->generateTokens($files->count());

                foreach ($files as $file) {
                    DB::table('files')
                        ->where('id', $file->id)
                        ->update(['token' => array_shift($tokens)]);
                }
            });
    }

    private function getDuplicateTokens($resource): Collection
    {
        return DB::table('files')
            ->select('token')
            ->whereIn('resource', (array) $resource)
            ->whereNotNull('token')
            ->groupBy('token')
            ->havingRaw('COUNT(*) > 1')
            ->get();
    }

    private function generateMissingTokens()
    {
        $this->getFilesWithoutTokens()
            ->chunk(50)
            ->each(function (Collection $files) {
                $tokens = $this->generateTokens($files->count());

                foreach ($files as $file) {
                    DB::table('files')
                        ->where('id', $file->id)
                        ->update(['token' => array_shift($tokens)]);
                }
            });
    }

    private function getFilesByToken(string $token): Collection
    {
        return DB::table('files')
            ->where('token', $token)
            ->orderBy('created_at')
            ->get();
    }

    private function getFilesWithoutTokens(): Collection
    {
        return DB::table('files')
            ->whereNull('token')
            ->get();
    }

    private function generateTokens(int $count): array
    {
        $tokens = [];

        do {
            for ($i = 1; $i <= $count; $i++) {
                $tokens[] = str_random();
            }
        } while (DB::table('files')->whereIn('token', $tokens)->count());

        return $tokens;
    }
}
