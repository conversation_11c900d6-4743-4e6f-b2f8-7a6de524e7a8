<?php

use Illuminate\Database\Migrations\Migration;

class AddUniqueKeyToFieldValues extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $sql = 'SELECT group_concat(id) as ids FROM field_values GROUP BY field_id, foreign_id HAVING count(id) > 1;';
        $results = DB::select($sql);

        array_walk($results, function ($duplicates) {
            $ids = explode(',', $duplicates->ids);

            DB::table('field_values')
                ->whereIn('id', $ids)
                ->orderBy('updated_at', 'ASC')
                ->limit(count($ids) - 1)
                ->delete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
