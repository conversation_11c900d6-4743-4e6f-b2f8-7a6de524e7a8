<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class AddDatesToScoreSets extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('score_sets', function (Blueprint $table) {
            $table->datetime('gallery_starts_at')->nullable()->after('top_pick_winners');
            $table->string('gallery_starts_tz')->nullable()->after('gallery_starts_at');
            $table->datetime('gallery_ends_at')->nullable()->after('gallery_starts_tz');
            $table->string('gallery_ends_tz')->nullable()->after('gallery_ends_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('score_sets', function (Blueprint $table) {
            $table->dropColumn('gallery_starts_at');
            $table->dropColumn('gallery_starts_tz');
            $table->dropColumn('gallery_ends_at');
            $table->dropColumn('gallery_ends_tz');
        });
    }
}
