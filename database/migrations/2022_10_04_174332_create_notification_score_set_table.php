<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNotificationScoreSetTable extends Migration
{
    public function up()
    {
        Schema::create('notification_score_set', function (Blueprint $table) {
            $table->unsignedInteger('notification_id')->index();
            $table->foreign('notification_id')->references('id')->on('notifications')->onDelete('cascade');
            $table->unsignedBigInteger('score_set_id')->index();
            $table->foreign('score_set_id')->references('id')->on('score_sets')->onDelete('cascade');
            $table->primary(['notification_id', 'score_set_id']);
        });
    }

    public function down()
    {
        Schema::dropIfExists('notification_score_set');
    }
}
