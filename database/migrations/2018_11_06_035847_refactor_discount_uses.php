<?php

use AwardForce\Modules\Payments\Models\Discount;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RefactorDiscountUses extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->integer('maximum_uses')->nullable()->after('use');
            $table->integer('maximum_uses_per_user')->nullable()->after('use');
        });

        Discount::where('use', '=', 'once-per-entrant')->update(['maximum_uses_per_user' => 1]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->dropColumn('maximum_uses');
            $table->dropColumn('maximum_uses_per_user');
        });
    }
}
