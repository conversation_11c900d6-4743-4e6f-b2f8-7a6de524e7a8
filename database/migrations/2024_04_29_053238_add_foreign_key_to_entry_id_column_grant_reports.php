<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('grant_reports')
            ->select('grant_reports.id')
            ->leftJoin('entries', 'grant_reports.entry_id', '=', 'entries.id')
            ->whereNull('entries.id')
            ->delete();

        Schema::table('grant_reports', function (Blueprint $table) {
            $table->foreign('entry_id')->references('id')->on('entries')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('grant_reports', function (Blueprint $table) {
            $table->dropForeign(['entry_id']);
        });
    }
};
